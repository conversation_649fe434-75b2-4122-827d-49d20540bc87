// This file configures the initialization of Sentry for edge runtime
import * as Sen<PERSON> from "@sentry/nextjs";

// Only initialize if <PERSON><PERSON> is configured
if (process.env.NEXT_PUBLIC_SENTRY_DSN && process.env.NEXT_PUBLIC_SENTRY_DSN !== 'your_sentry_dsn_here') {
  Sentry.init({
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
    environment: process.env.NEXT_PUBLIC_SENTRY_ENVIRONMENT || "development",
    
    // Performance monitoring for edge runtime - reduced to avoid rate limits
    tracesSampleRate: process.env.NODE_ENV === "production" ? 0.1 : 0.2,
    
    // Edge runtime specific configuration
    integrations: [
      // Add edge-specific integrations here if needed
    ],
    
    // Custom error filtering for edge runtime
    beforeSend(event) {
      // Filter out development-only errors
      if (process.env.NODE_ENV === "development") {
        // Allow all errors in development
        return event;
      }
      
      return event;
    },
    
    // Set initial scope
    initialScope: {
      tags: {
        component: "nextjs-edge",
      },
    },
  });
} else {
  console.warn("Sentry DSN not configured. Edge runtime error monitoring disabled.");
}