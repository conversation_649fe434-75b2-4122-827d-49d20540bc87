# J&A Business Solutions - Production Environment Configuration
# Copy this file to .env.production and update with actual production values

# Application Environment
NODE_ENV=production
PORT=3000
HOSTNAME=0.0.0.0

# Next.js Configuration
NEXT_TELEMETRY_DISABLED=1

# Supabase Configuration (Production)
NEXT_PUBLIC_SUPABASE_URL=https://your-production-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-production-anon-key

# Sentry Configuration (Production)
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id
NEXT_PUBLIC_SENTRY_ENVIRONMENT=production
SENTRY_ORG=your-sentry-org
SENTRY_PROJECT=your-sentry-project
SENTRY_AUTH_TOKEN=your-sentry-auth-token

# PostHog Configuration (Production)
NEXT_PUBLIC_POSTHOG_API_KEY=phc_your-production-api-key
NEXT_PUBLIC_POSTHOG_API_HOST=https://us.i.posthog.com

# Security Configuration
# Add any additional security-related environment variables here