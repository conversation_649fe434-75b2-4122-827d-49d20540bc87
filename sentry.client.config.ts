// This file configures the initialization of Sentry on the browser/client side
import * as Sen<PERSON> from "@sentry/nextjs";
import { shouldAllowError, shouldAllowTransaction } from "./src/lib/sentry-rate-limiter";

// Only initialize if <PERSON><PERSON> is configured
if (process.env.NEXT_PUBLIC_SENTRY_DSN && process.env.NEXT_PUBLIC_SENTRY_DSN !== 'your_sentry_dsn_here') {
  Sentry.init({
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
    environment: process.env.NEXT_PUBLIC_SENTRY_ENVIRONMENT || "development",
    
    // Reduced sample rates to avoid 429 rate limit errors
    tracesSampleRate: process.env.NODE_ENV === "production" ? 0.01 : 0.1,
    profilesSampleRate: process.env.NODE_ENV === "production" ? 0.01 : 0.05,

    // Session replay with very conservative rate limiting
    replaysSessionSampleRate: process.env.NODE_ENV === "production" ? 0.01 : 0.05,
    replaysOnErrorSampleRate: process.env.NODE_ENV === "production" ? 0.1 : 0.2,
    
    integrations: [
      Sentry.browserTracingIntegration({
        // Enhanced browser tracing options
        enableLongTask: true,
        enableInp: true,
      }),
      Sentry.replayIntegration({
        maskAllText: false,
        blockAllMedia: false,
        // Enhanced replay configuration
        maskAllInputs: true,
        // Capture console logs and network requests
        networkDetailAllowUrls: [window.location.origin],
        networkCaptureBodies: true,
        networkRequestHeaders: ['User-Agent', 'Accept', 'Content-Type'],
        networkResponseHeaders: ['Content-Type', 'Content-Length'],
      }),
      // Web Vitals integration
      Sentry.browserProfilingIntegration(),
    ],
    
    // Enhanced error filtering with rate limiting
    beforeSend(event) {
      // Apply rate limiting first
      if (!shouldAllowError()) {
        return null;
      }
      
      // Filter out development-only errors
      if (process.env.NODE_ENV === "development") {
        // In development, randomly drop some errors to avoid rate limits
        if (Math.random() > 0.7) {
          return null;
        }
        return event;
      }
      
      // Filter out common non-critical errors in production
      const errorMessage = event.exception?.values?.[0]?.value || '';
      
      // Browser-specific non-critical errors
      const ignoredErrors = [
        "ResizeObserver loop limit exceeded",
        "Non-Error promise rejection captured",
        "Script error",
        "Network request failed",
        "Loading chunk",
        "ChunkLoadError",
        // Add more patterns as needed
      ];
      
      if (ignoredErrors.some(pattern => errorMessage.includes(pattern))) {
        return null;
      }
      
      // Filter out errors from browser extensions
      if (event.exception?.values?.[0]?.stacktrace?.frames?.some(frame => 
        frame.filename?.includes('extension://') || 
        frame.filename?.includes('moz-extension://')
      )) {
        return null;
      }
      
      return event;
    },
    

    
    // Set initial scope with enhanced context
    initialScope: {
      tags: {
        component: "nextjs-client",
        runtime: "browser",
      },
      contexts: {
        browser: {
          name: navigator.userAgent,
          version: navigator.appVersion,
        },
        device: {
          screen_resolution: `${screen.width}x${screen.height}`,
          orientation: screen.orientation?.type?.includes('landscape') ? 'landscape' : 'portrait',
        },
      },
    },
    
    // Enhanced transport options with rate limiting
    transport: Sentry.makeBrowserOfflineTransport(Sentry.makeFetchTransport),
    
    // Add rate limiting to prevent 429 errors
    maxBreadcrumbs: process.env.NODE_ENV === "production" ? 50 : 25,
    
    // Throttle events to prevent rate limiting
    beforeSendTransaction(event) {
      // Apply rate limiting first
      if (!shouldAllowTransaction()) {
        return null;
      }
      
      // Filter out very short transactions (likely not meaningful)
      if (event.start_timestamp && event.timestamp) {
        const duration = event.timestamp - event.start_timestamp;
        if (duration < 0.001) { // Less than 1ms
          return null;
        }
      }
      
      // In development, randomly drop some transactions to avoid rate limits
      if (process.env.NODE_ENV === "development" && Math.random() > 0.5) {
        return null;
      }
      
      return event;
    },
    
    // Custom release and dist
    release: process.env.NEXT_PUBLIC_APP_VERSION || 'unknown',
    dist: process.env.NODE_ENV,
  });

  // Initialize performance monitoring and test utilities after Sentry setup
  if (typeof window !== 'undefined') {
    // Import and initialize performance monitoring
    import('./src/lib/performance').then(({ initPerformanceMonitoring }) => {
      initPerformanceMonitoring();
    }).catch(console.warn);
    
    // Load test utilities in development
    if (process.env.NODE_ENV === 'development') {
      import('./src/lib/sentry-test').catch(console.warn);
    }
  }
} else {
  console.warn("Sentry DSN not configured. Client-side error monitoring disabled.");
}