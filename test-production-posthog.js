// Quick Production PostHog Test
// Run this in the browser console on https://jnabusinesssolutions.com

console.log('🚀 Testing PostHog v0.23 with Bypass System...');

// Wait a moment for the page to fully load
setTimeout(() => {
  console.log('📊 Checking PostHog status...');
  
  // Check if regular PostHog loaded
  if (window.posthog && window.posthog.__loaded) {
    console.log('✅ Regular PostHog loaded successfully');
    
    // Send test event
    window.posthog.capture('production_test_v023', {
      test_type: 'regular_posthog',
      version: 'v0.23',
      timestamp: new Date().toISOString(),
      page_url: window.location.href
    });
    
    console.log('📤 Test event sent via regular PostHog');
  } else {
    console.log('⚠️ Regular PostHog not loaded (likely blocked by ad blocker)');
    console.log('🔧 This is expected - bypass system should be active');
  }
  
  // Check if analytics functions are available
  if (typeof window.trackEvent === 'function') {
    console.log('✅ Analytics functions available');
    
    // Test the trackEvent function
    try {
      window.trackEvent('cta_click', {
        cta_type: 'production_test_v023',
        location: 'console_test',
        text: 'Bypass System Test',
        version: 'v0.23'
      });
      console.log('📤 Test event sent via trackEvent function');
    } catch (error) {
      console.error('❌ Error sending test event:', error);
    }
  } else {
    console.log('⚠️ Analytics functions not available in global scope');
  }
  
  // Final status
  console.log('\n🎯 Test Summary:');
  console.log('- Version: v0.23 with PostHog bypass system');
  console.log('- Check PostHog dashboard: https://us.posthog.com');
  console.log('- Look for events: production_test_v023');
  console.log('- Events should appear within 1-2 minutes');
  
}, 3000);

console.log('⏱️ Test will run in 3 seconds...');