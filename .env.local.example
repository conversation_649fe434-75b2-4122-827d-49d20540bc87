# =============================================================================
# J&A Business Solutions - Local Environment Configuration
# =============================================================================
# Copy this file to .env.local and fill in your actual values
# This file contains the actual DSN for the proptech project

# =============================================================================
# Supabase Configuration
# =============================================================================
# proptech project configuration
NEXT_PUBLIC_SUPABASE_URL=https://bdzvmmnbtwsfvrqepjuu.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJkenZtbW5idHdzZnZycWVwanV1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NDM1MDAsImV4cCI6MjA2ODQxOTUwMH0.j5Fanbr-kiqPdsCmJf736qP2ZT5LZQwo0p3-fmCyUbc

# =============================================================================
# Sentry Configuration (Error Monitoring)
# =============================================================================
# Your actual Sentry DSN for the proptech project
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/4509747857260544
NEXT_PUBLIC_SENTRY_ENVIRONMENT=development

# Sentry build configuration (for source maps upload)
SENTRY_ORG=atemkeng
SENTRY_PROJECT=proptech
SENTRY_AUTH_TOKEN=your_sentry_auth_token_here

# =============================================================================
# PostHog Configuration (Analytics)
# =============================================================================
NEXT_PUBLIC_POSTHOG_API_KEY=your_posthog_api_key
NEXT_PUBLIC_POSTHOG_API_HOST=https://app.posthog.com

# =============================================================================
# Development Configuration
# =============================================================================
NODE_ENV=development