# Project Structure

## Root Directory
```
├── src/                    # Source code
├── public/                 # Static assets and PWA files
├── .kiro/                  # Kiro AI assistant configuration
├── node_modules/           # Dependencies
└── dist/                   # Build output (generated)
```

## Source Code Organization (`src/`)
```
src/
├── components/             # React components
│   ├── ui/                # Reusable UI components
│   └── *.tsx              # Page/section components
├── App.tsx                # Main application component
├── main.tsx               # Application entry point
├── index.css              # Global styles
├── ScrollContext.tsx      # Shared scroll state context
└── vite-env.d.ts         # Vite type definitions
```

## Component Architecture
- **Single-page application** with section-based components
- **Context-based state management** for scroll behavior
- **Functional components** with React hooks
- **TypeScript** for all component files

## Key Components
- `Header.tsx` - Navigation with scroll-responsive styling
- `Hero.tsx` - Landing section
- `Services.tsx` - Service offerings display
- `PropertyOwners.tsx` - Target audience section
- `Contact.tsx` - Contact information and forms
- `PWAInstallPrompt.tsx` - Progressive web app installation

## Static Assets (`public/`)
```
public/
├── icons/                 # App icons and favicons
├── screenshots/           # App screenshots for PWA
├── manifest.json          # PWA manifest
├── service-worker.js      # Service worker for offline functionality
└── offline.html           # Offline fallback page
```

## Styling Conventions
- **Tailwind CSS** utility classes
- **Custom color palette** (navy and gold theme)
- **Responsive design** with mobile-first approach
- **Component-scoped styling** using Tailwind classes

## File Naming
- **PascalCase** for React components (`Header.tsx`)
- **camelCase** for utility files (`vite.config.ts`)
- **kebab-case** for static assets and configuration files