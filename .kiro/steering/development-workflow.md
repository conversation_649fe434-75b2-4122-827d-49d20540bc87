# Development Workflow Rules

## Language Standards

### TypeScript First
- **TypeScript is the default language** for all new code unless explicitly specified otherwise
- All new files MUST use `.ts` or `.tsx` extensions
- JavaScript files (`.js`, `.jsx`) are only allowed for legacy code or specific configuration files
- All components, utilities, and business logic MUST be written in TypeScript
- Proper TypeScript types MUST be defined for all interfaces, props, and function parameters

## Branching Strategy

### Feature Branch Workflow
- **Every new feature MUST be developed in a separate branch**
- Branch naming convention: `feature/descriptive-name` or `feat/descriptive-name`
- No direct commits to `main` branch
- All changes must go through Pull Request review process

### Branch Creation Rules
- Create new branch from latest `main`: `git checkout -b feature/feature-name`
- Branch names should be descriptive and kebab-case
- Examples: `feature/contact-form`, `feature/supabase-integration`, `feature/analytics-setup`

## Commit and Push Requirements

### After Each Task
- **Code MUST be committed after completing each task**
- **Commits MUST be pushed to remote repository immediately**
- Commit messages should follow conventional commit format: `feat: add contact form validation`
- Each task completion should result in at least one commit

### Commit Message Format
```
type(scope): description

Examples:
feat(auth): add user authentication with Supabase
fix(ui): resolve mobile navigation menu issue
docs(readme): update setup instructions
test(e2e): add contact form submission tests
```

## MCP (Model Context Protocol) Priority

### MCP First Development
- **MCPs are first-class citizens and take priority during development**
- Before setting up any tool or service, MUST first check if an MCP exists
- Use MCP tools when available instead of manual setup or alternative tools
- MCPs should be configured and tested before proceeding with manual alternatives

### MCP Integration Workflow
1. Check available MCPs for the required functionality
2. Configure and test MCP integration
3. Use MCP tools for development tasks
4. Only use alternative tools if no suitable MCP exists
5. Document MCP usage in development notes

### MCP Configuration Priority
- Workspace-level MCP configuration takes precedence
- User-level MCP configuration as fallback
- Auto-approve commonly used MCP tools in configuration
- Regularly update MCP server configurations

## Code Quality Standards

### TypeScript Configuration
- Strict TypeScript configuration MUST be maintained
- No `any` types allowed without explicit justification
- All props and function parameters MUST have proper types
- Use TypeScript utility types when appropriate

### Component Standards
- All React components MUST be functional components with TypeScript
- Use proper TypeScript interfaces for component props
- Implement proper error boundaries and error handling
- Follow React best practices and hooks rules

## Testing Requirements

### Test Coverage
- All new features MUST include appropriate tests
- Unit tests for utility functions and business logic
- Component tests for React components
- E2E tests for critical user journeys
- Tests MUST be written in TypeScript

### Testing Workflow
- Write tests alongside feature development
- Run tests before committing code
- Ensure all tests pass in CI/CD pipeline
- Update tests when modifying existing functionality