# Technology Stack

## Build System & Framework
- **Next.js 14+** - React framework with App Router, SSR, and SSG capabilities
- **React 18** - Frontend framework with TypeScript
- **TypeScript** - Primary development language (default for all new code)

## Database & Backend Services
- **Supabase** - PostgreSQL database with real-time capabilities, authentication, and storage
- **Supabase Client** - JavaScript client for database operations and auth

## Monitoring & Analytics
- **Sentry** - Error monitoring, performance tracking, and application monitoring
- **PostHog** - Product analytics, feature flags, and user behavior tracking

## Styling & UI
- **shadcn/ui** - Modern component library built on Radix UI primitives
- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Unstyled, accessible UI primitives (via shadcn/ui)
- **Custom Design System** - Navy blue and gold color palette
- **Lucide React** - Icon library for consistent iconography

## PWA & Performance
- **Next.js PWA** - Progressive Web App capabilities with Next.js
- **Service Worker** - Offline functionality and caching
- **Next.js Image Optimization** - Automatic image optimization and lazy loading
- **Next.js App Router** - Modern routing with layouts and streaming

## Testing & Quality Assurance
- **Playwright** - End-to-end testing across multiple browsers
- **Jest** - Unit testing framework
- **React Testing Library** - Component testing utilities
- **ESLint** - Code linting with TypeScript support
- **TypeScript ESLint** - TypeScript-specific linting rules
- **Prettier** - Code formatting

## CI/CD & Deployment
- **GitHub Actions** - Continuous integration and deployment pipeline
- **Automated Testing** - Unit tests, E2E tests, and linting in CI
- **Multi-Environment Deployment** - Development, staging, and production environments
- **Vercel** - Recommended deployment platform for Next.js applications

## MCP (Model Context Protocol) Integration
- **MCP First Priority** - Always check for MCP tools before using alternatives
- **Supabase MCP** - Database operations and schema management
- **GitHub MCP** - Repository management and CI/CD operations
- **File System MCP** - File operations and project structure management
- **Testing MCP** - Automated testing and quality assurance tools

## Common Commands

### Development
```bash
npm run dev          # Start Next.js development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run format       # Format code with Prettier
```

### Testing
```bash
npm run test         # Run unit tests
npm run test:e2e     # Run Playwright end-to-end tests
npm run test:e2e:ui  # Run E2E tests with UI mode
npm run test:headed  # Run E2E tests in headed mode
```

### Database & Services
```bash
npx supabase start   # Start local Supabase instance
npx supabase stop    # Stop local Supabase instance
npx supabase status  # Check Supabase service status
npx supabase db reset # Reset local database
```

### Key Configuration Files
- `next.config.js` - Next.js configuration with PWA setup
- `tailwind.config.ts` - Custom colors and design tokens (TypeScript)
- `tsconfig.json` - TypeScript configuration
- `eslint.config.js` - ESLint rules and plugins
- `prettier.config.js` - Prettier formatting configuration
- `components.json` - shadcn/ui component configuration
- `playwright.config.ts` - Playwright testing configuration
- `jest.config.js` - Jest testing configuration
- `supabase/config.toml` - Supabase local development configuration
- `.github/workflows/` - GitHub Actions CI/CD pipeline configuration

### Environment Configuration
- `.env.local` - Local development environment variables
- `.env.example` - Template for required environment variables
- Environment variables for Supabase, Sentry, and PostHog configuration

## Performance Optimizations
- Manual chunk splitting for lucide-react
- Optimized dependencies configuration
- Runtime caching for Google Fonts and external images
- Source maps disabled in production
- Sentry performance monitoring for Core Web Vitals
- PostHog analytics with privacy-first configuration

## Development Setup Requirements

### Required Environment Variables
```bash
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Sentry Configuration
VITE_SENTRY_DSN=your_sentry_dsn
VITE_SENTRY_ENVIRONMENT=development

# PostHog Configuration
VITE_POSTHOG_API_KEY=your_posthog_api_key
VITE_POSTHOG_API_HOST=https://app.posthog.com
```

### Initial Setup Steps
1. **Check MCP availability** for all required tools before manual setup
2. Clone repository and install dependencies: `npm install`
3. Initialize shadcn/ui: `npx shadcn-ui@latest init`
4. Copy `.env.example` to `.env.local` and configure environment variables
5. Set up Supabase project (use Supabase MCP if available)
6. Configure Sentry project for error monitoring
7. Set up PostHog project for analytics
8. Install Playwright browsers: `npx playwright install`
9. Start development server: `npm run dev`

### MCP Configuration Priority
- Configure workspace-level MCP servers in `.kiro/settings/mcp.json`
- Use MCP tools for database operations, file management, and testing
- Auto-approve commonly used MCP tools in configuration
- Check MCP availability before installing alternative tools

### Testing Setup
- Playwright tests run against local development server
- E2E tests cover critical user journeys and responsive design
- CI/CD pipeline runs tests automatically on pull requests
- Production monitoring through Sentry and PostHog dashboards