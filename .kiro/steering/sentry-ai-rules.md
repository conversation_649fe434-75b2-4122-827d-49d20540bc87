# Sentry AI Rules for J&A Business Solutions

These AI rules provide comprehensive guidance for implementing Sentry error monitoring, performance tracking, and logging in the J&A Business Solutions Next.js application. Follow these patterns to ensure consistent, effective error monitoring across the codebase.

## Exception Catching

Use `Sentry.captureException(error)` to capture an exception and log the error in Sentry. Use this in try-catch blocks or areas where exceptions are expected.

### Basic Exception Handling

```typescript
import * as Sen<PERSON> from "@sentry/nextjs";

try {
  // Risky operation
  await performBookingOperation();
} catch (error) {
  // Always capture exceptions with context
  Sentry.captureException(error, {
    tags: {
      operation: 'booking_creation',
      component: 'BookingService'
    },
    contexts: {
      booking: {
        propertyId: bookingData.propertyId,
        checkIn: bookingData.checkIn,
        checkOut: bookingData.checkOut,
        guestCount: bookingData.guestCount
      }
    }
  });
  throw error; // Re-throw for proper error handling
}
```

### Business Logic Error Handling

For business-specific errors, use the enhanced error reporting utilities:

```typescript
import { reportBusinessError, reportError } from "@/lib/sentry";

// For business logic errors
try {
  await validateBookingAvailability(propertyId, dates);
} catch (error) {
  reportBusinessError({
    code: 'BOOKING_CONFLICT',
    message: 'Property not available for selected dates',
    severity: 'high',
    category: 'business',
    context: {
      propertyId,
      requestedDates: dates,
      conflictingBookings: existingBookings
    }
  }, {
    user: { id: userId, email: userEmail },
    page: { url: '/booking', section: 'availability-check' },
    business: {
      operation: 'availability_check',
      entityType: 'booking',
      entityId: propertyId
    }
  });
  throw error;
}
```

### Async Operation Error Handling

```typescript
// For API calls and async operations
async function syncCalendarWithHospitable(propertyId: string) {
  try {
    const response = await fetch(hospitableApiUrl);
    if (!response.ok) {
      throw new Error(`Hospitable API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    Sentry.captureException(error, {
      tags: {
        operation: 'calendar_sync',
        api: 'hospitable',
        propertyId
      },
      contexts: {
        api: {
          endpoint: hospitableApiUrl,
          method: 'GET',
          statusCode: response?.status,
          propertyId
        }
      }
    });
    throw error;
  }
}
```

## Performance Monitoring and Tracing

Spans should be created for meaningful actions within applications like button clicks, API calls, and function calls. Use the `Sentry.startSpan` function to create a span. Child spans can exist within a parent span.

### Custom Span Instrumentation in Component Actions

The `name` and `op` properties should be meaningful for the activities in the call. Attach attributes based on relevant information and metrics from the request.

```typescript
import * as Sentry from "@sentry/nextjs";

function BookingComponent() {
  const handleBookingSubmit = async (bookingData: BookingFormData) => {
    // Create a transaction/span to measure performance
    return Sentry.startSpan(
      {
        op: "ui.booking.submit",
        name: "Booking Form Submission",
        attributes: {
          propertyId: bookingData.propertyId,
          guestCount: bookingData.guestCount,
          totalPrice: bookingData.totalPrice,
          checkInDate: bookingData.checkIn,
          checkOutDate: bookingData.checkOut
        }
      },
      async (span) => {
        try {
          // Add business context to span
          span?.setTag('business.operation', 'booking_creation');
          span?.setTag('business.entityType', 'booking');
          
          const booking = await createBooking(bookingData);
          
          // Add success metrics
          span?.setTag('booking.status', 'success');
          span?.setAttribute('booking.id', booking.id);
          
          return booking;
        } catch (error) {
          // Mark span as error and capture exception
          span?.setTag('booking.status', 'error');
          span?.recordException(error as Error);
          
          Sentry.captureException(error);
          throw error;
        }
      }
    );
  };

  return (
    <button type="button" onClick={() => handleBookingSubmit(formData)}>
      Submit Booking
    </button>
  );
}
```

### Custom Span Instrumentation in API Calls

The `name` and `op` properties should be meaningful for the activities in the call. Attach attributes based on relevant information and metrics from the request.

```typescript
async function fetchPropertyAvailability(propertyId: string, dateRange: DateRange) {
  return Sentry.startSpan(
    {
      op: "http.client.availability",
      name: `GET /api/properties/${propertyId}/availability`,
      attributes: {
        propertyId,
        checkIn: dateRange.checkIn,
        checkOut: dateRange.checkOut,
        apiEndpoint: `/api/properties/${propertyId}/availability`
      }
    },
    async (span) => {
      const startTime = performance.now();
      
      try {
        const response = await fetch(`/api/properties/${propertyId}/availability`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });
        
        const endTime = performance.now();
        const responseTime = endTime - startTime;
        
        // Add response metrics
        span?.setAttribute('http.status_code', response.status);
        span?.setAttribute('http.response_time_ms', responseTime);
        span?.setTag('api.success', response.ok);
        
        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Add business metrics
        span?.setAttribute('availability.conflicts', data.conflicts?.length || 0);
        span?.setAttribute('availability.available_days', data.availableDays || 0);
        
        return data;
      } catch (error) {
        const endTime = performance.now();
        span?.setAttribute('http.response_time_ms', endTime - startTime);
        span?.setTag('api.success', false);
        span?.recordException(error as Error);
        throw error;
      }
    }
  );
}
```

### Database Operation Monitoring

```typescript
async function createBookingRecord(bookingData: BookingData) {
  return Sentry.startSpan(
    {
      op: "db.supabase.insert",
      name: "Insert Booking Record",
      attributes: {
        table: 'bookings',
        propertyId: bookingData.propertyId,
        operation: 'insert'
      }
    },
    async (span) => {
      try {
        const { data, error } = await supabase
          .from('bookings')
          .insert(bookingData)
          .select()
          .single();
        
        if (error) {
          span?.setTag('db.error', true);
          span?.setAttribute('db.error_code', error.code);
          span?.setAttribute('db.error_message', error.message);
          throw error;
        }
        
        span?.setTag('db.success', true);
        span?.setAttribute('db.record_id', data.id);
        
        return data;
      } catch (error) {
        span?.recordException(error as Error);
        throw error;
      }
    }
  );
}
```

## Logging Integration

Where logs are used, ensure Sentry is imported using `import * as Sentry from "@sentry/nextjs"`. Enable logging in Sentry using `Sentry.init({ _experiments: { enableLogs: true } })`. Reference the logger using `const { logger } = Sentry`.

### Configuration

In Next.js, the client-side Sentry initialization is in `sentry.client.config.ts`, the server initialization is in `sentry.server.config.ts`, and the edge initialization is in `sentry.edge.config.ts`. Initialization does not need to be repeated in other files; it only needs to happen in the files mentioned above. You should use `import * as Sentry from "@sentry/nextjs"` to reference Sentry functionality.

### Baseline Configuration

```typescript
import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NEXT_PUBLIC_SENTRY_ENVIRONMENT || "development",
  
  _experiments: {
    enableLogs: true,
  },
});
```

### Logger Integration

```typescript
Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  integrations: [
    // Send console.log, console.error, and console.warn calls as logs to Sentry
    Sentry.consoleLoggingIntegration({ levels: ["log", "error", "warn"] }),
  ],
});
```

### Logger Examples

`logger.fmt` is a template literal function that should be used to bring variables into structured logs.

```typescript
import * as Sentry from "@sentry/nextjs";

const { logger } = Sentry;

// Booking system logging
logger.trace("Starting booking availability check", { 
  propertyId: "prop_123",
  checkIn: "2024-03-15",
  checkOut: "2024-03-18"
});

logger.debug(logger.fmt`Cache miss for property availability: ${propertyId}`);

logger.info("Booking created successfully", { 
  bookingId: "booking_456",
  propertyId: "prop_123",
  guestEmail: "<EMAIL>",
  totalPrice: 299.99
});

logger.warn("Calendar sync delay detected", {
  propertyId: "prop_123",
  syncSource: "hospitable",
  delayMinutes: 15,
  lastSyncTime: "2024-03-14T10:30:00Z"
});

logger.error("Payment processing failed", {
  bookingId: "booking_456",
  paymentMethod: "stripe",
  errorCode: "card_declined",
  amount: 299.99
});

logger.fatal("Database connection pool exhausted", {
  database: "supabase",
  activeConnections: 100,
  maxConnections: 100,
  service: "booking_service"
});
```

### Business-Specific Logging Patterns

```typescript
// Content management logging
logger.info("Social media post created", {
  postId: "post_789",
  platform: "instagram",
  postType: "property_showcase",
  mediaCount: 3,
  propertyId: "prop_123"
});

logger.warn("Media upload size exceeded", {
  fileName: "property_video.mp4",
  fileSize: "50MB",
  maxSize: "25MB",
  uploadId: "upload_321"
});

// Calendar synchronization logging
logger.info("Hospitable calendar sync completed", {
  propertyId: "prop_123",
  syncedEvents: 15,
  conflicts: 2,
  syncDuration: "2.3s",
  lastSyncTime: new Date().toISOString()
});

logger.error("Airbnb calendar sync failed", {
  propertyId: "prop_123",
  listingId: "1451906792103385338",
  errorType: "authentication_failed",
  retryCount: 3,
  nextRetryTime: "2024-03-14T11:00:00Z"
});
```

## TypeScript Integration

### Proper Import Patterns

Always use the correct import pattern for Sentry in Next.js:

```typescript
// Correct import for Next.js
import * as Sentry from "@sentry/nextjs";

// Use existing utility functions
import { 
  reportError, 
  reportBusinessError, 
  startTransaction,
  addBusinessBreadcrumb,
  addAPIBreadcrumb 
} from "@/lib/sentry";
```

### Type-Safe Error Handling

```typescript
import { BusinessError, ErrorContext } from "@/lib/sentry";

// Define business error with proper typing
const bookingError: BusinessError = {
  code: 'BOOKING_VALIDATION_FAILED',
  message: 'Guest count exceeds property maximum',
  severity: 'medium',
  category: 'validation',
  context: {
    propertyId: 'prop_123',
    requestedGuests: 8,
    maxGuests: 6
  }
};

// Define error context with proper typing
const errorContext: ErrorContext = {
  user: {
    id: 'user_456',
    email: '<EMAIL>'
  },
  page: {
    url: '/booking/prop_123',
    section: 'guest-selection',
    component: 'GuestCountSelector'
  },
  business: {
    operation: 'booking_validation',
    entityType: 'booking',
    entityId: 'prop_123',
    workflow: 'booking_creation'
  }
};

// Report with proper typing
reportBusinessError(bookingError, errorContext);
```

### React Component Integration

```typescript
import * as Sentry from "@sentry/nextjs";
import { ErrorBoundary } from "@sentry/react";

// Error boundary with Sentry integration
function BookingErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={({ error, resetError }) => (
        <div className="error-fallback">
          <h2>Booking Error</h2>
          <p>Something went wrong with your booking.</p>
          <button onClick={resetError}>Try Again</button>
        </div>
      )}
      beforeCapture={(scope, error, errorInfo) => {
        scope.setTag('component', 'BookingFlow');
        scope.setContext('errorInfo', errorInfo);
      }}
    >
      {children}
    </ErrorBoundary>
  );
}

// Hook-based error handling
function useBookingError() {
  const reportBookingError = useCallback((error: Error, bookingContext: any) => {
    Sentry.withScope((scope) => {
      scope.setTag('hook', 'useBookingError');
      scope.setContext('booking', bookingContext);
      Sentry.captureException(error);
    });
  }, []);

  return { reportBookingError };
}
```

## Business-Specific Monitoring Patterns

### Booking System Monitoring

```typescript
// Booking availability check
async function checkBookingAvailability(propertyId: string, dates: DateRange) {
  return Sentry.startSpan(
    {
      op: "business.booking.availability_check",
      name: "Check Booking Availability",
      attributes: {
        propertyId,
        checkIn: dates.checkIn,
        checkOut: dates.checkOut,
        operation: 'availability_check'
      }
    },
    async (span) => {
      try {
        // Add business breadcrumb
        addBusinessBreadcrumb(
          'availability_check',
          'booking',
          propertyId,
          undefined,
          { checkIn: dates.checkIn, checkOut: dates.checkOut }
        );

        const availability = await fetchAvailability(propertyId, dates);
        
        span?.setAttribute('availability.is_available', availability.isAvailable);
        span?.setAttribute('availability.conflicts', availability.conflicts.length);
        
        if (!availability.isAvailable) {
          logger.warn("Booking availability conflict detected", {
            propertyId,
            requestedDates: dates,
            conflicts: availability.conflicts,
            conflictCount: availability.conflicts.length
          });
        }
        
        return availability;
      } catch (error) {
        span?.recordException(error as Error);
        reportBusinessError({
          code: 'AVAILABILITY_CHECK_FAILED',
          message: 'Failed to check booking availability',
          severity: 'high',
          category: 'business',
          context: { propertyId, dates }
        });
        throw error;
      }
    }
  );
}

// Payment processing monitoring
async function processBookingPayment(bookingId: string, paymentData: PaymentData) {
  return Sentry.startSpan(
    {
      op: "business.booking.payment",
      name: "Process Booking Payment",
      attributes: {
        bookingId,
        amount: paymentData.amount,
        currency: paymentData.currency,
        paymentMethod: paymentData.method
      }
    },
    async (span) => {
      try {
        const payment = await processPayment(paymentData);
        
        span?.setAttribute('payment.status', payment.status);
        span?.setAttribute('payment.transaction_id', payment.transactionId);
        
        logger.info("Booking payment processed", {
          bookingId,
          transactionId: payment.transactionId,
          amount: paymentData.amount,
          status: payment.status
        });
        
        return payment;
      } catch (error) {
        span?.recordException(error as Error);
        reportBusinessError({
          code: 'PAYMENT_PROCESSING_FAILED',
          message: 'Booking payment processing failed',
          severity: 'critical',
          category: 'business',
          context: { 
            bookingId, 
            amount: paymentData.amount,
            paymentMethod: paymentData.method,
            errorDetails: (error as Error).message
          }
        });
        throw error;
      }
    }
  );
}
```

### Calendar Synchronization Monitoring

```typescript
// Hospitable calendar sync
async function syncHospitableCalendar(propertyId: string) {
  return Sentry.startSpan(
    {
      op: "business.calendar.sync_hospitable",
      name: "Sync Hospitable Calendar",
      attributes: {
        propertyId,
        syncSource: 'hospitable',
        operation: 'calendar_sync'
      }
    },
    async (span) => {
      try {
        const calendarUrl = getHospitableCalendarUrl(propertyId);
        
        // Add API breadcrumb
        addAPIBreadcrumb('GET', calendarUrl, undefined, undefined, {
          propertyId,
          syncSource: 'hospitable'
        });

        const calendarData = await fetchHospitableCalendar(calendarUrl);
        
        span?.setAttribute('sync.events_count', calendarData.events.length);
        span?.setAttribute('sync.conflicts_detected', calendarData.conflicts.length);
        span?.setAttribute('sync.last_modified', calendarData.lastModified);
        
        logger.info("Hospitable calendar sync completed", {
          propertyId,
          eventsCount: calendarData.events.length,
          conflictsDetected: calendarData.conflicts.length,
          syncDuration: span?.getDuration() || 0
        });
        
        return calendarData;
      } catch (error) {
        span?.recordException(error as Error);
        reportBusinessError({
          code: 'HOSPITABLE_SYNC_FAILED',
          message: 'Failed to sync Hospitable calendar',
          severity: 'high',
          category: 'external',
          context: { 
            propertyId,
            syncSource: 'hospitable',
            errorType: (error as Error).name
          }
        });
        throw error;
      }
    }
  );
}
```

### Content Management Monitoring

```typescript
// Social media post creation
async function createSocialMediaPost(postData: SocialPostData) {
  return Sentry.startSpan(
    {
      op: "business.content.create_post",
      name: "Create Social Media Post",
      attributes: {
        platform: postData.platform,
        postType: postData.type,
        mediaCount: postData.mediaUrls?.length || 0,
        hasVideo: postData.mediaUrls?.some(url => url.includes('.mp4')) || false
      }
    },
    async (span) => {
      try {
        // Add business breadcrumb
        addBusinessBreadcrumb(
          'create_social_post',
          'content',
          undefined,
          undefined,
          { 
            platform: postData.platform,
            type: postData.type,
            mediaCount: postData.mediaUrls?.length || 0
          }
        );

        const post = await savePostToDatabase(postData);
        
        span?.setAttribute('post.id', post.id);
        span?.setAttribute('post.status', post.status);
        
        logger.info("Social media post created", {
          postId: post.id,
          platform: postData.platform,
          type: postData.type,
          mediaCount: postData.mediaUrls?.length || 0,
          status: post.status
        });
        
        return post;
      } catch (error) {
        span?.recordException(error as Error);
        reportBusinessError({
          code: 'SOCIAL_POST_CREATION_FAILED',
          message: 'Failed to create social media post',
          severity: 'medium',
          category: 'business',
          context: { 
            platform: postData.platform,
            postType: postData.type,
            mediaCount: postData.mediaUrls?.length || 0
          }
        });
        throw error;
      }
    }
  );
}

// Blog article expansion from social post
async function expandPostToBlog(postId: string, blogData: BlogExpansionData) {
  return Sentry.startSpan(
    {
      op: "business.content.expand_to_blog",
      name: "Expand Social Post to Blog",
      attributes: {
        postId,
        blogTitle: blogData.title,
        contentLength: blogData.content.length,
        operation: 'content_expansion'
      }
    },
    async (span) => {
      try {
        const blogArticle = await createBlogArticle({
          ...blogData,
          socialPostId: postId
        });
        
        span?.setAttribute('blog.id', blogArticle.id);
        span?.setAttribute('blog.word_count', blogData.content.split(' ').length);
        span?.setAttribute('blog.has_featured_image', !!blogData.featuredImage);
        
        logger.info("Blog article created from social post", {
          blogId: blogArticle.id,
          sourcePostId: postId,
          title: blogData.title,
          wordCount: blogData.content.split(' ').length
        });
        
        return blogArticle;
      } catch (error) {
        span?.recordException(error as Error);
        reportBusinessError({
          code: 'BLOG_EXPANSION_FAILED',
          message: 'Failed to expand social post to blog article',
          severity: 'medium',
          category: 'business',
          context: { 
            postId,
            blogTitle: blogData.title,
            contentLength: blogData.content.length
          }
        });
        throw error;
      }
    }
  );
}
```

## Testing Patterns

### Unit Testing with Sentry Mocks

```typescript
import * as Sentry from "@sentry/nextjs";
import { reportBusinessError } from "@/lib/sentry";

// Mock Sentry for testing
jest.mock("@sentry/nextjs", () => ({
  captureException: jest.fn(),
  startSpan: jest.fn((options, callback) => callback(mockSpan)),
  withScope: jest.fn((callback) => callback(mockScope)),
}));

const mockSpan = {
  setAttribute: jest.fn(),
  setTag: jest.fn(),
  recordException: jest.fn(),
};

const mockScope = {
  setTag: jest.fn(),
  setContext: jest.fn(),
  setUser: jest.fn(),
};

describe('Booking Service Error Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should capture booking validation errors with proper context', async () => {
    const bookingData = { propertyId: 'prop_123', guestCount: 8 };
    
    await expect(validateBooking(bookingData)).rejects.toThrow();
    
    expect(Sentry.captureException).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        tags: expect.objectContaining({
          operation: 'booking_validation'
        }),
        contexts: expect.objectContaining({
          booking: expect.objectContaining({
            propertyId: 'prop_123',
            guestCount: 8
          })
        })
      })
    );
  });

  it('should create performance spans for booking operations', async () => {
    await createBooking({ propertyId: 'prop_123' });
    
    expect(Sentry.startSpan).toHaveBeenCalledWith(
      expect.objectContaining({
        op: 'business.booking.create',
        name: 'Create Booking',
        attributes: expect.objectContaining({
          propertyId: 'prop_123'
        })
      }),
      expect.any(Function)
    );
  });
});
```

### Integration Testing

```typescript
describe('Sentry Integration Tests', () => {
  it('should properly configure Sentry in different environments', () => {
    process.env.NEXT_PUBLIC_SENTRY_DSN = 'test-dsn';
    process.env.NEXT_PUBLIC_SENTRY_ENVIRONMENT = 'test';
    
    // Test configuration loading
    const config = getSentryConfig();
    
    expect(config).toEqual({
      dsn: 'test-dsn',
      environment: 'test',
      tracesSampleRate: expect.any(Number)
    });
  });

  it('should handle Sentry initialization errors gracefully', () => {
    process.env.NEXT_PUBLIC_SENTRY_DSN = '';
    
    expect(() => initSentry()).not.toThrow();
    // Should log warning instead of throwing
  });
});
```

## Anti-Patterns to Avoid

### ❌ Don't Do This

```typescript
// Don't re-initialize Sentry in components
import * as Sentry from "@sentry/nextjs";

function MyComponent() {
  Sentry.init({ dsn: "..." }); // ❌ Wrong - already initialized
  
  // Don't capture errors without context
  try {
    doSomething();
  } catch (error) {
    Sentry.captureException(error); // ❌ Missing context
  }
  
  // Don't create meaningless spans
  Sentry.startSpan({ op: "function", name: "doStuff" }, () => {
    // ❌ Not meaningful for monitoring
  });
}
```

### ✅ Do This Instead

```typescript
// Use existing utilities and provide context
import { reportError, reportBusinessError } from "@/lib/sentry";

function MyComponent() {
  try {
    doSomething();
  } catch (error) {
    reportError(error, {
      page: { url: '/booking', section: 'form-submission' },
      business: { operation: 'booking_creation' }
    }); // ✅ Proper context
  }
  
  // Create meaningful spans for business operations
  Sentry.startSpan({
    op: "business.booking.validation",
    name: "Validate Booking Data",
    attributes: { propertyId, guestCount }
  }, () => {
    // ✅ Meaningful business operation
  });
}
```

## Environment-Specific Configuration

### Development
- Use 100% sampling rates for complete visibility
- Enable verbose logging and debugging
- Capture all errors for development feedback

### Production
- Use reduced sampling rates (10-20%) to manage volume
- Filter out non-critical errors
- Focus on business-critical error monitoring
- Enable session replay for critical user journeys

### Testing
- Mock Sentry operations in unit tests
- Use test-specific Sentry project for integration tests
- Validate error capture and performance monitoring in E2E tests

This comprehensive guide ensures consistent, effective Sentry integration across the J&A Business Solutions application, with specific focus on the booking system, content management, and calendar synchronization features.