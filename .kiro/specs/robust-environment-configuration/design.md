# Design Document

## Overview

The robust environment configuration system provides a comprehensive, type-safe approach to managing environment variables and application configuration. The system emphasizes developer experience, security, and operational reliability through clear validation, graceful degradation, and comprehensive error reporting.

## Architecture

### Core Components

```mermaid
graph TB
    A[Environment Loader] --> B[Configuration Validator]
    B --> C[Type System]
    C --> D[Service Registry]
    D --> E[Health Monitor]
    
    F[Environment Files] --> A
    G[Runtime Environment] --> A
    
    B --> H[Validation Rules]
    B --> I[Error Reporter]
    
    D --> J[Service Connectors]
    D --> K[Fallback Handlers]
    
    E --> L[Health Endpoints]
    E --> M[Status Dashboard]
```

### Configuration Flow

```mermaid
sequenceDiagram
    participant App as Application
    participant Loader as Environment Loader
    participant Validator as Configuration Validator
    participant Registry as Service Registry
    participant Monitor as Health Monitor
    
    App->>Loader: Initialize Configuration
    Loader->>Loader: Load Environment Variables
    Loader->>Validator: Validate Configuration
    Validator->>Validator: Apply Validation Rules
    Validator->>Registry: Register Services
    Registry->>Registry: Test Service Connectivity
    Registry->>Monitor: Initialize Health Checks
    Monitor-->>App: Configuration Ready
```

## Components and Interfaces

### Environment Configuration Types

```typescript
// Core configuration interface
export interface EnvironmentConfig {
  // Application settings
  app: {
    name: string;
    version: string;
    environment: 'development' | 'staging' | 'production';
    port: number;
    baseUrl: string;
  };
  
  // Database configuration
  database: {
    url: string;
    maxConnections: number;
    connectionTimeout: number;
    ssl: boolean;
  };
  
  // External services
  services: {
    sentry: SentryConfig;
    posthog: PostHogConfig;
    supabase: SupabaseConfig;
  };
  
  // Feature flags
  features: {
    analytics: boolean;
    monitoring: boolean;
    debugging: boolean;
  };
  
  // Security settings
  security: {
    corsOrigins: string[];
    rateLimiting: boolean;
    apiKeyRequired: boolean;
  };
}

// Service-specific configurations
export interface SentryConfig {
  dsn: string;
  environment: string;
  tracesSampleRate: number;
  profilesSampleRate: number;
  enabled: boolean;
}

export interface PostHogConfig {
  apiKey: string;
  apiHost: string;
  enabled: boolean;
  capturePageViews: boolean;
}

export interface SupabaseConfig {
  url: string;
  anonKey: string;
  serviceRoleKey?: string;
  enabled: boolean;
}
```

### Configuration Validator

```typescript
export interface ValidationRule<T = any> {
  name: string;
  description: string;
  validate: (value: T, context: ValidationContext) => ValidationResult;
  required: boolean;
  environments: ('development' | 'staging' | 'production')[];
}

export interface ValidationContext {
  environment: string;
  allConfig: Partial<EnvironmentConfig>;
  isOptional: boolean;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  suggestions: string[];
}

export interface ValidationError {
  field: string;
  message: string;
  expectedFormat?: string;
  currentValue?: string;
  severity: 'error' | 'warning';
  helpUrl?: string;
}

export class ConfigurationValidator {
  private rules: Map<string, ValidationRule> = new Map();
  
  registerRule(path: string, rule: ValidationRule): void;
  validateConfiguration(config: Partial<EnvironmentConfig>): ValidationResult;
  validateField(path: string, value: any, context: ValidationContext): ValidationResult;
  getValidationSummary(): ValidationSummary;
}
```

### Service Registry

```typescript
export interface ServiceDefinition {
  name: string;
  type: 'required' | 'optional';
  healthCheck: () => Promise<ServiceHealth>;
  fallbackHandler?: FallbackHandler;
  retryConfig: RetryConfig;
}

export interface ServiceHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  latency?: number;
  lastChecked: Date;
  error?: string;
  metadata?: Record<string, any>;
}

export interface FallbackHandler {
  canFallback: (error: Error) => boolean;
  getFallbackImplementation: () => any;
  getStatusMessage: () => string;
}

export class ServiceRegistry {
  private services: Map<string, ServiceDefinition> = new Map();
  private healthStatus: Map<string, ServiceHealth> = new Map();
  
  registerService(service: ServiceDefinition): void;
  getServiceHealth(serviceName: string): ServiceHealth;
  getAllServicesHealth(): Map<string, ServiceHealth>;
  enableFallbackMode(serviceName: string): void;
  testConnectivity(): Promise<Map<string, ServiceHealth>>;
}
```

### Environment Loader

```typescript
export class EnvironmentLoader {
  private config: EnvironmentConfig | null = null;
  private validator: ConfigurationValidator;
  private serviceRegistry: ServiceRegistry;
  
  constructor(
    validator: ConfigurationValidator,
    serviceRegistry: ServiceRegistry
  );
  
  async loadConfiguration(): Promise<EnvironmentConfig>;
  reloadConfiguration(): Promise<EnvironmentConfig>;
  getConfiguration(): EnvironmentConfig;
  isConfigurationValid(): boolean;
  getConfigurationErrors(): ValidationError[];
}
```

## Data Models

### Configuration Schema

```typescript
// Environment variable definitions with metadata
export const ENV_SCHEMA = {
  // Application configuration
  'NEXT_PUBLIC_APP_NAME': {
    type: 'string',
    required: true,
    default: 'J&A Business Solutions',
    description: 'Application display name',
    validation: (value: string) => value.length > 0
  },
  
  'NEXT_PUBLIC_APP_ENVIRONMENT': {
    type: 'enum',
    required: true,
    values: ['development', 'staging', 'production'],
    description: 'Current deployment environment'
  },
  
  // Sentry configuration
  'NEXT_PUBLIC_SENTRY_DSN': {
    type: 'url',
    required: ['staging', 'production'],
    optional: ['development'],
    description: 'Sentry Data Source Name for error tracking',
    validation: (value: string) => {
      const sentryDsnPattern = /^https:\/\/[a-f0-9]+@[a-z0-9]+\.ingest\.sentry\.io\/[0-9]+$/;
      return sentryDsnPattern.test(value);
    },
    example: 'https://<EMAIL>/123456',
    helpUrl: 'https://docs.sentry.io/product/sentry-basics/dsn-explainer/'
  },
  
  // PostHog configuration
  'NEXT_PUBLIC_POSTHOG_KEY': {
    type: 'string',
    required: ['staging', 'production'],
    optional: ['development'],
    description: 'PostHog API key for analytics',
    validation: (value: string) => value.startsWith('phc_') && value.length > 20,
    example: 'phc_abc123def456...'
  },
  
  // Supabase configuration
  'NEXT_PUBLIC_SUPABASE_URL': {
    type: 'url',
    required: true,
    description: 'Supabase project URL',
    validation: (value: string) => value.includes('.supabase.co'),
    example: 'https://abc123.supabase.co'
  },
  
  'NEXT_PUBLIC_SUPABASE_ANON_KEY': {
    type: 'string',
    required: true,
    description: 'Supabase anonymous key',
    validation: (value: string) => value.startsWith('eyJ') && value.length > 100,
    sensitive: true
  }
} as const;
```

### Configuration State Management

```typescript
export interface ConfigurationState {
  status: 'loading' | 'valid' | 'invalid' | 'degraded';
  config: EnvironmentConfig | null;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  servicesHealth: Map<string, ServiceHealth>;
  lastValidated: Date;
  fallbacksActive: string[];
}

export class ConfigurationManager {
  private state: ConfigurationState;
  private listeners: Set<(state: ConfigurationState) => void> = new Set();
  
  getState(): ConfigurationState;
  subscribe(listener: (state: ConfigurationState) => void): () => void;
  validateConfiguration(): Promise<void>;
  enableGracefulDegradation(serviceName: string): void;
  getHealthSummary(): HealthSummary;
}
```

## Error Handling

### Validation Error Types

```typescript
export class EnvironmentValidationError extends Error {
  constructor(
    message: string,
    public field: string,
    public expectedFormat?: string,
    public currentValue?: string,
    public suggestions: string[] = [],
    public helpUrl?: string
  ) {
    super(message);
    this.name = 'EnvironmentValidationError';
  }
}

export class ServiceConnectionError extends Error {
  constructor(
    message: string,
    public serviceName: string,
    public endpoint?: string,
    public statusCode?: number,
    public canFallback: boolean = false
  ) {
    super(message);
    this.name = 'ServiceConnectionError';
  }
}

export class ConfigurationLoadError extends Error {
  constructor(
    message: string,
    public errors: ValidationError[],
    public partialConfig?: Partial<EnvironmentConfig>
  ) {
    super(message);
    this.name = 'ConfigurationLoadError';
  }
}
```

### Error Recovery Strategies

```typescript
export interface ErrorRecoveryStrategy {
  canRecover(error: Error): boolean;
  recover(error: Error, context: RecoveryContext): Promise<RecoveryResult>;
  getRecoveryMessage(): string;
}

export interface RecoveryContext {
  environment: string;
  serviceName?: string;
  configPath?: string;
  attemptCount: number;
}

export interface RecoveryResult {
  success: boolean;
  fallbackValue?: any;
  message: string;
  shouldRetry: boolean;
}

// Built-in recovery strategies
export const RECOVERY_STRATEGIES = {
  missingOptionalService: new OptionalServiceRecoveryStrategy(),
  invalidUrl: new UrlValidationRecoveryStrategy(),
  connectionTimeout: new ConnectionRetryStrategy(),
  malformedConfig: new ConfigurationRepairStrategy()
};
```

## Testing Strategy

### Unit Testing Approach

```typescript
// Configuration validation tests
describe('ConfigurationValidator', () => {
  it('should validate Sentry DSN format correctly', () => {
    const validator = new ConfigurationValidator();
    const result = validator.validateField(
      'NEXT_PUBLIC_SENTRY_DSN',
      'https://<EMAIL>/123456',
      { environment: 'production', allConfig: {}, isOptional: false }
    );
    expect(result.isValid).toBe(true);
  });
  
  it('should provide helpful error messages for invalid DSN', () => {
    const validator = new ConfigurationValidator();
    const result = validator.validateField(
      'NEXT_PUBLIC_SENTRY_DSN',
      'invalid-dsn',
      { environment: 'production', allConfig: {}, isOptional: false }
    );
    expect(result.isValid).toBe(false);
    expect(result.errors[0].message).toContain('Expected format');
    expect(result.errors[0].helpUrl).toBeDefined();
  });
});

// Service registry tests
describe('ServiceRegistry', () => {
  it('should handle service fallbacks gracefully', async () => {
    const registry = new ServiceRegistry();
    registry.registerService({
      name: 'analytics',
      type: 'optional',
      healthCheck: () => Promise.reject(new Error('Service unavailable')),
      fallbackHandler: new MockAnalyticsFallback()
    });
    
    const health = await registry.getServiceHealth('analytics');
    expect(health.status).toBe('degraded');
  });
});
```

### Integration Testing

```typescript
// End-to-end configuration loading tests
describe('Environment Configuration Integration', () => {
  it('should load configuration successfully in development', async () => {
    process.env.NODE_ENV = 'development';
    process.env.NEXT_PUBLIC_SENTRY_DSN = ''; // Optional in development
    
    const loader = new EnvironmentLoader(validator, serviceRegistry);
    const config = await loader.loadConfiguration();
    
    expect(config.app.environment).toBe('development');
    expect(config.services.sentry.enabled).toBe(false);
  });
  
  it('should enforce strict validation in production', async () => {
    process.env.NODE_ENV = 'production';
    process.env.NEXT_PUBLIC_SENTRY_DSN = ''; // Required in production
    
    const loader = new EnvironmentLoader(validator, serviceRegistry);
    
    await expect(loader.loadConfiguration()).rejects.toThrow(
      ConfigurationLoadError
    );
  });
});
```

### Health Check Testing

```typescript
// Health monitoring tests
describe('Health Monitoring', () => {
  it('should detect service degradation', async () => {
    const monitor = new HealthMonitor(serviceRegistry);
    
    // Simulate service becoming unhealthy
    jest.spyOn(serviceRegistry, 'getServiceHealth')
      .mockResolvedValue({
        status: 'unhealthy',
        lastChecked: new Date(),
        error: 'Connection timeout'
      });
    
    const summary = await monitor.getHealthSummary();
    expect(summary.overallStatus).toBe('degraded');
    expect(summary.unhealthyServices).toContain('analytics');
  });
});
```

## Implementation Considerations

### Performance Optimization

1. **Lazy Loading**: Load and validate configuration only when needed
2. **Caching**: Cache validated configuration to avoid repeated validation
3. **Async Validation**: Perform non-critical validations asynchronously
4. **Batch Health Checks**: Group health checks to reduce overhead

### Security Measures

1. **Secret Masking**: Never log or expose sensitive configuration values
2. **Validation Sanitization**: Sanitize inputs during validation
3. **Access Control**: Restrict access to sensitive configuration methods
4. **Audit Logging**: Log configuration access and changes

### Monitoring and Observability

1. **Configuration Metrics**: Track configuration load times and validation results
2. **Health Dashboards**: Provide real-time service health visibility
3. **Alert Integration**: Send alerts when critical services become unhealthy
4. **Performance Tracking**: Monitor configuration-related performance impacts

### Development Experience

1. **IDE Integration**: Provide TypeScript definitions for autocomplete
2. **Documentation Generation**: Auto-generate configuration documentation
3. **Setup Wizards**: Provide guided setup for new developers
4. **Validation Feedback**: Give immediate feedback on configuration issues

This design provides a robust foundation for environment configuration management that addresses the current Sentry DSN validation error while establishing a scalable system for future configuration needs.