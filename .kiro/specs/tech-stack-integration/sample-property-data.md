# Sample Property Data

## Property 1 - Airbnb Listing

### Basic Information
- **Airbnb URL**: https://www.airbnb.com/rooms/1451906792103385338
- **Airbnb Listing ID**: 1451906792103385338
- **Max Guests**: 12 adults
- **Hospitable Property Key**: 1924170

### Calendar Integration
- **Hospitable iCal URL**: https://api.hospitable.com/v1/properties/reservations.ics?key=1924170&token=11f02529-9354-4278-8008-d45898c11dce&noCache
- **Sync Frequency**: Every 15 minutes
- **Conflict Prevention**: Real-time availability checking

### Database Seed Data
```sql
INSERT INTO properties (
  title,
  description,
  location,
  price_per_night,
  max_guests,
  airbnb_url,
  airbnb_listing_id,
  hospitable_calendar_url,
  hospitable_property_key,
  is_active
) VALUES (
  'Premium Corporate Rental - 12 Guests',
  'Spacious property perfect for corporate groups and extended stays',
  'Tucson, AZ',
  250.00,
  12,
  'https://www.airbnb.com/rooms/1451906792103385338',
  '1451906792103385338',
  'https://api.hospitable.com/v1/properties/reservations.ics?key=1924170&token=11f02529-9354-4278-8008-d45898c11dce&noCache',
  '1924170',
  true
);
```

### Implementation Notes
- Use Hospitable API for calendar management instead of direct Airbnb integration
- Hospitable handles the Airbnb synchronization automatically
- iCal format provides standardized calendar data
- Property supports up to 12 guests for corporate bookings
- Located in Tucson, AZ area

### Testing Scenarios
- Test calendar sync with real Hospitable data
- Verify booking conflict detection
- Test availability checking for date ranges
- Validate guest capacity limits (max 12)
- Test booking flow with real property data