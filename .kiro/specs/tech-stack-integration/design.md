# Design Document

## Overview

This design outlines the migration from React/Vite to Next.js with shadcn/ui and the integration of Supabase (database), Sentry (error monitoring), PostHog (analytics), Playwright (testing), and GitHub Actions (CI/CD) into the J&A Business Solutions LLC application. The migration and integration will transform the current static website into a modern, production-ready Next.js application with proper monitoring, testing, and deployment infrastructure, following TypeScript-first and MCP-priority development practices.

## Architecture

### Current Architecture

- **Frontend**: React 18 + TypeScript + Vite
- **Styling**: Tailwind CSS with custom design system
- **PWA**: Vite PWA plugin with service worker
- **Deployment**: Static hosting (current state)

### Target Architecture (Next.js Migration)

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Layer                           │
│  Next.js 14+ + TypeScript + shadcn/ui + Tailwind CSS      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Sentry    │ │   PostHog   │ │   Supabase  │          │
│  │ Monitoring  │ │  Analytics  │ │   Client    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Backend Services                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Supabase  │ │   Sentry    │ │   PostHog   │          │
│  │  Database   │ │   Server    │ │   Server    │          │
│  │   + Auth    │ │             │ │             │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                 CI/CD & Testing Layer                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   GitHub    │ │  Playwright │ │   Build &   │          │
│  │   Actions   │ │   E2E Tests │ │   Deploy    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## Components and Interfaces

### 1. Supabase Integration

#### Database Client Setup

```typescript
// src/lib/supabase.ts
interface SupabaseConfig {
  url: string;
  anonKey: string;
}

interface DatabaseClient {
  from: (table: string) => QueryBuilder;
  auth: AuthClient;
  storage: StorageClient;
}
```

#### Environment Configuration

- `VITE_SUPABASE_URL`: Supabase project URL
- `VITE_SUPABASE_ANON_KEY`: Public anonymous key
- Separate configurations for development and production

#### Database Schema (Enhanced for Booking & Content)

```sql
-- Contact form submissions
CREATE TABLE contact_submissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  message TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Property listings with booking capabilities
CREATE TABLE properties (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  description TEXT,
  location TEXT,
  price_per_night DECIMAL,
  max_guests INTEGER,
  amenities JSONB,
  images TEXT[],
  airbnb_url TEXT, -- e.g., https://www.airbnb.com/rooms/1451906792103385338
  airbnb_listing_id TEXT, -- e.g., 1451906792103385338
  hospitable_calendar_url TEXT, -- iCal URL from Hospitable
  hospitable_property_key TEXT, -- e.g., 1924170
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Booking system
CREATE TABLE bookings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id UUID REFERENCES properties(id),
  guest_name TEXT NOT NULL,
  guest_email TEXT NOT NULL,
  guest_phone TEXT,
  check_in DATE NOT NULL,
  check_out DATE NOT NULL,
  total_guests INTEGER NOT NULL,
  total_price DECIMAL NOT NULL,
  status TEXT DEFAULT 'pending', -- pending, confirmed, cancelled
  airbnb_sync_status TEXT DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content management for social media posts
CREATE TABLE social_posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  media_urls TEXT[],
  post_type TEXT, -- video, image, carousel
  platform TEXT, -- instagram, facebook, tiktok
  published_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Blog articles (long-form content)
CREATE TABLE blog_articles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  content TEXT NOT NULL,
  excerpt TEXT,
  featured_image TEXT,
  social_post_id UUID REFERENCES social_posts(id),
  published_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Calendar availability (synced with Airbnb)
CREATE TABLE availability_calendar (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id UUID REFERENCES properties(id),
  date DATE NOT NULL,
  is_available BOOLEAN DEFAULT true,
  price_override DECIMAL,
  source TEXT DEFAULT 'manual', -- manual, airbnb, booking
  last_synced TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(property_id, date)
);
```

### 2. Sentry Integration

#### Error Monitoring Setup

```typescript
// src/lib/sentry.ts
interface SentryConfig {
  dsn: string;
  environment: string;
  tracesSampleRate: number;
}

interface ErrorBoundaryProps {
  fallback: React.ComponentType<{ error: Error }>;
  children: React.ReactNode;
}
```

#### Error Tracking Strategy

- **Client-side errors**: Automatic capture with React Error Boundary
- **Performance monitoring**: Core Web Vitals and user interactions
- **User context**: Anonymous session tracking
- **Custom events**: Business-specific error tracking

### 3. PostHog Integration

#### Analytics Client Setup

```typescript
// src/lib/posthog.ts
interface PostHogConfig {
  apiKey: string;
  apiHost: string;
  options: {
    capture_pageview: boolean;
    capture_pageleave: boolean;
  };
}

interface AnalyticsEvents {
  page_view: { page: string; section: string };
  contact_form_submit: { form_type: string };
  cta_click: { cta_type: string; location: string };
}
```

#### Event Tracking Strategy

- **Page views**: Automatic tracking with section identification
- **User interactions**: CTA clicks, form submissions, navigation
- **Business metrics**: Contact form conversions, service inquiries
- **Privacy compliance**: GDPR-compliant anonymous tracking

### 4. PWA Architecture & Mobile Optimization

#### PWA Features

```typescript
// src/lib/pwa.ts
interface PWAConfig {
  manifest: {
    name: string;
    short_name: string;
    theme_color: string;
    background_color: string;
    display: "standalone" | "fullscreen";
    orientation: "portrait" | "landscape";
  };
  serviceWorker: {
    caching: CachingStrategy;
    offline: OfflineStrategy;
  };
}

interface BookingOfflineData {
  pendingBookings: BookingRequest[];
  cachedAvailability: AvailabilityData[];
  lastSync: Date;
}
```

#### Mobile-First Design Considerations

- **Touch-optimized UI**: Large touch targets, swipe gestures for image galleries
- **Responsive Booking Flow**: Optimized for mobile booking experience
- **Offline Capability**: Cache property data and allow offline browsing
- **Push Notifications**: Booking confirmations and availability updates
- **App-like Experience**: Full-screen mode, splash screen, app icons

### 5. Booking System & Airbnb Integration

#### Booking API Architecture

```typescript
// src/lib/booking.ts
interface BookingSystem {
  checkAvailability: (
    propertyId: string,
    dates: DateRange
  ) => Promise<AvailabilityResult>;
  createBooking: (booking: BookingRequest) => Promise<BookingConfirmation>;
  syncAirbnbCalendar: (propertyId: string) => Promise<SyncResult>;
  validateBookingConflicts: (booking: BookingRequest) => Promise<ConflictCheck>;
}

interface HospitableIntegration {
  fetchCalendar: (calendarUrl: string) => Promise<CalendarData>;
  parseICalData: (icalData: string) => AvailabilityData[];
  detectConflicts: (
    newBooking: BookingRequest,
    existingBookings: BookingData[]
  ) => ConflictResult;
  syncReservations: (propertyKey: string, token: string) => Promise<SyncResult>;
}

// Example usage with real data
const PROPERTY_CONFIG = {
  hospitable_calendar_url:
    "https://api.hospitable.com/v1/properties/reservations.ics?key=1924170&token=11f02529-9354-4278-8008-d45898c11dce&noCache",
  airbnb_url: "https://www.airbnb.com/rooms/1451906792103385338",
  airbnb_listing_id: "1451906792103385338",
  hospitable_property_key: "1924170",
  max_guests: 12,
};
```

#### Calendar Synchronization Strategy

- **Hospitable Integration**: Use Hospitable API for calendar management and Airbnb sync
- **iCal URL**: `https://api.hospitable.com/v1/properties/reservations.ics?key=1924170&token=11f02529-9354-4278-8008-d45898c11dce&noCache`
- **Real-time Sync**: Periodic sync with Hospitable calendar (every 15 minutes)
- **Conflict Prevention**: Check availability before confirming bookings
- **Dual Booking Protection**: Block dates immediately upon booking request
- **Manual Override**: Allow manual calendar adjustments for special cases

### 6. Content Management System

#### Social Media Integration

```typescript
// src/lib/content.ts
interface ContentManagement {
  socialPosts: SocialPostManager;
  blogArticles: BlogManager;
  mediaLibrary: MediaManager;
}

interface SocialPostManager {
  createPost: (post: SocialPostData) => Promise<SocialPost>;
  expandToBlog: (postId: string) => Promise<BlogArticle>;
  schedulePost: (
    post: SocialPostData,
    publishDate: Date
  ) => Promise<ScheduledPost>;
}
```

#### Landing Page Architecture

- **Dynamic Content**: Property showcases, latest social posts, booking CTA
- **Social Media Feed**: Embedded posts from Instagram, Facebook, TikTok
- **Video Integration**: Property tour videos, testimonials
- **Conversion Optimization**: Strategic placement of booking buttons and forms

### 7. Playwright Testing Framework

#### Test Structure

```typescript
// tests/e2e/
interface TestSuite {
  navigation: NavigationTests;
  responsive: ResponsiveTests;
  forms: FormTests;
  performance: PerformanceTests;
}

interface TestConfig {
  browsers: ["chromium", "firefox", "webkit"];
  viewports: Mobile | Tablet | Desktop;
  baseURL: string;
}
```

#### Test Coverage Areas

- **Navigation flow**: Header navigation, smooth scrolling, mobile menu
- **Responsive design**: Mobile, tablet, desktop layouts
- **Contact forms**: Form validation, submission flow
- **Performance**: Page load times, Core Web Vitals
- **Accessibility**: WCAG compliance testing

### 5. GitHub Actions CI/CD

#### Pipeline Stages

```yaml
# .github/workflows/main.yml
stages:
  - install_dependencies
  - lint_and_type_check
  - unit_tests
  - e2e_tests
  - build_application
  - deploy_to_production
```

#### Deployment Strategy

- **Development**: Auto-deploy on feature branch pushes to preview environment
- **Staging**: Auto-deploy on main branch for testing
- **Production**: Manual approval or auto-deploy after all checks pass
- **Rollback**: Automated rollback on deployment failure

## Data Models

### Environment Configuration

```typescript
interface EnvironmentConfig {
  supabase: {
    url: string;
    anonKey: string;
  };
  sentry: {
    dsn: string;
    environment: "development" | "staging" | "production";
  };
  posthog: {
    apiKey: string;
    apiHost: string;
  };
  app: {
    environment: string;
    version: string;
  };
}
```

### Error Tracking Models

```typescript
interface ErrorContext {
  user: {
    id?: string;
    email?: string;
  };
  page: {
    url: string;
    section: string;
  };
  browser: {
    name: string;
    version: string;
  };
}
```

### Analytics Event Models

```typescript
interface BaseEvent {
  timestamp: Date;
  sessionId: string;
  userId?: string;
}

interface PageViewEvent extends BaseEvent {
  type: "page_view";
  page: string;
  section: string;
  referrer?: string;
}

interface InteractionEvent extends BaseEvent {
  type: "interaction";
  element: string;
  action: string;
  value?: string;
}
```

## Error Handling

### Client-Side Error Handling

- **React Error Boundaries**: Catch component errors and display fallback UI
- **Async Error Handling**: Proper try-catch blocks for API calls
- **Network Error Handling**: Retry logic and offline state management
- **Validation Errors**: User-friendly form validation messages

### Monitoring and Alerting

- **Sentry Alerts**: Real-time notifications for critical errors
- **Performance Monitoring**: Track Core Web Vitals and user experience metrics
- **Error Rate Thresholds**: Automated alerts when error rates exceed limits
- **Custom Dashboards**: Business-specific monitoring dashboards

### Fallback Strategies

- **Database Unavailable**: Graceful degradation to static content
- **Analytics Failure**: Continue normal operation without tracking
- **Service Worker Issues**: Fallback to network-only mode

## Testing Strategy

### Unit Testing (Existing)

- **Component Testing**: React component unit tests
- **Utility Testing**: Helper function and hook testing
- **Integration Testing**: Service integration tests

### End-to-End Testing (New)

- **User Journey Testing**: Complete user flows from landing to contact
- **Cross-Browser Testing**: Chrome, Firefox, Safari compatibility
- **Responsive Testing**: Mobile, tablet, desktop layouts
- **Performance Testing**: Page load times and Core Web Vitals
- **Accessibility Testing**: WCAG 2.1 compliance

### Testing Environments

- **Local Development**: Full test suite with local services
- **CI/CD Pipeline**: Automated testing on every commit
- **Staging Environment**: Production-like testing environment
- **Production Monitoring**: Continuous monitoring and alerting

### Test Data Management

- **Mock Data**: Consistent test data for development
- **Test Database**: Isolated database for testing
- **Environment Isolation**: Separate test environments
- **Data Cleanup**: Automated test data cleanup procedures
