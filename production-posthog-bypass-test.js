// Production PostHog Bypass Test Script
// This script tests the new bypass functionality that works around ad blockers
console.log('🚀 Testing PostHog Bypass System in Production...');

// Production configuration
const apiKey = 'phc_yErhnRXWMQkP4hO7nU37vDRE72w3KcgyUA5CCXvRdP1';
const apiHost = 'https://us.i.posthog.com';

console.log('🔑 API Key:', apiKey.substring(0, 10) + '...');
console.log('🌐 API Host:', apiHost);
console.log('🌍 Environment: Production');
console.log('📍 Current URL:', window.location.href);

// Test 1: Check if regular PostHog is blocked
console.log('\n=== Test 1: Regular PostHog Loading ===');
if (window.posthog && window.posthog.__loaded) {
  console.log('✅ Regular PostHog is loaded and working');
  
  // Test regular PostHog
  const testEvent = {
    test_type: 'production_regular_posthog',
    timestamp: new Date().toISOString(),
    page_url: window.location.href
  };
  
  window.posthog.capture('production_regular_test', testEvent);
  console.log('📤 Sent test event via regular PostHog');
} else {
  console.log('❌ Regular PostHog is not loaded (likely blocked)');
  console.log('🔧 This is expected if ad blockers are present');
}

// Test 2: Initialize and test bypass system
console.log('\n=== Test 2: PostHog Bypass System ===');

// Create bypass instance manually for testing
class PostHogBypassTest {
  constructor(config) {
    this.config = config;
    this.distinctId = `bypass_test_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    this.sessionId = `session_test_${Date.now()}`;
    this.optedOut = false;
    this.__loaded = true;
  }

  async capture(event, properties = {}) {
    console.log('📊 Bypass capture called:', event, properties);
    
    const eventData = {
      event,
      properties: {
        ...properties,
        distinct_id: this.distinctId,
        $session_id: this.sessionId,
        $lib: 'posthog-bypass-test',
        $lib_version: '1.0.0',
        $current_url: window.location.href,
        $host: window.location.hostname,
        $pathname: window.location.pathname,
        $browser: navigator.userAgent,
        $timestamp: new Date().toISOString(),
        $time: Date.now() / 1000,
      }
    };

    // Try to send to PostHog API directly
    const payload = {
      api_key: this.config.apiKey,
      batch: [{
        event: eventData.event,
        properties: eventData.properties,
        timestamp: new Date().toISOString()
      }]
    };

    // Test multiple endpoints
    const endpoints = [
      `${this.config.apiHost}/batch/`,
      `${this.config.apiHost}/capture/`,
      // Alternative endpoint
      this.config.apiHost.replace('us.i.posthog.com', 'app.posthog.com') + '/batch/',
    ];

    let success = false;
    let lastError = null;

    for (const endpoint of endpoints) {
      try {
        console.log(`🌐 Trying endpoint: ${endpoint}`);
        
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'PostHog-Bypass-Test/1.0.0',
          },
          body: JSON.stringify(payload),
          credentials: 'omit',
          mode: 'cors',
        });

        console.log(`📡 Response status: ${response.status} ${response.statusText}`);

        if (response.ok) {
          console.log(`✅ Successfully sent event to ${endpoint}`);
          success = true;
          break;
        } else {
          const responseText = await response.text();
          console.log(`⚠️ Response body:`, responseText);
          lastError = new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        console.log(`❌ Failed to send to ${endpoint}:`, error.message);
        lastError = error;
        continue;
      }
    }

    if (!success) {
      console.error('❌ All endpoints failed. Last error:', lastError);
      throw lastError;
    }

    return success;
  }

  get_distinct_id() {
    return this.distinctId;
  }

  get_session_id() {
    return this.sessionId;
  }

  has_opted_out_capturing() {
    return this.optedOut;
  }

  opt_in_capturing() {
    this.optedOut = false;
    console.log('✅ Bypass opted in');
  }

  opt_out_capturing() {
    this.optedOut = true;
    console.log('🚫 Bypass opted out');
  }

  get config() {
    return {
      api_host: this.config.apiHost,
      token: this.config.apiKey
    };
  }
}

// Initialize bypass test
const bypassTest = new PostHogBypassTest({
  apiKey: apiKey,
  apiHost: apiHost
});

console.log('🔧 Bypass instance created:', {
  distinctId: bypassTest.get_distinct_id(),
  sessionId: bypassTest.get_session_id(),
  hasOptedOut: bypassTest.has_opted_out_capturing()
});

// Test bypass event sending
console.log('📤 Testing bypass event sending...');

const bypassTestEvent = {
  test_type: 'production_bypass_test',
  bypass_mode: true,
  timestamp: new Date().toISOString(),
  page_url: window.location.href,
  user_agent: navigator.userAgent,
  test_id: Math.random().toString(36).substring(7)
};

bypassTest.capture('production_bypass_test_event', bypassTestEvent)
  .then(() => {
    console.log('✅ Bypass test event sent successfully!');
    
    // Test 3: Send multiple events to verify batching
    console.log('\n=== Test 3: Multiple Events Test ===');
    
    const multipleEvents = [
      { event: 'bypass_pageview', data: { page: 'home', section: 'hero' } },
      { event: 'bypass_cta_click', data: { cta_type: 'contact', location: 'header' } },
      { event: 'bypass_navigation', data: { section: 'services', method: 'click' } }
    ];
    
    const promises = multipleEvents.map((eventInfo, index) => {
      return bypassTest.capture(eventInfo.event, {
        ...eventInfo.data,
        test_batch: true,
        event_index: index,
        batch_id: 'production_test_batch_' + Date.now()
      });
    });
    
    return Promise.all(promises);
  })
  .then(() => {
    console.log('✅ Multiple events sent successfully!');
    
    // Final summary
    console.log('\n=== Test Summary ===');
    console.log('✅ PostHog Bypass system is working in production!');
    console.log('📊 Events should appear in PostHog dashboard within 1-2 minutes');
    console.log('🔗 Check dashboard: https://us.posthog.com');
    console.log('🔍 Look for events:');
    console.log('  - production_bypass_test_event');
    console.log('  - bypass_pageview');
    console.log('  - bypass_cta_click');
    console.log('  - bypass_navigation');
    
    // Test 4: Verify the actual app's PostHog instance
    console.log('\n=== Test 4: App PostHog Instance Check ===');
    
    // Try to access the app's analytics functions
    if (typeof window.initializeAnalytics === 'function') {
      console.log('📊 App analytics functions found, testing...');
      
      // This would test the actual app's analytics
      try {
        window.initializeAnalytics().then((result) => {
          console.log('📊 App analytics initialization result:', result);
          
          if (typeof window.trackEvent === 'function') {
            window.trackEvent('cta_click', {
              cta_type: 'production_test',
              location: 'bypass_test_script',
              text: 'Bypass Test Event'
            });
            console.log('📊 App test event sent via trackEvent function');
          }
        }).catch((error) => {
          console.warn('⚠️ App analytics initialization failed:', error);
        });
      } catch (error) {
        console.warn('⚠️ Error testing app analytics:', error);
      }
    } else {
      console.log('📊 App analytics functions not found in global scope');
      console.log('🔧 This is normal - they may be scoped within modules');
    }
  })
  .catch((error) => {
    console.error('❌ Bypass test failed:', error);
    console.log('🔧 Possible issues:');
    console.log('  - Network connectivity problems');
    console.log('  - PostHog API endpoint changes');
    console.log('  - CORS policy restrictions');
    console.log('  - Firewall blocking requests');
  });

// Test 5: Network monitoring
console.log('\n=== Test 5: Network Request Monitoring ===');
console.log('🌐 Monitoring network requests for 30 seconds...');

const originalFetch = window.fetch;
let requestCount = 0;
const requests = [];

window.fetch = function(...args) {
  const url = args[0];
  if (typeof url === 'string' && (url.includes('posthog') || url.includes('i.posthog.com'))) {
    requestCount++;
    const requestInfo = {
      count: requestCount,
      url: url,
      method: args[1]?.method || 'GET',
      timestamp: new Date().toISOString()
    };
    requests.push(requestInfo);
    console.log(`🌐 PostHog request #${requestCount}:`, requestInfo);
  }
  return originalFetch.apply(this, args);
};

setTimeout(() => {
  window.fetch = originalFetch;
  console.log(`🌐 Network monitoring complete`);
  console.log(`📊 Total PostHog requests: ${requestCount}`);
  
  if (requestCount > 0) {
    console.log('✅ PostHog bypass is making network requests!');
    console.log('📋 Request summary:', requests);
  } else {
    console.warn('⚠️ No PostHog network requests detected');
    console.warn('🔧 This could indicate:');
    console.warn('  - Events are being queued for later sending');
    console.warn('  - Network requests are being blocked');
    console.warn('  - API endpoints are not accessible');
  }
  
  console.log('\n🎯 Final Instructions:');
  console.log('1. Check PostHog dashboard: https://us.posthog.com');
  console.log('2. Look for events with "bypass" or "production_test" in the name');
  console.log('3. Events should appear within 1-2 minutes');
  console.log('4. If events appear, the bypass system is working correctly!');
}, 30000);

console.log('⏱️ Test running... wait 30 seconds for complete results');