// Comprehensive Analytics Test Script
// Copy and paste this entire script into the browser console at http://localhost:3000

console.log('🚀 Starting Comprehensive Analytics Test');
console.log('📍 Current URL:', window.location.href);
console.log('🕐 Test started at:', new Date().toISOString());

// Test 1: Check if global analytics functions are available
console.log('\n=== Test 1: Global Functions Availability ===');

const functionsToCheck = [
  'analytics',
  'trackEvent', 
  'sendTestEvent',
  'getAnalyticsStatus',
  'initializeAnalytics',
  'isAnalyticsReady',
  'resetAnalytics'
];

const availableFunctions = {};
functionsToCheck.forEach(funcName => {
  const func = window[funcName];
  availableFunctions[funcName] = {
    exists: !!func,
    type: typeof func
  };
  console.log(`${funcName}:`, availableFunctions[funcName]);
});

// Test 2: Check analytics status
console.log('\n=== Test 2: Analytics Status ===');

if (window.getAnalyticsStatus) {
  try {
    const status = window.getAnalyticsStatus();
    console.log('📊 Analytics Status:', status);
    
    if (status.ready) {
      console.log(`✅ Analytics is ready in ${status.mode} mode`);
      console.log(`📈 Events sent so far: ${status.eventsSent}`);
      console.log(`🆔 Distinct ID: ${status.distinctId}`);
    } else {
      console.log('⚠️ Analytics not ready');
      console.log('Mode:', status.mode);
      console.log('Initialized:', status.initialized);
      if (status.lastError) {
        console.log('❌ Last error:', status.lastError);
      }
    }
  } catch (error) {
    console.error('❌ Error getting analytics status:', error);
  }
} else {
  console.log('⚠️ getAnalyticsStatus function not available');
}

// Test 3: Try to initialize analytics manually
console.log('\n=== Test 3: Manual Initialization ===');

if (window.initializeAnalytics) {
  console.log('🔄 Attempting manual initialization...');
  window.initializeAnalytics()
    .then(success => {
      console.log('Manual initialization result:', success);
      
      // Check status again after initialization
      if (window.getAnalyticsStatus) {
        const newStatus = window.getAnalyticsStatus();
        console.log('📊 Status after manual init:', newStatus);
      }
    })
    .catch(error => {
      console.error('❌ Manual initialization failed:', error);
    });
} else {
  console.log('⚠️ initializeAnalytics function not available');
}

// Test 4: Send test events
console.log('\n=== Test 4: Event Sending Tests ===');

const testId = 'comprehensive_test_' + Math.random().toString(36).substring(7);
console.log('🧪 Test ID:', testId);

// Test using sendTestEvent function
if (window.sendTestEvent) {
  console.log('📤 Sending test event using sendTestEvent()...');
  try {
    window.sendTestEvent();
    console.log('✅ sendTestEvent() called successfully');
  } catch (error) {
    console.error('❌ sendTestEvent() failed:', error);
  }
} else {
  console.log('⚠️ sendTestEvent function not available');
}

// Test using trackEvent function
if (window.trackEvent) {
  console.log('📤 Sending custom event using trackEvent()...');
  try {
    window.trackEvent('comprehensive_test', {
      test_id: testId,
      test_type: 'comprehensive_browser_test',
      location: 'browser_console',
      timestamp: new Date().toISOString(),
      url: window.location.href,
      user_agent: navigator.userAgent.substring(0, 50) + '...',
      screen_resolution: `${screen.width}x${screen.height}`,
      viewport_size: `${window.innerWidth}x${window.innerHeight}`
    });
    console.log('✅ trackEvent() called successfully with test_id:', testId);
  } catch (error) {
    console.error('❌ trackEvent() failed:', error);
  }
} else {
  console.log('⚠️ trackEvent function not available');
}

// Test using analytics object
if (window.analytics && window.analytics.trackEvent) {
  console.log('📤 Sending event using analytics.trackEvent()...');
  try {
    window.analytics.trackEvent('analytics_object_test', {
      test_id: testId + '_obj',
      test_type: 'analytics_object_test',
      method: 'analytics_object',
      timestamp: new Date().toISOString()
    });
    console.log('✅ analytics.trackEvent() called successfully');
  } catch (error) {
    console.error('❌ analytics.trackEvent() failed:', error);
  }
} else {
  console.log('⚠️ analytics.trackEvent method not available');
}

// Test 5: Monitor network requests
console.log('\n=== Test 5: Network Monitoring ===');

const originalFetch = window.fetch;
let postHogRequests = [];
let requestCount = 0;

// Override fetch to monitor PostHog requests
window.fetch = function(...args) {
  const url = args[0];
  if (typeof url === 'string' && (url.includes('posthog') || url.includes('i.posthog.com'))) {
    requestCount++;
    const requestInfo = {
      count: requestCount,
      url: url,
      method: args[1]?.method || 'GET',
      timestamp: new Date().toISOString(),
      body: args[1]?.body ? 'Present' : 'None'
    };
    postHogRequests.push(requestInfo);
    console.log(`🌐 PostHog request #${requestCount}:`, requestInfo);
  }
  return originalFetch.apply(this, args);
};

// Test 6: Check health API
console.log('\n=== Test 6: Health API Check ===');

fetch('/api/health')
  .then(response => response.json())
  .then(health => {
    console.log('🏥 Health API Response:');
    console.log('Overall status:', health.status);
    console.log('Analytics service:', health.services?.analytics);
    
    if (health.services?.analytics?.details?.globalAnalytics) {
      console.log('Global Analytics details:', health.services.analytics.details.globalAnalytics);
    }
    
    if (health.services?.analytics?.status === 'up') {
      console.log('✅ Analytics service reported as UP');
    } else {
      console.log('⚠️ Analytics service status:', health.services?.analytics?.status);
    }
  })
  .catch(error => {
    console.error('❌ Health API check failed:', error);
  });

// Test 7: Check for PostHog script loading
console.log('\n=== Test 7: PostHog Script Detection ===');

const postHogScripts = document.querySelectorAll('script[src*="posthog"]');
console.log('PostHog scripts found:', postHogScripts.length);

if (postHogScripts.length > 0) {
  postHogScripts.forEach((script, index) => {
    console.log(`Script ${index + 1}:`, script.src);
  });
} else {
  console.log('No PostHog scripts detected (expected with bypass mode)');
}

// Check for global PostHog object
console.log('Global posthog object:', typeof window.posthog);
if (window.posthog) {
  console.log('PostHog loaded:', !!window.posthog.__loaded);
  console.log('PostHog distinct ID:', window.posthog.get_distinct_id?.());
}

// Test 8: Final results after delay
console.log('\n=== Test 8: Final Results (in 10 seconds) ===');

setTimeout(() => {
  // Restore original fetch
  window.fetch = originalFetch;
  
  console.log('\n🎯 FINAL TEST RESULTS');
  console.log('='.repeat(50));
  
  // Network requests summary
  console.log(`📊 PostHog network requests detected: ${requestCount}`);
  if (requestCount > 0) {
    console.log('✅ Network requests are being made!');
    console.log('Request details:', postHogRequests);
  } else {
    console.log('⚠️ No network requests detected');
  }
  
  // Final status check
  if (window.getAnalyticsStatus) {
    const finalStatus = window.getAnalyticsStatus();
    console.log('📊 Final analytics status:', finalStatus);
    
    if (finalStatus.ready) {
      console.log(`✅ Analytics system is working in ${finalStatus.mode} mode`);
      console.log(`📈 Total events sent: ${finalStatus.eventsSent}`);
    } else {
      console.log('❌ Analytics system is not ready');
      console.log('Troubleshooting info:');
      console.log('- Mode:', finalStatus.mode);
      console.log('- Initialized:', finalStatus.initialized);
      console.log('- Last Error:', finalStatus.lastError);
    }
  }
  
  console.log('\n🎯 Next Steps:');
  console.log('1. Check PostHog dashboard: https://us.posthog.com');
  console.log('2. Look for events with test_id:', testId);
  console.log('3. Events should appear within 1-2 minutes');
  console.log('4. If events appear, the system is working!');
  
  console.log('\n📊 Test completed at:', new Date().toISOString());
  
  // Summary
  const workingFunctions = Object.entries(availableFunctions)
    .filter(([name, info]) => info.exists && info.type === 'function')
    .map(([name]) => name);
    
  console.log('\n📋 SUMMARY:');
  console.log(`✅ Working functions: ${workingFunctions.join(', ')}`);
  console.log(`📡 Network requests: ${requestCount}`);
  console.log(`🎯 Test ID to look for: ${testId}`);
  
}, 10000);

console.log('\n📝 Instructions:');
console.log('1. Wait for all tests to complete (about 15 seconds)');
console.log('2. Watch for "PostHog request" messages');
console.log('3. Check the final results summary');
console.log('4. Look for your test events in PostHog dashboard');

console.log('\n⏱️ Test will complete in ~15 seconds...');