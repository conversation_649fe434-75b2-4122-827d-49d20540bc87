# =============================================================================
# J&A Business Solutions - Production Environment Configuration
# =============================================================================
# This file contains production-specific environment variables
# Ensure all sensitive values are properly secured and not committed to version control

# =============================================================================
# Application Configuration
# =============================================================================
NODE_ENV=production
PORT=3000
HOSTNAME=0.0.0.0

# Next.js Configuration
NEXT_TELEMETRY_DISABLED=1
NEXT_PUBLIC_APP_ENV=production

# =============================================================================
# Supabase Configuration (Production)
# =============================================================================
# Production Supabase project credentials - proptech project
# Project ID: bdzvmmnbtwsfvrqepjuu
# Region: us-east-2
# Status: ACTIVE_HEALTHY
NEXT_PUBLIC_SUPABASE_URL=https://bdzvmmnbtwsfvrqepjuu.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.j5Fanbr-kiqPdsCmJf736qP2ZT5LZQwo0p3-fmCyUbc

# =============================================================================
# Sentry Configuration (Production Error Monitoring)
# =============================================================================
# Production Sentry DSN for error tracking and performance monitoring
# Get these from your Sentry production project settings
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/4509747857260544
NEXT_PUBLIC_SENTRY_ENVIRONMENT=production

# Sentry build configuration for source maps upload
SENTRY_ORG=atemkeng
SENTRY_PROJECT=proptech
SENTRY_AUTH_TOKEN=***********************************************************************

# Sentry performance monitoring configuration
SENTRY_TRACES_SAMPLE_RATE=0.1
SENTRY_PROFILES_SAMPLE_RATE=0.1

# =============================================================================
# PostHog Configuration (Production Analytics)
# =============================================================================
# Production PostHog API key for user analytics and feature flags
# Get these from your PostHog production project settings
# TODO: Replace with actual production PostHog API key
NEXT_PUBLIC_POSTHOG_API_KEY=phc_yErhnRXWMQkP4hO7nU37vDRE72w3KcgyUA5CCXvRdP1
NEXT_PUBLIC_POSTHOG_API_HOST=https://us.i.posthog.com
NEXT_PUBLIC_POSTHOG_PROJECT=Readme

# PostHog configuration
POSTHOG_CAPTURE_PAGEVIEW=true
POSTHOG_CAPTURE_PAGELEAVE=true

# =============================================================================
# Security Configuration
# =============================================================================
# Security headers and CORS configuration
CORS_ORIGIN=https://jnabusinesssolutions.com
TRUSTED_HOSTS=jnabusinesssolutions.com,www.jnabusinesssolutions.com

# Content Security Policy
CSP_REPORT_URI=https://jnabusinesssolutions.com/api/csp-report

# =============================================================================
# Performance Configuration
# =============================================================================
# Image optimization and caching
NEXT_PUBLIC_IMAGE_DOMAINS=jnabusinesssolutions.com,supabase.co
CACHE_MAX_AGE=31536000
# https://bdzvmmnbtwsfvrqepjuu.supabase.co

# =============================================================================
# Logging Configuration
# =============================================================================
# Production logging levels and destinations
LOG_LEVEL=error
LOG_FORMAT=json
ENABLE_REQUEST_LOGGING=true

# =============================================================================
# Health Check Configuration
# =============================================================================
# Health check endpoint configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_RETRIES=3

# =============================================================================
# Database Configuration
# =============================================================================
# Database connection settings for production
DB_CONNECTION_TIMEOUT=10000
DB_MAX_CONNECTIONS=20
DB_IDLE_TIMEOUT=30000

# =============================================================================
# External Services Configuration
# =============================================================================
# Rate limiting for external API calls
API_RATE_LIMIT=100
API_RATE_WINDOW=60000

# =============================================================================
# PWA Configuration
# =============================================================================
# Progressive Web App settings for production
PWA_CACHE_STRATEGY=CacheFirst
PWA_OFFLINE_FALLBACK=true
SERVICE_WORKER_ENABLED=true

# =============================================================================
# Build Configuration
# =============================================================================
# Production build optimizations
ANALYZE_BUNDLE=false
GENERATE_SOURCEMAP=false
MINIFY_CSS=true
MINIFY_JS=true

# =============================================================================
# Monitoring and Alerting
# =============================================================================
# Application monitoring configuration
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ERROR_REPORTING=true
ALERT_WEBHOOK_URL=your-alert-webhook-url

# =============================================================================
# Feature Flags
# =============================================================================
# Production feature flags
FEATURE_CONTACT_FORM=true
FEATURE_PWA_INSTALL=true
FEATURE_ANALYTICS_DEBUG=false
FEATURE_SENTRY_DEBUG=false

# =============================================================================
# Container Configuration
# =============================================================================
# Docker container specific settings
CONTAINER_NAME=jna-business-solutions-prod
RESTART_POLICY=unless-stopped
MEMORY_LIMIT=1g
CPU_LIMIT=1.0