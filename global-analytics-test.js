// Global Analytics Test Script (v1.0)
// This test works with the new global analytics system
// Copy and paste this into the browser console on https://jnabusinesssolutions.com

console.log('🚀 Testing Global Analytics System (v1.0)');
console.log('📍 Current URL:', window.location.href);
console.log('🕐 Test started at:', new Date().toISOString());

// Test 1: Check if global analytics functions are available
console.log('\n=== Test 1: Global Analytics Detection ===');

const analytics = window.analytics;
const trackEvent = window.trackEvent;
const sendTestEvent = window.sendTestEvent;
const getAnalyticsStatus = window.getAnalyticsStatus;
const initializeAnalytics = window.initializeAnalytics;

console.log('📊 Global analytics functions available:', {
  analytics: !!analytics,
  trackEvent: typeof trackEvent === 'function',
  sendTestEvent: typeof sendTestEvent === 'function',
  getAnalyticsStatus: typeof getAnalyticsStatus === 'function',
  initializeAnalytics: typeof initializeAnalytics === 'function'
});

// Test 2: Get analytics status
console.log('\n=== Test 2: Analytics Status Check ===');

if (getAnalyticsStatus) {
  const status = getAnalyticsStatus();
  console.log('📊 Analytics status:', status);
  
  if (status.ready) {
    console.log(`✅ Analytics is ready in ${status.mode} mode`);
    console.log(`📈 Events sent so far: ${status.eventsSent}`);
    console.log(`🆔 Distinct ID: ${status.distinctId}`);
  } else {
    console.log('⚠️ Analytics not ready yet');
    if (status.lastError) {
      console.log('❌ Last error:', status.lastError);
    }
  }
} else {
  console.log('⚠️ getAnalyticsStatus function not available');
}

// Test 3: Send test events
console.log('\n=== Test 3: Event Sending Tests ===');

const testId = 'test_' + Math.random().toString(36).substring(7);

// Test using sendTestEvent function
if (sendTestEvent) {
  console.log('🧪 Sending test event using sendTestEvent()...');
  sendTestEvent();
} else {
  console.log('⚠️ sendTestEvent function not available');
}

// Test using trackEvent function
if (trackEvent) {
  console.log('📤 Sending custom event using trackEvent()...');
  trackEvent('global_analytics_test', {
    test_id: testId,
    test_type: 'browser_console_test',
    location: 'browser_console',
    timestamp: new Date().toISOString(),
    url: window.location.href,
    user_agent: navigator.userAgent.substring(0, 50) + '...'
  });
  console.log('✅ Custom event sent with test_id:', testId);
} else {
  console.log('⚠️ trackEvent function not available');
}

// Test using analytics object
if (analytics && analytics.trackEvent) {
  console.log('📤 Sending event using analytics.trackEvent()...');
  analytics.trackEvent('analytics_object_test', {
    test_id: testId + '_obj',
    test_type: 'analytics_object_test',
    method: 'analytics_object',
    timestamp: new Date().toISOString()
  });
  console.log('✅ Analytics object event sent');
} else {
  console.log('⚠️ analytics.trackEvent method not available');
}

// Test 4: Monitor network activity
console.log('\n=== Test 4: Network Activity Monitoring ===');

const originalFetch = window.fetch;
let postHogRequests = [];
let requestCount = 0;

// Override fetch to monitor PostHog requests
window.fetch = function(...args) {
  const url = args[0];
  if (typeof url === 'string' && (url.includes('posthog') || url.includes('i.posthog.com'))) {
    requestCount++;
    const requestInfo = {
      count: requestCount,
      url: url,
      method: args[1]?.method || 'GET',
      timestamp: new Date().toISOString()
    };
    postHogRequests.push(requestInfo);
    console.log(`🌐 PostHog request #${requestCount}:`, requestInfo);
  }
  return originalFetch.apply(this, args);
};

// Test 5: Check health endpoint
console.log('\n=== Test 5: Health Check ===');

fetch('/api/health')
  .then(response => response.json())
  .then(health => {
    console.log('🏥 Application health status:', {
      status: health.status,
      analytics: health.services?.analytics?.status,
      posthog: health.services?.analytics?.posthog,
      globalAnalytics: health.services?.analytics?.details?.globalAnalytics,
      version: health.version,
      environment: health.environment
    });
    
    if (health.services?.analytics?.status === 'up') {
      console.log('✅ Analytics service is reported as UP in health check');
    } else {
      console.log('⚠️ Analytics service status:', health.services?.analytics?.status);
    }
  })
  .catch(error => {
    console.warn('⚠️ Could not fetch health status:', error.message);
  });

// Test 6: Wait and show results
console.log('\n=== Test 6: Results Summary (in 15 seconds) ===');

setTimeout(() => {
  // Restore original fetch
  window.fetch = originalFetch;
  
  console.log('\n=== FINAL TEST RESULTS ===');
  console.log(`📊 Total PostHog requests detected: ${requestCount}`);
  
  if (requestCount > 0) {
    console.log('✅ PostHog analytics system is making network requests!');
    console.log('📋 Request details:', postHogRequests);
    console.log('🎯 This indicates the analytics system is working correctly');
  } else {
    console.log('⚠️ No PostHog network requests detected');
    console.log('🔧 Possible reasons:');
    console.log('  - Events are being queued for batch sending');
    console.log('  - Network requests are being blocked at a deeper level');
    console.log('  - Analytics initialization is still in progress');
  }
  
  // Get final status
  if (getAnalyticsStatus) {
    const finalStatus = getAnalyticsStatus();
    console.log('📊 Final analytics status:', finalStatus);
    
    if (finalStatus.ready) {
      console.log(`✅ Analytics system is ready and working in ${finalStatus.mode} mode`);
      console.log(`📈 Total events sent: ${finalStatus.eventsSent}`);
    } else {
      console.log('⚠️ Analytics system is not ready');
    }
  }
  
  console.log('\n🎯 Next Steps:');
  console.log('1. Check PostHog dashboard: https://us.posthog.com');
  console.log('2. Look for events with test_id:', testId);
  console.log('3. Events should appear within 1-2 minutes');
  console.log('4. If events appear, the analytics system is working correctly!');
  
  console.log('\n📊 Test completed at:', new Date().toISOString());
}, 15000);

console.log('\n📝 Instructions:');
console.log('1. Wait for all tests to complete (about 20 seconds)');
console.log('2. Look for "PostHog request" messages indicating network activity');
console.log('3. Check your PostHog dashboard for test events');
console.log('4. Events with test_id "' + testId + '" confirm the system is working');

console.log('\n⏱️ Full test will complete in ~20 seconds...');