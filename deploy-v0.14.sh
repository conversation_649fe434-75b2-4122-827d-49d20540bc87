#!/bin/bash

# Deploy v0.14 with PostHog Analytics Fix
# This script updates the production deployment to use the new Docker image

set -e

echo "🚀 Deploying J&A Business Solutions v0.14 with PostHog Analytics Fix"
echo "=================================================================="

# Check if docker-compose.prod.yml exists
if [ ! -f "docker-compose.prod.yml" ]; then
    echo "❌ Error: docker-compose.prod.yml not found"
    exit 1
fi

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    echo "❌ Error: .env.production not found"
    exit 1
fi

echo "📋 Current deployment status:"
docker-compose -f docker-compose.prod.yml ps || echo "No containers running"

echo ""
echo "🛑 Stopping current production containers..."
docker-compose -f docker-compose.prod.yml down

echo ""
echo "🧹 Cleaning up old images (keeping last 2 versions)..."
docker images atemndobs/jna-amd64 --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}\t{{.Size}}"

echo ""
echo "📥 Pulling latest image v0.14..."
docker pull atemndobs/jna-amd64:v0.14

echo ""
echo "🚀 Starting new deployment with v0.14..."
docker-compose -f docker-compose.prod.yml up -d

echo ""
echo "⏳ Waiting for services to start..."
sleep 10

echo ""
echo "🔍 Checking deployment status..."
docker-compose -f docker-compose.prod.yml ps

echo ""
echo "🏥 Running health check..."
echo "Waiting 30 seconds for application to fully start..."
sleep 30

# Test the health endpoint
echo "Testing health endpoint..."
if curl -f -s https://jnabusinesssolutions.com/api/health > /dev/null; then
    echo "✅ Health check passed"
    
    # Test analytics specifically
    echo "Testing analytics status..."
    ANALYTICS_STATUS=$(curl -s https://jnabusinesssolutions.com/api/health | jq -r '.services.analytics.status' 2>/dev/null || echo "unknown")
    echo "Analytics status: $ANALYTICS_STATUS"
    
    if [ "$ANALYTICS_STATUS" = "up" ]; then
        echo "✅ PostHog analytics is working!"
    else
        echo "⚠️  Analytics status is: $ANALYTICS_STATUS"
        echo "📊 Full analytics details:"
        curl -s https://jnabusinesssolutions.com/api/health | jq '.services.analytics' 2>/dev/null || echo "Could not parse analytics status"
    fi
else
    echo "❌ Health check failed"
    echo "📋 Container logs:"
    docker-compose -f docker-compose.prod.yml logs --tail=20
    exit 1
fi

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📊 To test PostHog analytics:"
echo "   1. Visit: https://jnabusinesssolutions.com?debug=true"
echo "   2. Look for the Analytics Debugger in the bottom-right corner"
echo "   3. Click 'Check Status' and 'Send Test' buttons"
echo "   4. Check your PostHog dashboard for events"
echo ""
echo "🔧 Useful commands:"
echo "   View logs: docker-compose -f docker-compose.prod.yml logs -f"
echo "   Check status: docker-compose -f docker-compose.prod.yml ps"
echo "   Health check: curl -s https://jnabusinesssolutions.com/api/health | jq"
echo ""
echo "✅ Deployment v0.14 is live!"