/**
 * Global setup for Playwright tests
 * Enhanced setup with environment validation and data management
 */

import { chromium, FullConfig } from '@playwright/test';
import { EnvironmentHelper, TestDataManager } from './utils/base-test';

async function globalSetup(_config: FullConfig) {
  console.log('🚀 Starting global test setup...');
  
  // Environment validation
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 Base URL: ${EnvironmentHelper.getBaseURL()}`);
  console.log(`🔄 Retries: ${EnvironmentHelper.getRetries()}`);
  console.log(`⏱️  Timeout: ${EnvironmentHelper.getTimeout()}ms`);
  
  // Verify that the development server is running
  const browser = await chromium.launch({ headless: true });
  const page = await browser.newPage();
  
  try {
    console.log('🔍 Checking development server...');
    
    // Wait for the development server to be ready
    await page.goto(EnvironmentHelper.getBaseURL(), { 
      waitUntil: 'networkidle',
      timeout: 60000 
    });
    
    // Verify essential elements are present
    const title = await page.title();
    console.log(`📄 Page title: ${title}`);
    
    // Check for React hydration
    await page.waitForFunction(() => {
      return window.React !== undefined || document.querySelector('[data-reactroot]') !== null;
    }, { timeout: 10000 }).catch(() => {
      console.warn('⚠️  React hydration check failed - continuing anyway');
    });
    
    // Check for essential sections
    const sections = ['#home', '#services', '#contact'];
    for (const section of sections) {
      const element = await page.locator(section).first();
      const isVisible = await element.isVisible().catch(() => false);
      if (isVisible) {
        console.log(`✅ Section ${section} is present`);
      } else {
        console.warn(`⚠️  Section ${section} not found`);
      }
    }
    
    console.log('✅ Development server is ready and responsive');
    
  } catch (error) {
    console.error('❌ Failed to connect to development server:', error);
    console.error('💡 Make sure to run "npm run dev" before running tests');
    throw error;
  } finally {
    await browser.close();
  }
  
  // Clean up any existing test data
  try {
    await TestDataManager.cleanupTestData();
    console.log('🧹 Test data cleanup completed');
  } catch (error) {
    console.warn('⚠️  Test data cleanup failed:', error);
  }
  
  // Set up test environment variables if needed
  if (!process.env.PLAYWRIGHT_BASE_URL) {
    process.env.PLAYWRIGHT_BASE_URL = EnvironmentHelper.getBaseURL();
  }
  
  console.log('✅ Global test setup completed successfully');
  console.log('🎭 Ready to run Playwright tests!');
}

export default globalSetup;