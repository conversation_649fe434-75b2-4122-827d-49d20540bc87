/**
 * Test utilities and helper functions for Playwright tests
 */

import { Page, expect, Locator } from '@playwright/test';

/**
 * Viewport configurations for responsive testing
 */
export const VIEWPORTS = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  desktop: { width: 1280, height: 720 },
  largeDesktop: { width: 1920, height: 1080 }
} as const;

/**
 * Common selectors used across tests
 */
export const SELECTORS = {
  navigation: {
    desktopNav: 'nav.hidden.md\\:flex',
    mobileMenuButton: 'button[type="button"].md\\:hidden',
    mobileMenu: '.md\\:hidden.bg-white.absolute',
    navLink: (href: string) => `nav a[href="${href}"]`,
    phoneLink: 'a[href="tel:+***********"]'
  },
  sections: {
    home: '#home',
    propertyOwners: '#property-owners',
    services: '#services',
    testimonials: '#testimonials',
    about: '#about',
    contact: '#contact'
  },
  contact: {
    emailLink: 'a[href="mailto:<EMAIL>"]',
    phoneNumber: 'text=+****************',
    address: 'text=7484 S Camino Cardal'
  }
} as const;

/**
 * Test data constants
 */
export const TEST_DATA = {
  company: {
    name: 'J&A Business Solutions LLC',
    phone: '+****************',
    email: '<EMAIL>',
    address: '7484 S Camino Cardal, Tucson, AZ 85756-0058'
  },
  teamMembers: [
    'Juliane Schlegel',
    'Bertrand Atemkeng',
    'Randalls Chabeja'
  ],
  services: [
    'Luxury Short-Term Rentals',
    'Flexible Booking',
    'Corporate Housing',
    'Concierge Services'
  ],
  propertyOwnerFeatures: [
    'Corporate Guest Portfolio',
    'Guaranteed Rental Income',
    'Proactive Asset Maintenance',
    'Comprehensive Insurance Protection',
    'Long-Term Occupancy Stability',
    'Professional Communication & Oversight'
  ],
  testimonials: [
    'Sarah Johnson',
    'Michael Thompson',
    'Rebecca Chen'
  ]
} as const;

/**
 * Navigation helper class for handling mobile vs desktop navigation
 */
export class NavigationHelper {
  constructor(private page: Page) {}

  /**
   * Check if the current viewport is mobile
   */
  async isMobile(): Promise<boolean> {
    return await this.page.evaluate(() => window.innerWidth < 768);
  }

  /**
   * Click a navigation link, handling mobile menu if necessary
   */
  async clickNavLink(href: string): Promise<void> {
    const isMobile = await this.isMobile();
    
    if (isMobile) {
      // Open mobile menu first
      const menuButton = this.page.locator(SELECTORS.navigation.mobileMenuButton);
      await menuButton.click();
      await this.page.waitForTimeout(500);
      
      // Wait for mobile menu to appear
      const mobileMenu = this.page.locator(SELECTORS.navigation.mobileMenu);
      await mobileMenu.waitFor({ state: 'visible', timeout: 2000 });
      
      // Click the nav link in the opened mobile menu
      const mobileNavLink = mobileMenu.locator(`a[href="${href}"]`);
      await mobileNavLink.click();
    } else {
      // Click desktop nav link
      await this.page.locator(SELECTORS.navigation.navLink(href)).first().click();
    }
    await this.page.waitForTimeout(300);
  }

  /**
   * Verify that a section is in viewport after navigation
   */
  async verifySectionInView(sectionId: string): Promise<void> {
    const section = this.page.locator(sectionId);
    await expect(section).toBeInViewport();
  }

  /**
   * Navigate to a section and verify it's in view
   */
  async navigateToSection(href: string, sectionId: string): Promise<void> {
    await this.clickNavLink(href);
    await this.verifySectionInView(sectionId);
  }
}

/**
 * Responsive testing helper class
 */
export class ResponsiveHelper {
  constructor(private page: Page) {}

  /**
   * Set viewport to specific size
   */
  async setViewport(viewport: keyof typeof VIEWPORTS): Promise<void> {
    await this.page.setViewportSize(VIEWPORTS[viewport]);
  }

  /**
   * Test element visibility across different viewports
   */
  async testElementAcrossViewports(
    selector: string,
    viewports: (keyof typeof VIEWPORTS)[] = ['mobile', 'tablet', 'desktop']
  ): Promise<void> {
    for (const viewport of viewports) {
      await this.setViewport(viewport);
      const element = this.page.locator(selector);
      await expect(element).toBeVisible();
    }
  }

  /**
   * Test responsive layout changes
   */
  async testResponsiveLayout(
    mobileSelector: string,
    desktopSelector: string
  ): Promise<void> {
    // Test mobile layout
    await this.setViewport('mobile');
    await expect(this.page.locator(mobileSelector)).toBeVisible();
    await expect(this.page.locator(desktopSelector)).not.toBeVisible();

    // Test desktop layout
    await this.setViewport('desktop');
    await expect(this.page.locator(desktopSelector)).toBeVisible();
    await expect(this.page.locator(mobileSelector)).not.toBeVisible();
  }
}

/**
 * Performance testing helper class
 */
export class PerformanceHelper {
  constructor(private page: Page) {}

  /**
   * Measure page load time
   */
  async measurePageLoad(): Promise<number> {
    const startTime = Date.now();
    await this.page.goto('/', { waitUntil: 'networkidle' });
    return Date.now() - startTime;
  }

  /**
   * Check Core Web Vitals
   */
  async getCoreWebVitals(): Promise<{
    lcp: number;
    fid: number;
    cls: number;
  }> {
    return await this.page.evaluate(() => {
      return new Promise((resolve) => {
        const vitals = { lcp: 0, fid: 0, cls: 0 };
        
        // LCP (Largest Contentful Paint)
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          vitals.lcp = lastEntry.startTime;
        }).observe({ entryTypes: ['largest-contentful-paint'] });

        // CLS (Cumulative Layout Shift)
        new PerformanceObserver((list) => {
          let clsValue = 0;
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value;
            }
          }
          vitals.cls = clsValue;
        }).observe({ entryTypes: ['layout-shift'] });

        // FID (First Input Delay) - approximated
        vitals.fid = performance.now();
        
        setTimeout(() => resolve(vitals), 2000);
      });
    });
  }

  /**
   * Check if page loads within acceptable time
   */
  async verifyPageLoadPerformance(maxLoadTime: number = 5000): Promise<void> {
    const loadTime = await this.measurePageLoad();
    expect(loadTime).toBeLessThan(maxLoadTime);
  }
}

/**
 * Accessibility testing helper class
 */
export class AccessibilityHelper {
  constructor(private page: Page) {}

  /**
   * Check heading hierarchy
   */
  async verifyHeadingHierarchy(): Promise<void> {
    const headings = await this.page.locator('h1, h2, h3, h4, h5, h6').all();
    
    // Should have exactly one h1
    const h1Count = await this.page.locator('h1').count();
    expect(h1Count).toBe(1);

    // Check that headings follow proper hierarchy
    let previousLevel = 0;
    for (const heading of headings) {
      const tagName = await heading.evaluate(el => el.tagName.toLowerCase());
      const currentLevel = parseInt(tagName.charAt(1));
      
      if (previousLevel > 0) {
        // Next heading should not skip levels (e.g., h2 to h4)
        expect(currentLevel - previousLevel).toBeLessThanOrEqual(1);
      }
      previousLevel = currentLevel;
    }
  }

  /**
   * Check that all images have alt text
   */
  async verifyImageAltText(): Promise<void> {
    const images = await this.page.locator('img').all();
    
    for (const img of images) {
      const alt = await img.getAttribute('alt');
      expect(alt).toBeTruthy();
      expect(alt?.length).toBeGreaterThan(0);
    }
  }

  /**
   * Check keyboard navigation
   */
  async verifyKeyboardNavigation(): Promise<void> {
    // Focus on first interactive element
    await this.page.keyboard.press('Tab');
    
    // Check that focus is visible
    const focusedElement = await this.page.evaluate(() => document.activeElement?.tagName);
    expect(focusedElement).toBeTruthy();
  }

  /**
   * Check ARIA labels and roles
   */
  async verifyAriaLabels(): Promise<void> {
    // Check that buttons have accessible names
    const buttons = await this.page.locator('button').all();
    
    for (const button of buttons) {
      const ariaLabel = await button.getAttribute('aria-label');
      const textContent = await button.textContent();
      const title = await button.getAttribute('title');
      
      // Button should have either aria-label, text content, or title
      const hasAccessibleName = !!(ariaLabel || (textContent && textContent.trim()) || title);
      
      if (!hasAccessibleName) {
        // Log the button for debugging
        const outerHTML = await button.evaluate(el => el.outerHTML);
        console.warn('Button without accessible name:', outerHTML);
      }
      
      // Only fail if it's a visible, interactive button without any accessible name
      const isVisible = await button.isVisible();
      if (isVisible) {
        expect(hasAccessibleName).toBe(true);
      }
    }
  }
}

/**
 * Form testing helper class
 */
export class FormHelper {
  constructor(private page: Page) {}

  /**
   * Fill form field and verify it was filled
   */
  async fillField(selector: string, value: string): Promise<void> {
    const field = this.page.locator(selector);
    await field.fill(value);
    await expect(field).toHaveValue(value);
  }

  /**
   * Submit form and wait for response
   */
  async submitForm(_formSelector: string, submitButtonSelector: string): Promise<void> {
    const submitButton = this.page.locator(submitButtonSelector);
    
    await submitButton.click();
    
    // Wait for form submission to complete
    await this.page.waitForTimeout(1000);
  }

  /**
   * Verify form validation messages
   */
  async verifyValidationMessage(selector: string, expectedMessage: string): Promise<void> {
    const validationMessage = this.page.locator(selector);
    await expect(validationMessage).toContainText(expectedMessage);
  }
}

/**
 * Wait utilities
 */
export class WaitHelper {
  constructor(private page: Page) {}

  /**
   * Wait for element to be visible and stable
   */
  async waitForElementStable(selector: string, timeout: number = 5000): Promise<Locator> {
    const element = this.page.locator(selector);
    await element.waitFor({ state: 'visible', timeout });
    await this.page.waitForTimeout(100); // Small delay for stability
    return element;
  }

  /**
   * Wait for page to be fully loaded and handle any blocking modals
   */
  async waitForPageLoad(): Promise<void> {
    await this.page.waitForLoadState('networkidle');
    
    // Handle privacy consent modal if it appears
    await this.handlePrivacyModal();
    
    await this.page.waitForTimeout(500); // Additional stability wait
  }

  /**
   * Handle privacy consent modal by accepting all cookies
   */
  async handlePrivacyModal(): Promise<void> {
    try {
      // Wait for privacy modal to appear (it shows after 2 seconds)
      const privacyModal = this.page.locator('.fixed.inset-0.z-50.flex.items-end.justify-center');
      
      // Check if modal is visible with a reasonable timeout
      const isModalVisible = await privacyModal.isVisible().catch(() => false);
      
      if (!isModalVisible) {
        // Wait a bit longer for the modal to appear
        await privacyModal.waitFor({ state: 'visible', timeout: 3000 }).catch(() => {
          // Modal didn't appear, that's fine
        });
      }
      
      // If modal is now visible, handle it
      if (await privacyModal.isVisible()) {
        // Click "Accept All" button
        const acceptButton = privacyModal.locator('button:has-text("Accept All")');
        await acceptButton.click();
        
        // Wait for modal to disappear
        await privacyModal.waitFor({ state: 'hidden', timeout: 2000 });
      }
    } catch (error) {
      // Privacy modal handling failed, but this is not critical
      // Continue with the test
    }
  }

  /**
   * Wait for smooth scroll to complete
   */
  async waitForSmoothScroll(): Promise<void> {
    await this.page.waitForTimeout(800); // Smooth scroll animation time
  }
}

/**
 * Screenshot and debugging helper
 */
export class DebugHelper {
  constructor(private page: Page) {}

  /**
   * Take screenshot with timestamp
   */
  async takeScreenshot(name: string): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    await this.page.screenshot({ 
      path: `test-results/debug-${name}-${timestamp}.png`,
      fullPage: true 
    });
  }

  /**
   * Log page console messages
   */
  async logConsoleMessages(): Promise<void> {
    this.page.on('console', msg => {
      console.log(`Console ${msg.type()}: ${msg.text()}`);
    });
  }

  /**
   * Log network requests
   */
  async logNetworkRequests(): Promise<void> {
    this.page.on('request', request => {
      console.log(`Request: ${request.method()} ${request.url()}`);
    });
    
    this.page.on('response', response => {
      console.log(`Response: ${response.status()} ${response.url()}`);
    });
  }
}