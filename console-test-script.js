// Copy and paste this entire script into your browser console to test PostHog

console.log("🧪 Starting PostHog console test...");

// Get environment variables (these should be available in the browser)
const apiKey = "phc_yErhnRXWMQkP4hO7nU37vDRE72w3KcgyUA5CCXvRdP1"; // Your actual API key
const apiHost = "https://us.i.posthog.com";

console.log("🔑 Using API Key:", apiKey.substring(0, 10) + "...");
console.log("🌐 Using API Host:", apiHost);

// Check if PostHog is already loaded
if (window.posthog && window.posthog.__loaded) {
  console.log("✅ PostHog already loaded, testing existing instance...");

  const posthog = window.posthog;

  console.log("📊 Current PostHog status:", {
    distinctId: posthog.get_distinct_id?.(),
    hasOptedOut: posthog.has_opted_out_capturing?.(),
    apiHost: posthog.config?.api_host,
    apiKey: posthog.config?.token?.substring(0, 10) + "...",
  });

  // Force opt-in if needed
  if (posthog.has_opted_out_capturing?.()) {
    console.log("📊 PostHog is opted out, forcing opt-in...");
    posthog.opt_in_capturing();
    console.log("📊 Opt-in result:", !posthog.has_opted_out_capturing?.());
  }

  // Send test event
  const testEvent = {
    timestamp: new Date().toISOString(),
    test_type: "console_script",
    random_id: Math.random().toString(36).substring(7),
    page_url: window.location.href,
  };

  console.log("📤 Sending test event:", testEvent);
  posthog.capture("console_test_event", testEvent);
  console.log("✅ Test event sent! Check your PostHog dashboard.");
} else {
  console.log("⚠️ PostHog not loaded, initializing fresh instance...");

  // Load PostHog dynamically
  const script = document.createElement("script");
  script.src = "https://app.posthog.com/static/array.js";
  script.onload = function () {
    console.log("📦 PostHog script loaded");

    // Initialize PostHog
    window.posthog.init(apiKey, {
      api_host: apiHost,
      debug: true,
      opt_out_capturing_by_default: false,
      request_batching: false,

      loaded: function (posthog) {
        console.log("✅ PostHog initialized successfully!");
        console.log("📊 PostHog info:", {
          distinctId: posthog.get_distinct_id(),
          hasOptedOut: posthog.has_opted_out_capturing(),
          apiHost: posthog.config.api_host,
        });

        // Send test event
        const testEvent = {
          timestamp: new Date().toISOString(),
          test_type: "console_fresh_init",
          random_id: Math.random().toString(36).substring(7),
          page_url: window.location.href,
        };

        console.log("📤 Sending test event:", testEvent);
        posthog.capture("console_fresh_test_event", testEvent);
        console.log("✅ Test event sent! Check your PostHog dashboard.");
      },

      before_send: function (event) {
        console.log("📤 PostHog sending event to server:", event);
        return event;
      },
    });
  };

  script.onerror = function () {
    console.error("❌ Failed to load PostHog script");
  };

  document.head.appendChild(script);
}

// Monitor network requests for 10 seconds
console.log("🌐 Monitoring network requests for 10 seconds...");
const originalFetch = window.fetch;
let requestCount = 0;

window.fetch = function (...args) {
  const url = args[0];
  if (
    typeof url === "string" &&
    (url.includes("posthog") || url.includes("i.posthog.com"))
  ) {
    requestCount++;
    console.log(`🌐 PostHog network request #${requestCount}:`, url);
  }
  return originalFetch.apply(this, args);
};

setTimeout(() => {
  window.fetch = originalFetch;
  console.log(
    `🌐 Network monitoring complete. Total PostHog requests: ${requestCount}`
  );

  if (requestCount === 0) {
    console.warn(
      "⚠️ No PostHog network requests detected. This might indicate:"
    );
    console.warn("  - Events are being blocked by ad blocker");
    console.warn("  - Network connectivity issues");
    console.warn("  - PostHog configuration problems");
    console.warn("  - Events are being batched (try waiting longer)");
  }
}, 10000);
