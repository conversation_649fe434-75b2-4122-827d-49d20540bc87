#!/usr/bin/env node

/**
 * Debug script to check environment variables in production
 */

console.log('🔍 Environment Variable Debug Script');
console.log('=====================================');

// Required variables
const REQUIRED_VARS = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY', 
  'NEXT_PUBLIC_SENTRY_DSN',
  'NEXT_PUBLIC_POSTHOG_API_KEY',
];

// Placeholder patterns
const placeholderPatterns = [
  /your_.*_here/i,
  /replace.*with.*actual/i,
  /todo.*replace/i,
  /example/i,
  /test.*key/i,
  /dummy/i,
  /placeholder/i,
];

function isPlaceholderValue(value) {
  return placeholderPatterns.some(pattern => pattern.test(value));
}

function checkVariable(varName) {
  const value = process.env[varName];
  const exists = !!value;
  const isEmpty = !value || value.trim() === '';
  const isPlaceholder = value ? isPlaceholderValue(value) : false;
  
  console.log(`\n📋 ${varName}:`);
  console.log(`   Exists: ${exists}`);
  console.log(`   Empty: ${isEmpty}`);
  console.log(`   Is Placeholder: ${isPlaceholder}`);
  
  if (value) {
    // Show first and last 4 characters for security
    const masked = value.length > 8 
      ? `${value.substring(0, 4)}...${value.substring(value.length - 4)}`
      : '[REDACTED]';
    console.log(`   Value (masked): ${masked}`);
    console.log(`   Length: ${value.length}`);
  }
  
  // Check specific patterns
  if (varName.includes('POSTHOG') && value) {
    console.log(`   Starts with 'phc_': ${value.startsWith('phc_')}`);
  }
  
  if (varName.includes('SUPABASE_URL') && value) {
    console.log(`   Contains '.supabase.co': ${value.includes('.supabase.co')}`);
  }
  
  if (varName.includes('SENTRY_DSN') && value) {
    console.log(`   Contains 'sentry.io': ${value.includes('sentry.io')}`);
  }
  
  return { exists, isEmpty, isPlaceholder, value };
}

console.log(`\n🌍 NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`🐳 Running in container: ${process.env.HOSTNAME || 'unknown'}`);

console.log('\n📊 Required Variables Check:');
console.log('============================');

const results = {};
REQUIRED_VARS.forEach(varName => {
  results[varName] = checkVariable(varName);
});

console.log('\n📈 Summary:');
console.log('===========');

const missing = [];
const placeholders = [];
const valid = [];

Object.entries(results).forEach(([varName, result]) => {
  if (!result.exists || result.isEmpty) {
    missing.push(varName);
  } else if (result.isPlaceholder) {
    placeholders.push(varName);
  } else {
    valid.push(varName);
  }
});

console.log(`✅ Valid: ${valid.length} (${valid.join(', ') || 'none'})`);
console.log(`⚠️  Placeholders: ${placeholders.length} (${placeholders.join(', ') || 'none'})`);
console.log(`❌ Missing: ${missing.length} (${missing.join(', ') || 'none'})`);

if (placeholders.length > 0 || missing.length > 0) {
  console.log('\n🚨 Issues Found:');
  if (missing.length > 0) {
    console.log(`   Missing variables will cause the app to fail: ${missing.join(', ')}`);
  }
  if (placeholders.length > 0) {
    console.log(`   Placeholder variables will cause the app to fail: ${placeholders.join(', ')}`);
  }
  process.exit(1);
} else {
  console.log('\n🎉 All environment variables are properly configured!');
  process.exit(0);
}
