// Production PostHog Test Script
// Copy and paste this into your production site's browser console

console.log('🚀 Testing PostHog in Production...');

// Production configuration
const apiKey = 'phc_yErhnRXWMQkP4hO7nU37vDRE72w3KcgyUA5CCXvRdP1';
const apiHost = 'https://us.i.posthog.com';

console.log('🔑 API Key:', apiKey.substring(0, 10) + '...');
console.log('🌐 API Host:', apiHost);
console.log('🌍 Environment: Production');
console.log('📍 Current URL:', window.location.href);

// Check if PostHog is already loaded
if (window.posthog && window.posthog.__loaded) {
  console.log('✅ PostHog found and loaded');
  
  const posthog = window.posthog;
  
  // Display current status
  const status = {
    distinctId: posthog.get_distinct_id?.(),
    hasOptedOut: posthog.has_opted_out_capturing?.(),
    apiHost: posthog.config?.api_host,
    apiKey: posthog.config?.token?.substring(0, 10) + '...',
    requestBatching: posthog.config?.request_batching
  };
  
  console.log('📊 PostHog Status:', status);
  
  // Check if opted out
  if (posthog.has_opted_out_capturing?.()) {
    console.log('⚠️ PostHog is opted out, attempting to opt in...');
    posthog.opt_in_capturing();
    
    const stillOptedOut = posthog.has_opted_out_capturing?.();
    console.log('📊 Opt-in result:', stillOptedOut ? '❌ Still opted out' : '✅ Successfully opted in');
  } else {
    console.log('✅ PostHog is opted in and ready');
  }
  
  // Send production test event
  const testEvent = {
    timestamp: new Date().toISOString(),
    test_type: 'production_console_test',
    environment: 'production',
    page_url: window.location.href,
    user_agent: navigator.userAgent,
    random_id: Math.random().toString(36).substring(7),
    domain: window.location.hostname
  };
  
  console.log('📤 Sending production test event:', testEvent);
  posthog.capture('production_test_event', testEvent);
  
  // Also send a page view to test basic tracking
  console.log('📤 Sending production page view...');
  posthog.capture('$pageview', {
    $current_url: window.location.href,
    $host: window.location.hostname,
    $pathname: window.location.pathname,
    test_pageview: true
  });
  
  console.log('✅ Production test events sent!');
  console.log('🔍 Check your PostHog dashboard: https://us.posthog.com');
  
} else {
  console.error('❌ PostHog not found or not loaded');
  console.log('🔧 This might indicate:');
  console.log('  - PostHog failed to initialize');
  console.log('  - Script loading was blocked');
  console.log('  - Configuration error');
  
  // Try to initialize PostHog manually
  console.log('🔄 Attempting manual PostHog initialization...');
  
  // Load PostHog script
  const script = document.createElement('script');
  script.src = 'https://app.posthog.com/static/array.js';
  script.onload = function() {
    console.log('📦 PostHog script loaded, initializing...');
    
    window.posthog.init(apiKey, {
      api_host: apiHost,
      debug: false, // Keep debug off in production
      opt_out_capturing_by_default: false,
      request_batching: false, // This is the key fix!
      
      loaded: function(posthog) {
        console.log('✅ PostHog manually initialized in production!');
        
        // Send test event
        const testEvent = {
          timestamp: new Date().toISOString(),
          test_type: 'production_manual_init',
          environment: 'production',
          page_url: window.location.href,
          random_id: Math.random().toString(36).substring(7)
        };
        
        console.log('📤 Sending manual init test event:', testEvent);
        posthog.capture('production_manual_test_event', testEvent);
        console.log('✅ Manual test event sent!');
      }
    });
  };
  
  script.onerror = function() {
    console.error('❌ Failed to load PostHog script in production');
  };
  
  document.head.appendChild(script);
}

// Monitor network requests for 15 seconds (longer for production)
console.log('🌐 Monitoring PostHog network requests for 15 seconds...');
const originalFetch = window.fetch;
let requestCount = 0;
const requests = [];

window.fetch = function(...args) {
  const url = args[0];
  if (typeof url === 'string' && (url.includes('posthog') || url.includes('i.posthog.com'))) {
    requestCount++;
    const requestInfo = {
      count: requestCount,
      url: url,
      timestamp: new Date().toISOString()
    };
    requests.push(requestInfo);
    console.log(`🌐 PostHog request #${requestCount}:`, url);
  }
  return originalFetch.apply(this, args);
};

setTimeout(() => {
  window.fetch = originalFetch;
  console.log(`🌐 Production network monitoring complete`);
  console.log(`📊 Total PostHog requests: ${requestCount}`);
  
  if (requestCount > 0) {
    console.log('✅ PostHog is making network requests in production!');
    console.log('📋 Request summary:', requests);
  } else {
    console.warn('⚠️ No PostHog network requests detected in production');
    console.warn('🔍 Possible issues:');
    console.warn('  - Ad blocker blocking requests');
    console.warn('  - Corporate firewall blocking analytics');
    console.warn('  - PostHog configuration error');
    console.warn('  - Events being batched (should be fixed now)');
  }
  
  console.log('🎯 Next steps:');
  console.log('  1. Check PostHog dashboard: https://us.posthog.com');
  console.log('  2. Look for events: production_test_event, $pageview');
  console.log('  3. Verify events appear within 1-2 minutes');
}, 15000);

console.log('⏱️ Test running... wait 15 seconds for complete results');