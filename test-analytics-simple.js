// Simple Analytics Test - Copy and paste this into browser console at http://localhost:3000

console.log('🚀 Testing Analytics System');
console.log('📍 URL:', window.location.href);

// Check if global functions are available
console.log('\n=== Global Functions Check ===');
console.log('window.analytics:', typeof window.analytics);
console.log('window.trackEvent:', typeof window.trackEvent);
console.log('window.sendTestEvent:', typeof window.sendTestEvent);
console.log('window.getAnalyticsStatus:', typeof window.getAnalyticsStatus);

// Get status if available
if (window.getAnalyticsStatus) {
  const status = window.getAnalyticsStatus();
  console.log('\n=== Analytics Status ===');
  console.log('Status:', status);
  
  if (status.ready) {
    console.log('✅ Analytics is ready in', status.mode, 'mode');
  } else {
    console.log('⚠️ Analytics not ready');
    if (status.lastError) {
      console.log('Error:', status.lastError);
    }
  }
}

// Try to send a test event
if (window.trackEvent) {
  console.log('\n=== Sending Test Event ===');
  window.trackEvent('console_test', {
    test_id: 'simple_test_' + Date.now(),
    timestamp: new Date().toISOString(),
    source: 'console_test'
  });
  console.log('✅ Test event sent');
} else {
  console.log('⚠️ trackEvent function not available');
}

// Check health API
console.log('\n=== Health Check ===');
fetch('/api/health')
  .then(r => r.json())
  .then(health => {
    console.log('Health status:', health.services?.analytics);
  })
  .catch(e => console.log('Health check failed:', e));

console.log('\n📝 Instructions:');
console.log('1. If you see analytics functions available, the system is working');
console.log('2. Check PostHog dashboard for events');
console.log('3. Look for events with test_id starting with "simple_test_"');