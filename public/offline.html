<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Offline - J&A Business Solutions LLC</title>
  <link rel="manifest" href="/manifest.json">
  <meta name="theme-color" content="#0F172A">
  <style>
    body {
      font-family: 'Inter', system-ui, sans-serif;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      min-height: 100vh;
      background-color: #f8f9fa;
      color: #0F172A;
    }
    
    .container {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 2rem;
      text-align: center;
    }
    
    .logo {
      width: 120px;
      height: 120px;
      margin-bottom: 2rem;
    }
    
    h1 {
      font-size: 2rem;
      margin-bottom: 1rem;
      color: #0F172A;
    }
    
    p {
      font-size: 1.125rem;
      margin-bottom: 2rem;
      max-width: 600px;
      line-height: 1.5;
    }
    
    .btn {
      display: inline-block;
      background-color: #D4AF37;
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 0.375rem;
      text-decoration: none;
      font-weight: 500;
      transition: background-color 0.3s;
    }
    
    .btn:hover {
      background-color: #b38d27;
    }
    
    footer {
      padding: 1.5rem;
      background-color: #0F172A;
      color: white;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="container">
    <img src="/icons/android-icon-192x192.png" alt="J&A Business Solutions" class="logo">
    <h1>You're currently offline</h1>
    <p>
      It seems you're not connected to the internet right now. 
      Please check your connection and try again.
    </p>
    <a href="/" class="btn">Try Again</a>
  </div>
  
  <footer>
    &copy; 2023 J&A Business Solutions LLC. All rights reserved.
  </footer>
  
  <script>
    // Check if the user comes back online
    window.addEventListener('online', () => {
      window.location.reload();
    });
    
    // Add event listener for the "Try Again" button
    document.querySelector('.btn').addEventListener('click', (e) => {
      e.preventDefault();
      window.location.reload();
    });
  </script>
</body>
</html>
