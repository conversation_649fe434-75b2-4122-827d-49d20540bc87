// Debug PostHog initialization in production
// Run this in browser console on https://jnabusinesssolutions.com

console.log('🔍 PostHog Initialization Debug');
console.log('================================');

// Check if PostHog is loaded
console.log('1. PostHog global object:', typeof window.posthog);

// Check environment config
try {
  const envCheck = {
    NODE_ENV: process.env.NODE_ENV,
    isDevelopment: process.env.NODE_ENV === 'development',
    isProduction: process.env.NODE_ENV === 'production'
  };
  console.log('2. Environment:', envCheck);
} catch (e) {
  console.log('2. Environment check failed:', e.message);
}

// Check if analytics initialization was called
console.log('3. Checking for initialization logs...');
console.log('   (Look for "🚀 Starting PostHog analytics initialization..." in console)');

// Try to manually initialize PostHog
console.log('4. Attempting manual PostHog initialization...');

try {
  // Import PostHog
  import('posthog-js').then(posthogModule => {
    const posthog = posthogModule.default;
    
    console.log('   PostHog module loaded:', !!posthog);
    
    // Try to initialize with production config
    const config = {
      apiKey: 'phc_yErhnRXWMQkP4hO7nU37vDRE72w3KcgyUA5CCXvRdP1',
      apiHost: 'https://us.i.posthog.com'
    };
    
    console.log('   Attempting init with config:', {
      apiKey: config.apiKey.substring(0, 10) + '...',
      apiHost: config.apiHost
    });
    
    posthog.init(config.apiKey, {
      api_host: config.apiHost,
      debug: true, // Enable debug mode
      loaded: (ph) => {
        console.log('✅ PostHog loaded successfully!', {
          distinctId: ph.get_distinct_id(),
          hasOptedOut: ph.has_opted_out_capturing()
        });
      },
      on_request_error: (error) => {
        console.error('❌ PostHog request error:', error);
      }
    });
    
  }).catch(error => {
    console.error('   Failed to import PostHog:', error);
  });
  
} catch (error) {
  console.error('4. Manual initialization failed:', error);
}

// Check for CSP or network restrictions
console.log('5. Checking for potential blockers...');
console.log('   - Check Network tab for blocked requests to posthog.com');
console.log('   - Check Console for CSP violations');
console.log('   - Check if ad blockers are interfering');

console.log('\n🎯 Next steps:');
console.log('   1. Look for any error messages above');
console.log('   2. Check Network tab for failed requests');
console.log('   3. Try disabling ad blockers');
console.log('   4. Check browser console for CSP errors');