#!/bin/bash

# J&A Business Solutions - Deployment Scripts Validation
# This script validates the deployment scripts without executing them

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "J&A Business Solutions - Deployment Scripts Validation"
print_status "======================================================"
echo

# Check if scripts exist and are executable
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_SCRIPT="$SCRIPT_DIR/build-and-push.sh"
DEPLOY_SCRIPT="$SCRIPT_DIR/deploy-production.sh"

print_status "Checking script files..."

if [ -f "$BUILD_SCRIPT" ]; then
    if [ -x "$BUILD_SCRIPT" ]; then
        print_success "Build script exists and is executable: $BUILD_SCRIPT"
    else
        print_error "Build script exists but is not executable: $BUILD_SCRIPT"
        exit 1
    fi
else
    print_error "Build script not found: $BUILD_SCRIPT"
    exit 1
fi

if [ -f "$DEPLOY_SCRIPT" ]; then
    if [ -x "$DEPLOY_SCRIPT" ]; then
        print_success "Deploy script exists and is executable: $DEPLOY_SCRIPT"
    else
        print_error "Deploy script exists but is not executable: $DEPLOY_SCRIPT"
        exit 1
    fi
else
    print_error "Deploy script not found: $DEPLOY_SCRIPT"
    exit 1
fi

# Validate script syntax
print_status "Validating script syntax..."

if bash -n "$BUILD_SCRIPT"; then
    print_success "Build script syntax is valid"
else
    print_error "Build script has syntax errors"
    exit 1
fi

if bash -n "$DEPLOY_SCRIPT"; then
    print_success "Deploy script syntax is valid"
else
    print_error "Deploy script has syntax errors"
    exit 1
fi

# Check for required configuration files
print_status "Checking required configuration files..."

PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_ROOT"

REQUIRED_FILES=(
    "Dockerfile"
    "docker-compose.prod.yml"
    ".env.production.example"
    "package.json"
    "next.config.js"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_success "Required file exists: $file"
    else
        print_error "Required file missing: $file"
        exit 1
    fi
done

# Check if .env.production exists (optional but recommended)
if [ -f ".env.production" ]; then
    print_success "Production environment file exists: .env.production"
    
    # Validate environment file content
    if grep -q "NODE_ENV=production" ".env.production"; then
        print_success "NODE_ENV=production found in environment file"
    else
        print_warning "NODE_ENV=production not found in .env.production"
    fi
else
    print_warning "Production environment file not found: .env.production"
    print_status "Create it from .env.production.example before deployment"
fi

# Test script help functions
print_status "Testing script help functions..."

if "$BUILD_SCRIPT" --help >/dev/null 2>&1; then
    print_success "Build script help function works"
else
    print_warning "Build script help function may have issues"
fi

if "$DEPLOY_SCRIPT" --help >/dev/null 2>&1; then
    print_success "Deploy script help function works"
else
    print_warning "Deploy script help function may have issues"
fi

# Check Docker availability (optional)
print_status "Checking Docker availability..."

if command -v docker >/dev/null 2>&1; then
    if docker info >/dev/null 2>&1; then
        print_success "Docker is available and running"
    else
        print_warning "Docker is installed but not running"
    fi
else
    print_warning "Docker is not installed or not in PATH"
fi

# Check for zkm3 command (for deployment)
print_status "Checking VPC connection command..."

if command -v zkm3 >/dev/null 2>&1; then
    print_success "zkm3 command is available"
else
    print_warning "zkm3 command not found - required for production deployment"
fi

echo
print_status "Validation Summary:"
print_status "=================="
print_success "✓ All deployment scripts are present and executable"
print_success "✓ Script syntax validation passed"
print_success "✓ Required configuration files are present"
echo
print_status "Next Steps:"
echo "1. Ensure .env.production is configured with production values"
echo "2. Test build script: ./scripts/build-and-push.sh --help"
echo "3. Test deployment script: ./scripts/deploy-production.sh --help"
echo "4. Run actual deployment: ./scripts/deploy-production.sh"
echo

print_success "Deployment scripts validation completed successfully!"