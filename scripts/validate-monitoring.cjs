#!/usr/bin/env node

/**
 * Production monitoring validation script
 * Validates that all monitoring services are properly configured for production deployment
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  bold: '\x1b[1m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

/**
 * Load environment variables from .env files
 */
function loadEnvironmentVariables() {
  const envFiles = ['.env.production', '.env.local', '.env'];
  const env = { ...process.env };

  for (const envFile of envFiles) {
    const envPath = path.join(process.cwd(), envFile);
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const lines = envContent.split('\n');
      
      for (const line of lines) {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
          const [key, ...valueParts] = trimmed.split('=');
          if (key && valueParts.length > 0) {
            const value = valueParts.join('=').replace(/^["']|["']$/g, '');
            if (!env[key]) { // Don't override existing env vars
              env[key] = value;
            }
          }
        }
      }
      logInfo(`Loaded environment variables from ${envFile}`);
    }
  }

  return env;
}

/**
 * Validate Sentry configuration
 */
function validateSentry(env) {
  const issues = [];
  const warnings = [];

  // Check required environment variables
  if (!env.NEXT_PUBLIC_SENTRY_DSN) {
    issues.push('NEXT_PUBLIC_SENTRY_DSN is not set');
  } else if (env.NEXT_PUBLIC_SENTRY_DSN === 'your_sentry_dsn_here') {
    issues.push('NEXT_PUBLIC_SENTRY_DSN is set to placeholder value');
  } else {
    // Validate DSN format
    const dsnPattern = /^https:\/\/[a-f0-9]+@[a-z0-9.-]+\.ingest\.sentry\.io\/\d+$/;
    if (!dsnPattern.test(env.NEXT_PUBLIC_SENTRY_DSN)) {
      warnings.push('NEXT_PUBLIC_SENTRY_DSN format may be invalid');
    }
  }

  // Check environment setting
  if (!env.NEXT_PUBLIC_SENTRY_ENVIRONMENT) {
    warnings.push('NEXT_PUBLIC_SENTRY_ENVIRONMENT is not set, will default to development');
  } else if (env.NODE_ENV === 'production' && env.NEXT_PUBLIC_SENTRY_ENVIRONMENT !== 'production') {
    warnings.push(`NEXT_PUBLIC_SENTRY_ENVIRONMENT is '${env.NEXT_PUBLIC_SENTRY_ENVIRONMENT}' but NODE_ENV is 'production'`);
  }

  // Check optional Sentry configuration
  if (env.SENTRY_ORG && env.SENTRY_PROJECT) {
    logInfo('Sentry organization and project configured for source maps');
  } else {
    warnings.push('SENTRY_ORG and SENTRY_PROJECT not configured - source maps will not be uploaded');
  }

  return { issues, warnings };
}

/**
 * Validate PostHog configuration
 */
function validatePostHog(env) {
  const issues = [];
  const warnings = [];

  // Check required environment variables
  if (!env.NEXT_PUBLIC_POSTHOG_API_KEY) {
    issues.push('NEXT_PUBLIC_POSTHOG_API_KEY is not set');
  } else if (env.NEXT_PUBLIC_POSTHOG_API_KEY === 'your_posthog_api_key') {
    issues.push('NEXT_PUBLIC_POSTHOG_API_KEY is set to placeholder value');
  } else {
    // Validate API key format (PostHog keys start with 'phc_')
    if (!env.NEXT_PUBLIC_POSTHOG_API_KEY.startsWith('phc_')) {
      warnings.push('NEXT_PUBLIC_POSTHOG_API_KEY format may be invalid (should start with phc_)');
    }
  }

  // Check API host
  if (!env.NEXT_PUBLIC_POSTHOG_API_HOST) {
    warnings.push('NEXT_PUBLIC_POSTHOG_API_HOST is not set, will use default');
  } else {
    // Validate host format
    const hostPattern = /^https:\/\/[a-z0-9.-]+\.[a-z]{2,}$/;
    if (!hostPattern.test(env.NEXT_PUBLIC_POSTHOG_API_HOST)) {
      warnings.push('NEXT_PUBLIC_POSTHOG_API_HOST format may be invalid');
    }
  }

  return { issues, warnings };
}

/**
 * Validate monitoring configuration files
 */
function validateConfigurationFiles() {
  const issues = [];
  const warnings = [];

  // Check Sentry configuration files
  const sentryFiles = [
    'sentry.client.config.ts',
    'sentry.server.config.ts',
    'sentry.edge.config.ts'
  ];

  for (const file of sentryFiles) {
    const filePath = path.join(process.cwd(), file);
    if (!fs.existsSync(filePath)) {
      issues.push(`Missing Sentry configuration file: ${file}`);
    } else {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Check if file has proper initialization
      if (!content.includes('Sentry.init')) {
        warnings.push(`${file} may not have proper Sentry initialization`);
      }
      
      // Check for rate limiting configuration
      if (!content.includes('tracesSampleRate')) {
        warnings.push(`${file} missing tracesSampleRate configuration`);
      }
    }
  }

  // Check monitoring library files
  const monitoringFiles = [
    'src/lib/monitoring.ts',
    'src/lib/sentry.ts',
    'src/lib/posthog.ts'
  ];

  for (const file of monitoringFiles) {
    const filePath = path.join(process.cwd(), file);
    if (!fs.existsSync(filePath)) {
      warnings.push(`Missing monitoring library file: ${file}`);
    }
  }

  return { issues, warnings };
}

/**
 * Validate Next.js configuration for monitoring
 */
function validateNextJsConfig() {
  const issues = [];
  const warnings = [];

  const nextConfigPath = path.join(process.cwd(), 'next.config.js');
  if (!fs.existsSync(nextConfigPath)) {
    warnings.push('next.config.js not found');
    return { issues, warnings };
  }

  const content = fs.readFileSync(nextConfigPath, 'utf8');

  // Check for Sentry webpack plugin
  if (!content.includes('@sentry/nextjs') && !content.includes('withSentryConfig')) {
    warnings.push('Next.js config may not include Sentry integration');
  }

  // Check for source maps configuration
  if (content.includes('productionBrowserSourceMaps: false')) {
    logInfo('Production browser source maps are disabled (recommended for production)');
  } else if (!content.includes('productionBrowserSourceMaps')) {
    warnings.push('productionBrowserSourceMaps not explicitly configured');
  }

  return { issues, warnings };
}

/**
 * Test monitoring endpoints
 */
async function testMonitoringEndpoints() {
  const issues = [];
  const warnings = [];

  // Test health check endpoint
  try {
    const healthResponse = await fetch('http://localhost:3000/api/health', {
      method: 'GET',
      timeout: 5000,
    });

    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      
      if (healthData.services) {
        // Check individual service health
        if (healthData.services.monitoring?.sentry?.configured) {
          logSuccess('Sentry monitoring configured in health check');
        } else {
          warnings.push('Sentry not configured according to health check');
        }

        if (healthData.services.analytics?.posthog?.initialized) {
          logSuccess('PostHog analytics initialized in health check');
        } else {
          warnings.push('PostHog not initialized according to health check');
        }
      }
    } else {
      warnings.push(`Health check endpoint returned ${healthResponse.status}`);
    }
  } catch (error) {
    warnings.push('Could not test health check endpoint (server may not be running)');
  }

  return { issues, warnings };
}

/**
 * Main validation function
 */
async function validateMonitoring() {
  log('🔍 Validating monitoring configuration for production deployment...', 'bold');
  console.log();

  // Load environment variables
  const env = loadEnvironmentVariables();
  
  let totalIssues = 0;
  let totalWarnings = 0;

  // Validate Sentry
  log('📊 Validating Sentry configuration...', 'blue');
  const sentryValidation = validateSentry(env);
  if (sentryValidation.issues.length === 0 && sentryValidation.warnings.length === 0) {
    logSuccess('Sentry configuration is valid');
  } else {
    sentryValidation.issues.forEach(logError);
    sentryValidation.warnings.forEach(logWarning);
  }
  totalIssues += sentryValidation.issues.length;
  totalWarnings += sentryValidation.warnings.length;
  console.log();

  // Validate PostHog
  log('📈 Validating PostHog configuration...', 'blue');
  const posthogValidation = validatePostHog(env);
  if (posthogValidation.issues.length === 0 && posthogValidation.warnings.length === 0) {
    logSuccess('PostHog configuration is valid');
  } else {
    posthogValidation.issues.forEach(logError);
    posthogValidation.warnings.forEach(logWarning);
  }
  totalIssues += posthogValidation.issues.length;
  totalWarnings += posthogValidation.warnings.length;
  console.log();

  // Validate configuration files
  log('📁 Validating configuration files...', 'blue');
  const fileValidation = validateConfigurationFiles();
  if (fileValidation.issues.length === 0 && fileValidation.warnings.length === 0) {
    logSuccess('Configuration files are valid');
  } else {
    fileValidation.issues.forEach(logError);
    fileValidation.warnings.forEach(logWarning);
  }
  totalIssues += fileValidation.issues.length;
  totalWarnings += fileValidation.warnings.length;
  console.log();

  // Validate Next.js configuration
  log('⚙️  Validating Next.js configuration...', 'blue');
  const nextValidation = validateNextJsConfig();
  if (nextValidation.issues.length === 0 && nextValidation.warnings.length === 0) {
    logSuccess('Next.js configuration is valid');
  } else {
    nextValidation.issues.forEach(logError);
    nextValidation.warnings.forEach(logWarning);
  }
  totalIssues += nextValidation.issues.length;
  totalWarnings += nextValidation.warnings.length;
  console.log();

  // Test endpoints (optional)
  if (process.argv.includes('--test-endpoints')) {
    log('🌐 Testing monitoring endpoints...', 'blue');
    const endpointValidation = await testMonitoringEndpoints();
    if (endpointValidation.issues.length === 0 && endpointValidation.warnings.length === 0) {
      logSuccess('Monitoring endpoints are responding correctly');
    } else {
      endpointValidation.issues.forEach(logError);
      endpointValidation.warnings.forEach(logWarning);
    }
    totalIssues += endpointValidation.issues.length;
    totalWarnings += endpointValidation.warnings.length;
    console.log();
  }

  // Summary
  log('📋 Validation Summary:', 'bold');
  if (totalIssues === 0) {
    logSuccess(`Monitoring configuration is ready for production! (${totalWarnings} warnings)`);
  } else {
    logError(`Found ${totalIssues} issues and ${totalWarnings} warnings that need attention`);
  }

  // Exit with appropriate code
  process.exit(totalIssues > 0 ? 1 : 0);
}

// Run validation
if (require.main === module) {
  validateMonitoring().catch((error) => {
    logError(`Validation failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { validateMonitoring };