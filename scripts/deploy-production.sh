#!/bin/bash

# J&A Business Solutions - Production Deployment Script
# This script deploys the application to the production VPC using zkm3 alias
# 
# Features:
# - Connects to VPC using zkm3 alias command
# - Copies .env and docker-compose files to /home/<USER>/docker/jna-business-solutions
# - Manages containers (stop, pull, start) in the application directory
# - Comprehensive health check verification after deployment
# - Rollback capabilities and error recovery
# - Deployment logging and monitoring

set -e  # Exit on any error
set -u  # Exit on undefined variables
set -o pipefail  # Exit on pipe failures

# Configuration
REMOTE_USER="atem"
REMOTE_PATH="/home/<USER>/docker/jna-business-solutions"
LOCAL_COMPOSE_FILE="docker-compose.prod.yml"
LOCAL_ENV_FILE=".env.production"
HEALTH_CHECK_URL="http://localhost:8642/api/health"  # Using production port 8642
HEALTH_CHECK_RETRIES=30
HEALTH_CHECK_INTERVAL=10
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
DEPLOYMENT_LOG="/tmp/jna-deployment-$(date +%Y%m%d-%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to log messages
log_message() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $message" | tee -a "$DEPLOYMENT_LOG"
}

# Function to validate local environment
validate_local_environment() {
    print_status "Validating local environment..."
    
    cd "$PROJECT_ROOT"
    
    # Check if required files exist
    if [ ! -f "$LOCAL_COMPOSE_FILE" ]; then
        print_error "Docker Compose file not found: $LOCAL_COMPOSE_FILE"
        exit 1
    fi

    if [ ! -f "$LOCAL_ENV_FILE" ]; then
        print_error "Environment file not found: $LOCAL_ENV_FILE"
        print_warning "Please create $LOCAL_ENV_FILE from .env.production.example"
        exit 1
    fi
    
    # Validate environment file
    print_status "Validating environment configuration..."
    if ! grep -q "NODE_ENV=production" "$LOCAL_ENV_FILE"; then
        print_error "NODE_ENV=production not found in $LOCAL_ENV_FILE"
        exit 1
    fi
    
    # Check for required environment variables
    local required_vars=("NEXT_PUBLIC_SUPABASE_URL" "NEXT_PUBLIC_SUPABASE_ANON_KEY" "NEXT_PUBLIC_SENTRY_DSN")
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" "$LOCAL_ENV_FILE"; then
            print_error "Required environment variable $var not found in $LOCAL_ENV_FILE"
            exit 1
        fi
    done
    
    print_success "Local environment validation completed"
}

# Function to test VPC connection
test_vpc_connection() {
    print_status "Testing VPC connection..."
    
    # Check if zkm3 alias is available
    if ! command -v zkm3 &> /dev/null; then
        print_error "zkm3 command not found. Please ensure the VPC connection alias is set up."
        print_status "You may need to connect manually using SSH."
        exit 1
    fi
    
    # Test connection with timeout
    if timeout 30 zkm3 "echo 'VPC connection test successful'" >/dev/null 2>&1; then
        print_success "VPC connection established successfully"
    else
        print_error "Failed to connect to production VPC using zkm3"
        print_status "Please check your VPC connection and try again"
        exit 1
    fi
}

# Function to backup current deployment
backup_current_deployment() {
    print_status "Creating backup of current deployment..."
    
    local backup_dir="backup-$(date +%Y%m%d-%H%M%S)"
    
    # Create backup directory and copy current files
    zkm3 "cd $REMOTE_PATH && mkdir -p $backup_dir"
    zkm3 "cd $REMOTE_PATH && cp -f docker-compose.yml $backup_dir/ 2>/dev/null || true"
    zkm3 "cd $REMOTE_PATH && cp -f .env.production $backup_dir/ 2>/dev/null || true"
    
    # Export current container state
    zkm3 "cd $REMOTE_PATH && docker-compose ps > $backup_dir/container-state.txt 2>/dev/null || true"
    
    print_success "Backup created in $REMOTE_PATH/$backup_dir"
    log_message "Backup created: $backup_dir"
    
    # Store backup directory for potential rollback
    echo "$backup_dir" > /tmp/jna-last-backup
}

# Function to get current running version
get_current_version() {
    local current_version
    current_version=$(zkm3 "cd $REMOTE_PATH && docker-compose ps --format json 2>/dev/null | jq -r '.[0].Image' 2>/dev/null | cut -d':' -f2" 2>/dev/null || echo "unknown")
    echo "$current_version"
}

# Function to copy files to remote
copy_files_to_remote() {
    print_status "Copying configuration files to production server..."
    
    # Create remote directory structure
    zkm3 "mkdir -p $REMOTE_PATH"
    zkm3 "mkdir -p $REMOTE_PATH/logs"
    
    # Copy environment file with verification
    print_status "Copying environment configuration..."
    if scp "$LOCAL_ENV_FILE" "${REMOTE_USER}@zkm3:${REMOTE_PATH}/.env.production"; then
        print_success "Environment file copied successfully"
    else
        print_error "Failed to copy environment file"
        exit 1
    fi
    
    # Copy docker-compose file with verification
    print_status "Copying Docker Compose configuration..."
    if scp "$LOCAL_COMPOSE_FILE" "${REMOTE_USER}@zkm3:${REMOTE_PATH}/docker-compose.yml"; then
        print_success "Docker Compose file copied successfully"
    else
        print_error "Failed to copy Docker Compose file"
        exit 1
    fi
    
    # Verify files were copied correctly
    print_status "Verifying copied files..."
    zkm3 "cd $REMOTE_PATH && ls -la docker-compose.yml .env.production"
    
    log_message "Configuration files copied to remote server"
}

# Function to manage containers
manage_containers() {
    print_status "Managing application containers..."
    
    local current_version=$(get_current_version)
    print_status "Current running version: $current_version"
    
    # Stop existing containers gracefully
    print_status "Stopping existing containers..."
    zkm3 "cd $REMOTE_PATH && docker-compose down --timeout 30 --remove-orphans" || {
        print_warning "Graceful shutdown failed, forcing container stop..."
        zkm3 "cd $REMOTE_PATH && docker-compose kill" || true
        zkm3 "cd $REMOTE_PATH && docker-compose rm -f" || true
    }
    
    # Clean up unused images and containers
    print_status "Cleaning up unused Docker resources..."
    zkm3 "docker system prune -f --volumes" || print_warning "Docker cleanup had some issues"
    
    # Pull latest images
    print_status "Pulling latest Docker images..."
    if zkm3 "cd $REMOTE_PATH && docker-compose pull"; then
        print_success "Docker images pulled successfully"
    else
        print_error "Failed to pull Docker images"
        exit 1
    fi
    
    # Start new containers
    print_status "Starting new containers..."
    if zkm3 "cd $REMOTE_PATH && docker-compose up -d"; then
        print_success "Containers started successfully"
    else
        print_error "Failed to start containers"
        print_status "Checking container logs..."
        zkm3 "cd $REMOTE_PATH && docker-compose logs --tail=50" || true
        exit 1
    fi
    
    log_message "Container management completed - upgraded from $current_version"
}

# Function to perform comprehensive health checks
perform_health_checks() {
    print_status "Performing comprehensive health checks..."
    
    # Wait for containers to initialize
    print_status "Waiting for containers to initialize..."
    sleep 15
    
    # Check container status
    print_status "Checking container status..."
    local container_status
    container_status=$(zkm3 "cd $REMOTE_PATH && docker-compose ps --format json" 2>/dev/null || echo "[]")
    
    if [ "$container_status" = "[]" ]; then
        print_error "No containers are running"
        return 1
    fi
    
    # Display container status
    zkm3 "cd $REMOTE_PATH && docker-compose ps"
    
    # Check if main application container is running
    local app_running
    app_running=$(zkm3 "cd $REMOTE_PATH && docker-compose ps --services --filter status=running | grep -c web" || echo "0")
    
    if [ "$app_running" -eq 0 ]; then
        print_error "Main application container is not running"
        print_status "Container logs:"
        zkm3 "cd $REMOTE_PATH && docker-compose logs web --tail=50"
        return 1
    fi
    
    # Perform HTTP health checks
    print_status "Performing HTTP health checks..."
    local health_check_passed=false
    
    for i in $(seq 1 $HEALTH_CHECK_RETRIES); do
        print_status "Health check attempt $i/$HEALTH_CHECK_RETRIES..."
        
        if zkm3 "curl -f -s --max-time 10 $HEALTH_CHECK_URL" >/dev/null 2>&1; then
            health_check_passed=true
            print_success "Health check passed!"
            break
        else
            if [ $i -eq $HEALTH_CHECK_RETRIES ]; then
                print_error "Health check failed after $HEALTH_CHECK_RETRIES attempts"
                print_status "Checking application logs..."
                zkm3 "cd $REMOTE_PATH && docker-compose logs web --tail=20"
                return 1
            else
                print_status "Health check failed, retrying in ${HEALTH_CHECK_INTERVAL}s..."
                sleep $HEALTH_CHECK_INTERVAL
            fi
        fi
    done
    
    # Additional health checks
    if [ "$health_check_passed" = true ]; then
        print_status "Running additional health checks..."
        
        # Check if the application responds with expected content
        local health_response
        health_response=$(zkm3 "curl -s --max-time 10 $HEALTH_CHECK_URL" 2>/dev/null || echo "")
        
        if echo "$health_response" | grep -q "ok\|healthy\|success"; then
            print_success "Application health endpoint is responding correctly"
        else
            print_warning "Health endpoint responded but content may be unexpected"
            print_status "Response: $health_response"
        fi
        
        # Check application logs for errors
        print_status "Checking application logs for errors..."
        local error_count
        error_count=$(zkm3 "cd $REMOTE_PATH && docker-compose logs web --since=5m | grep -i error | wc -l" 2>/dev/null || echo "0")
        
        if [ "$error_count" -gt 0 ]; then
            print_warning "Found $error_count error(s) in recent logs"
            zkm3 "cd $REMOTE_PATH && docker-compose logs web --since=5m | grep -i error | tail -5"
        else
            print_success "No errors found in recent application logs"
        fi
    fi
    
    log_message "Health checks completed - Status: $([ "$health_check_passed" = true ] && echo "PASSED" || echo "FAILED")"
    return $([ "$health_check_passed" = true ] && echo 0 || echo 1)
}

# Function to rollback deployment
rollback_deployment() {
    print_error "Deployment failed. Initiating rollback..."
    
    if [ -f /tmp/jna-last-backup ]; then
        local backup_dir=$(cat /tmp/jna-last-backup)
        print_status "Rolling back to backup: $backup_dir"
        
        # Restore previous configuration
        zkm3 "cd $REMOTE_PATH && cp $backup_dir/docker-compose.yml . 2>/dev/null || true"
        zkm3 "cd $REMOTE_PATH && cp $backup_dir/.env.production . 2>/dev/null || true"
        
        # Restart with previous configuration
        zkm3 "cd $REMOTE_PATH && docker-compose down --timeout 30"
        zkm3 "cd $REMOTE_PATH && docker-compose up -d"
        
        print_status "Rollback completed. Please verify the application is working."
        log_message "Rollback completed using backup: $backup_dir"
    else
        print_warning "No backup found for rollback"
    fi
}

# Function to display deployment summary
display_deployment_summary() {
    local deployment_status=$1
    local new_version
    new_version=$(get_current_version)
    
    echo
    print_status "========================================================="
    if [ "$deployment_status" = "success" ]; then
        print_success "DEPLOYMENT COMPLETED SUCCESSFULLY"
    else
        print_error "DEPLOYMENT FAILED"
    fi
    print_status "========================================================="
    echo
    
    print_status "Deployment Summary:"
    echo "  Timestamp: $(date)"
    echo "  Version: $new_version"
    echo "  Remote Path: $REMOTE_PATH"
    echo "  Health Check URL: $HEALTH_CHECK_URL"
    echo "  Log File: $DEPLOYMENT_LOG"
    echo
    
    if [ "$deployment_status" = "success" ]; then
        print_status "Application Status:"
        zkm3 "cd $REMOTE_PATH && docker-compose ps"
        echo
        print_status "Next Steps:"
        echo "1. Monitor application: zkm3 'cd $REMOTE_PATH && docker-compose logs -f web'"
        echo "2. Check metrics: zkm3 'curl $HEALTH_CHECK_URL'"
        echo "3. View deployment log: cat $DEPLOYMENT_LOG"
    else
        print_status "Troubleshooting:"
        echo "1. Check logs: zkm3 'cd $REMOTE_PATH && docker-compose logs web'"
        echo "2. Check container status: zkm3 'cd $REMOTE_PATH && docker-compose ps'"
        echo "3. Manual rollback: zkm3 'cd $REMOTE_PATH && docker-compose down && docker-compose up -d'"
        echo "4. View deployment log: cat $DEPLOYMENT_LOG"
    fi
    
    log_message "Deployment summary displayed - Status: $deployment_status"
}

# Main deployment function
main() {
    print_status "J&A Business Solutions - Production Deployment Script"
    print_status "========================================================="
    print_status "Starting deployment at $(date)"
    print_status "Deployment log: $DEPLOYMENT_LOG"
    echo
    
    log_message "Deployment started"
    
    # Validation phase
    validate_local_environment
    test_vpc_connection
    
    # Backup phase
    backup_current_deployment
    
    # Deployment phase
    copy_files_to_remote
    manage_containers
    
    # Verification phase
    if perform_health_checks; then
        display_deployment_summary "success"
        log_message "Deployment completed successfully"
        return 0
    else
        rollback_deployment
        display_deployment_summary "failed"
        log_message "Deployment failed and rollback initiated"
        return 1
    fi
}

# Function to display usage information
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  --no-backup         Skip backup creation"
    echo "  --no-health-check   Skip health checks"
    echo "  --force             Force deployment even if health checks fail"
    echo "  --rollback          Rollback to previous deployment"
    echo
    echo "Examples:"
    echo "  $0                  # Normal deployment with all checks"
    echo "  $0 --force          # Force deployment"
    echo "  $0 --rollback       # Rollback to previous version"
    echo
}

# Parse command line arguments
SKIP_BACKUP=false
SKIP_HEALTH_CHECK=false
FORCE_DEPLOYMENT=false
ROLLBACK_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            usage
            exit 0
            ;;
        --no-backup)
            SKIP_BACKUP=true
            shift
            ;;
        --no-health-check)
            SKIP_HEALTH_CHECK=true
            shift
            ;;
        --force)
            FORCE_DEPLOYMENT=true
            shift
            ;;
        --rollback)
            ROLLBACK_ONLY=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Handle rollback-only mode
if [ "$ROLLBACK_ONLY" = true ]; then
    print_status "Rollback mode activated"
    rollback_deployment
    exit $?
fi

# Execute main deployment function
if main; then
    exit 0
else
    exit 1
fi