#!/bin/bash

# =============================================================================
# Production Configuration Validation Script
# =============================================================================
# This script validates the production environment configuration
# Run this before deploying to production

set -e

echo "🔍 Validating Production Configuration..."
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Validation results
ERRORS=0
WARNINGS=0

# Function to print error
print_error() {
    echo -e "${RED}❌ ERROR: $1${NC}"
    ((ERRORS++))
}

# Function to print warning
print_warning() {
    echo -e "${YELLOW}⚠️  WARNING: $1${NC}"
    ((WARNINGS++))
}

# Function to print success
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Check if required files exist
echo "📁 Checking required files..."

if [ -f ".env.production" ]; then
    print_success ".env.production file exists"
else
    print_error ".env.production file is missing"
fi

if [ -f "docker-compose.prod.yml" ]; then
    print_success "docker-compose.prod.yml file exists"
else
    print_error "docker-compose.prod.yml file is missing"
fi

if [ -d "logs" ]; then
    print_success "logs directory exists"
else
    print_error "logs directory is missing"
fi

if [ -d "public" ]; then
    print_success "public directory exists"
else
    print_error "public directory is missing"
fi

# Check environment variables in .env.production
echo ""
echo "🔧 Checking environment variables..."

if [ -f ".env.production" ]; then
    # Required variables
    REQUIRED_VARS=(
        "NODE_ENV"
        "NEXT_PUBLIC_SUPABASE_URL"
        "NEXT_PUBLIC_SUPABASE_ANON_KEY"
        "NEXT_PUBLIC_SENTRY_DSN"
        "NEXT_PUBLIC_SENTRY_ENVIRONMENT"
        "NEXT_PUBLIC_POSTHOG_API_KEY"
        "NEXT_PUBLIC_POSTHOG_API_HOST"
    )

    for var in "${REQUIRED_VARS[@]}"; do
        if grep -q "^${var}=" .env.production && ! grep -q "^${var}=your-" .env.production && ! grep -q "^${var}=$" .env.production; then
            print_success "$var is configured"
        else
            print_error "$var is missing or not configured properly"
        fi
    done

    # Check NODE_ENV is set to production
    if grep -q "^NODE_ENV=production" .env.production; then
        print_success "NODE_ENV is set to production"
    else
        print_error "NODE_ENV is not set to production"
    fi

    # Check for placeholder values
    if grep -q "your-" .env.production; then
        print_warning "Some environment variables still contain placeholder values"
    fi
fi

# Check Docker Compose configuration
echo ""
echo "🐳 Checking Docker Compose configuration..."

if [ -f "docker-compose.prod.yml" ]; then
    # Check if docker-compose is valid
    if docker-compose -f docker-compose.prod.yml config > /dev/null 2>&1; then
        print_success "Docker Compose configuration is valid"
    else
        print_error "Docker Compose configuration is invalid"
    fi

    # Check if image exists
    if docker-compose -f docker-compose.prod.yml config | grep -q "atemndobs/jna-amd64:v0.9"; then
        print_success "Docker image reference is correct"
    else
        print_warning "Docker image reference may need updating"
    fi
fi

# Check port availability
echo ""
echo "🌐 Checking port availability..."

if command -v netstat > /dev/null; then
    if netstat -tuln | grep -q ":8642 "; then
        print_warning "Port 8642 is already in use"
    else
        print_success "Port 8642 is available"
    fi
else
    print_warning "netstat not available, cannot check port 8642"
fi

# Check Docker availability
echo ""
echo "🐳 Checking Docker availability..."

if command -v docker > /dev/null; then
    if docker info > /dev/null 2>&1; then
        print_success "Docker is running and accessible"
    else
        print_error "Docker is not running or not accessible"
    fi
else
    print_error "Docker is not installed"
fi

if command -v docker-compose > /dev/null; then
    print_success "Docker Compose is available"
else
    print_error "Docker Compose is not installed"
fi

# Check disk space
echo ""
echo "💾 Checking disk space..."

AVAILABLE_SPACE=$(df . | tail -1 | awk '{print $4}')
REQUIRED_SPACE=1048576  # 1GB in KB

if [ "$AVAILABLE_SPACE" -gt "$REQUIRED_SPACE" ]; then
    print_success "Sufficient disk space available ($(($AVAILABLE_SPACE / 1024))MB)"
else
    print_warning "Low disk space: $(($AVAILABLE_SPACE / 1024))MB available, 1GB recommended"
fi

# Summary
echo ""
echo "📊 Validation Summary"
echo "===================="

if [ $ERRORS -eq 0 ] && [ $WARNINGS -eq 0 ]; then
    echo -e "${GREEN}🎉 All checks passed! Ready for production deployment.${NC}"
    exit 0
elif [ $ERRORS -eq 0 ]; then
    echo -e "${YELLOW}⚠️  $WARNINGS warning(s) found. Review before deployment.${NC}"
    exit 0
else
    echo -e "${RED}❌ $ERRORS error(s) and $WARNINGS warning(s) found. Fix errors before deployment.${NC}"
    exit 1
fi