# J&A Business Solutions - Deployment Scripts

This directory contains scripts for building and deploying the J&A Business Solutions Next.js application to production.

## Scripts Overview

### 1. `build-and-push.sh`
Builds the Docker image for Linux/AMD64 platform and pushes it to the container registry.

**Features:**
- Automatic version management with semantic versioning
- Comprehensive error handling and validation
- Build verification and testing
- Registry authentication checks
- Git tag creation and management
- Docker Compose file updates

**Usage:**
```bash
# Interactive mode (recommended)
./scripts/build-and-push.sh

# Command line options
./scripts/build-and-push.sh --patch    # Build next patch version
./scripts/build-and-push.sh --minor    # Build next minor version
./scripts/build-and-push.sh --major    # Build next major version
./scripts/build-and-push.sh --fast     # Skip tests, build current version
./scripts/build-and-push.sh --help     # Show help
```

### 2. `deploy-production.sh`
Deploys the application to the production VPC using the zkm3 alias command.

**Features:**
- Connects to VPC using zkm3 alias command
- Copies .env and docker-compose files to `/home/<USER>/docker/jna-business-solutions`
- Manages containers (stop, pull, start) in the application directory
- Comprehensive health check verification after deployment
- Automatic backup and rollback capabilities
- Deployment logging and monitoring

**Usage:**
```bash
# Normal deployment with all checks
./scripts/deploy-production.sh

# Command line options
./scripts/deploy-production.sh --no-backup        # Skip backup creation
./scripts/deploy-production.sh --no-health-check  # Skip health checks
./scripts/deploy-production.sh --force            # Force deployment
./scripts/deploy-production.sh --rollback         # Rollback to previous version
./scripts/deploy-production.sh --help             # Show help
```

### 3. `validate-deployment-scripts.sh`
Validates the deployment scripts and environment without executing them.

**Usage:**
```bash
./scripts/validate-deployment-scripts.sh
```

### 4. `validate-production-config.sh`
Validates the production configuration and environment variables.

**Usage:**
```bash
./scripts/validate-production-config.sh
```

## Prerequisites

### For Building (`build-and-push.sh`)
- Docker Desktop installed and running
- Docker buildx available
- Docker Hub account and authentication
- Node.js and npm installed
- All project dependencies installed (`npm ci`)

### For Deployment (`deploy-production.sh`)
- `zkm3` alias command configured for VPC access
- SSH access to production server
- `.env.production` file configured with production values
- `docker-compose.prod.yml` file present

## Configuration Files

### Required Files
- `Dockerfile` - Multi-stage Docker build configuration
- `docker-compose.prod.yml` - Production Docker Compose configuration
- `.env.production` - Production environment variables
- `package.json` - Node.js project configuration
- `next.config.js` - Next.js configuration

### Environment Variables
The `.env.production` file must contain:
```bash
NODE_ENV=production
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_key
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn
NEXT_PUBLIC_POSTHOG_API_KEY=your_posthog_key
# ... other required variables
```

## Deployment Workflow

### 1. Build Phase
```bash
# Build and push new Docker image
./scripts/build-and-push.sh --patch
```

### 2. Validation Phase
```bash
# Validate scripts and configuration
./scripts/validate-deployment-scripts.sh
./scripts/validate-production-config.sh
```

### 3. Deployment Phase
```bash
# Deploy to production
./scripts/deploy-production.sh
```

### 4. Monitoring Phase
```bash
# Monitor deployment logs
zkm3 'cd /home/<USER>/docker/jna-business-solutions && docker-compose logs -f web'

# Check application health
zkm3 'curl http://localhost:8642/api/health'
```

## Troubleshooting

### Build Issues
1. Check Docker is running: `docker info`
2. Verify Docker Hub authentication: `docker login`
3. Check project dependencies: `npm ci`
4. Validate TypeScript: `npx tsc --noEmit`
5. Test local build: `npm run build`

### Deployment Issues
1. Test VPC connection: `zkm3 'echo "test"`
2. Check environment file: `cat .env.production`
3. Verify Docker Compose file: `docker-compose -f docker-compose.prod.yml config`
4. Check container logs: `zkm3 'cd /home/<USER>/docker/jna-business-solutions && docker-compose logs web'`

### Rollback Procedure
```bash
# Automatic rollback
./scripts/deploy-production.sh --rollback

# Manual rollback
zkm3 'cd /home/<USER>/docker/jna-business-solutions && docker-compose down'
zkm3 'cd /home/<USER>/docker/jna-business-solutions && cp backup-*/docker-compose.yml .'
zkm3 'cd /home/<USER>/docker/jna-business-solutions && docker-compose up -d'
```

## Security Considerations

- Environment variables are securely transferred via SCP
- Containers run as non-root user (1000:1000)
- Health checks verify application security
- Backup files are created before each deployment
- All scripts include comprehensive error handling

## Monitoring and Logging

- Deployment logs are saved to `/tmp/jna-deployment-YYYYMMDD-HHMMSS.log`
- Container logs are available via `docker-compose logs`
- Health checks verify application availability
- Metrics are available via monitoring container on port 9100

## Support

For issues with deployment scripts:
1. Check the deployment log file
2. Verify all prerequisites are met
3. Run validation scripts to identify issues
4. Check container logs for application-specific errors
5. Use rollback functionality if needed