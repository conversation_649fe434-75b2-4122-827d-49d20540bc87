#!/usr/bin/env node

/**
 * Environment Variable Validation Script
 * Validates environment configuration for security and completeness
 */

const fs = require('fs');
const path = require('path');

// Environment variable validation rules (mirrored from TypeScript version)
const ENV_VALIDATION_RULES = {
  // Node.js Environment
  NODE_ENV: {
    required: true,
    allowedValues: ['development', 'production', 'test'],
    description: 'Node.js environment mode',
  },
  
  // Supabase Configuration (Public)
  NEXT_PUBLIC_SUPABASE_URL: {
    required: true,
    pattern: /^https:\/\/[a-z0-9]+\.supabase\.co$/,
    description: 'Supabase project URL',
  },
  NEXT_PUBLIC_SUPABASE_ANON_KEY: {
    required: true,
    pattern: /^eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+$/,
    minLength: 100,
    description: 'Supabase anonymous key (JWT)',
  },
  
  // Sentry Configuration (Public)
  NEXT_PUBLIC_SENTRY_DSN: {
    required: true,
    pattern: /^https:\/\/[a-f0-9]+@[a-z0-9-]+\.ingest\.(us\.)?sentry\.io\/[0-9]+$/,
    description: 'Sentry Data Source Name',
  },
  NEXT_PUBLIC_SENTRY_ENVIRONMENT: {
    required: true,
    allowedValues: ['development', 'staging', 'production'],
    description: 'Sentry environment identifier',
  },
  
  // PostHog Configuration (Public)
  NEXT_PUBLIC_POSTHOG_API_KEY: {
    required: true,
    pattern: /^phc_[a-zA-Z0-9]+$/,
    minLength: 20,
    description: 'PostHog API key',
  },
  NEXT_PUBLIC_POSTHOG_API_HOST: {
    required: true,
    pattern: /^https:\/\/.+/,
    description: 'PostHog API host URL',
  },
};

// Sensitive variables that should never be exposed
const SENSITIVE_SERVER_ONLY_VARS = [
  'SENTRY_AUTH_TOKEN',
  'SUPABASE_SERVICE_ROLE_KEY',
  'DATABASE_URL',
  'NEXTAUTH_SECRET',
  'WEBHOOK_SECRET',
  'API_SECRET_KEY',
  'PRIVATE_KEY',
  'JWT_SECRET',
];

/**
 * Checks if a value appears to be a placeholder
 */
function isPlaceholderValue(value) {
  const placeholderPatterns = [
    /your_.*_here/i,
    /replace.*with.*actual/i,
    /todo.*replace/i,
    /example/i,
    /test.*key/i,
    /dummy/i,
    /placeholder/i,
    /changeme/i,
    /default/i,
  ];
  
  return placeholderPatterns.some(pattern => pattern.test(value));
}

/**
 * Checks if a value appears to be a development/test value
 */
function isDevelopmentValue(value) {
  const devPatterns = [
    /localhost/i,
    /127\.0\.0\.1/i,
    /dev/i,
    /test/i,
    /staging/i,
    /demo/i,
  ];
  
  return devPatterns.some(pattern => pattern.test(value));
}

/**
 * Validates a single environment variable
 */
function validateSingleVar(name, value, rule) {
  const errors = [];
  const warnings = [];
  const securityIssues = [];
  
  // Check if required variable is missing
  if (rule.required && (!value || value.trim() === '')) {
    errors.push(`Missing required environment variable: ${name}`);
    return { errors, warnings, securityIssues };
  }
  
  // Skip validation if variable is not set and not required
  if (!value || value.trim() === '') {
    return { errors, warnings, securityIssues };
  }
  
  const trimmedValue = value.trim();
  
  // Validate pattern
  if (rule.pattern && !rule.pattern.test(trimmedValue)) {
    errors.push(`Invalid format for ${name}: ${rule.description}`);
  }
  
  // Validate length
  if (rule.minLength && trimmedValue.length < rule.minLength) {
    errors.push(`${name} is too short (minimum ${rule.minLength} characters)`);
  }
  
  if (rule.maxLength && trimmedValue.length > rule.maxLength) {
    errors.push(`${name} is too long (maximum ${rule.maxLength} characters)`);
  }
  
  // Validate allowed values
  if (rule.allowedValues && !rule.allowedValues.includes(trimmedValue)) {
    errors.push(`Invalid value for ${name}. Allowed values: ${rule.allowedValues.join(', ')}`);
  }
  
  // Check for placeholder values in production
  if (process.env.NODE_ENV === 'production' && isPlaceholderValue(trimmedValue)) {
    securityIssues.push(`Production environment contains placeholder value for ${name}`);
  }
  
  // Check for development values in production
  if (process.env.NODE_ENV === 'production' && isDevelopmentValue(trimmedValue)) {
    warnings.push(`${name} appears to contain development/test values in production`);
  }
  
  return { errors, warnings, securityIssues };
}

/**
 * Validates all environment variables
 */
function validateAllEnvironmentVariables() {
  const errors = [];
  const warnings = [];
  const securityIssues = [];
  
  console.log('🔍 Validating environment variables...\n');
  
  // Validate each environment variable
  Object.entries(ENV_VALIDATION_RULES).forEach(([name, rule]) => {
    const value = process.env[name];
    const result = validateSingleVar(name, value, rule);
    
    errors.push(...result.errors);
    warnings.push(...result.warnings);
    securityIssues.push(...result.securityIssues);
    
    // Log individual variable status
    if (result.errors.length > 0) {
      console.log(`❌ ${name}: ${result.errors.join(', ')}`);
    } else if (result.warnings.length > 0) {
      console.log(`⚠️  ${name}: ${result.warnings.join(', ')}`);
    } else if (value) {
      console.log(`✅ ${name}: Valid`);
    } else {
      console.log(`ℹ️  ${name}: Not set (optional)`);
    }
  });
  
  // Check for sensitive variables exposure
  console.log('\n🔒 Checking for sensitive variable exposure...');
  SENSITIVE_SERVER_ONLY_VARS.forEach(varName => {
    if (process.env[varName]) {
      console.log(`⚠️  ${varName}: Present (server-only)`);
    }
  });
  
  // Cross-variable validations
  console.log('\n🔗 Performing cross-variable validation...');
  
  const nodeEnv = process.env.NODE_ENV;
  const sentryEnv = process.env.NEXT_PUBLIC_SENTRY_ENVIRONMENT;
  
  if (nodeEnv === 'production' && sentryEnv !== 'production') {
    warnings.push('NODE_ENV is production but SENTRY_ENVIRONMENT is not');
    console.log('⚠️  Environment mismatch: NODE_ENV vs SENTRY_ENVIRONMENT');
  }
  
  // Check for mixed environment configurations
  if (nodeEnv === 'production') {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const posthogHost = process.env.NEXT_PUBLIC_POSTHOG_API_HOST;
    
    if (supabaseUrl?.includes('localhost') || posthogHost?.includes('localhost')) {
      securityIssues.push('Production environment contains localhost URLs');
      console.log('🚨 Security Issue: Production environment contains localhost URLs');
    }
  }
  
  return {
    isValid: errors.length === 0 && securityIssues.length === 0,
    errors,
    warnings,
    securityIssues,
  };
}

/**
 * Loads environment variables from file
 */
function loadEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return {};
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const env = {};
  
  content.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        env[key.trim()] = valueParts.join('=').trim();
      }
    }
  });
  
  return env;
}

/**
 * Main validation function
 */
function main() {
  console.log('🛡️  Environment Variable Security Validation\n');
  console.log('='.repeat(50));
  
  // Determine which environment file to load
  const envFile = process.argv[2] || '.env.local';
  const envPath = path.resolve(process.cwd(), envFile);
  
  console.log(`📁 Loading environment from: ${envFile}`);
  
  // Load environment variables from file
  const envVars = loadEnvFile(envPath);
  
  // Merge with process.env (process.env takes precedence)
  Object.entries(envVars).forEach(([key, value]) => {
    if (!process.env[key]) {
      process.env[key] = value;
    }
  });
  
  console.log(`📊 Total environment variables: ${Object.keys(process.env).length}`);
  console.log(`📊 Public variables: ${Object.keys(process.env).filter(k => k.startsWith('NEXT_PUBLIC_')).length}`);
  console.log(`📊 Server-only variables: ${Object.keys(process.env).filter(k => !k.startsWith('NEXT_PUBLIC_')).length}\n`);
  
  // Perform validation
  const result = validateAllEnvironmentVariables();
  
  // Print summary
  console.log('\n' + '='.repeat(50));
  console.log('📋 VALIDATION SUMMARY');
  console.log('='.repeat(50));
  
  if (result.isValid) {
    console.log('✅ Environment validation PASSED');
  } else {
    console.log('❌ Environment validation FAILED');
  }
  
  if (result.errors.length > 0) {
    console.log(`\n🔴 ERRORS (${result.errors.length}):`);
    result.errors.forEach(error => console.log(`  • ${error}`));
  }
  
  if (result.warnings.length > 0) {
    console.log(`\n🟡 WARNINGS (${result.warnings.length}):`);
    result.warnings.forEach(warning => console.log(`  • ${warning}`));
  }
  
  if (result.securityIssues.length > 0) {
    console.log(`\n🚨 SECURITY ISSUES (${result.securityIssues.length}):`);
    result.securityIssues.forEach(issue => console.log(`  • ${issue}`));
  }
  
  // Exit with appropriate code
  if (result.securityIssues.length > 0 || result.errors.length > 0) {
    console.log('\n💥 Validation failed - please fix the issues above');
    process.exit(1);
  } else if (result.warnings.length > 0) {
    console.log('\n⚠️  Validation passed with warnings');
    process.exit(0);
  } else {
    console.log('\n🎉 All checks passed!');
    process.exit(0);
  }
}

// Run the validation
if (require.main === module) {
  main();
}

module.exports = {
  validateAllEnvironmentVariables,
  isPlaceholderValue,
  isDevelopmentValue,
};