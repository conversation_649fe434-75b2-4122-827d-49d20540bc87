# Dependencies
node_modules
npm-debug.log

# Build output
build
dist/
.next/

# Environment files (exclude all except production)
.env*
!.env.production
!.env.example

# Git and version control
.git
.gitignore
.gitattributes

# IDE and editor files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Testing
coverage/
.nyc_output
test-results/
playwright-report/

# Local development
.cache/
.parcel-cache/

# Documentation
README.md
CHANGELOG.md
*.md
docs/

# Kiro configuration
.kiro/

# Scripts (not needed in container)
scripts/

# Temporary files
*.tmp
*.temp
