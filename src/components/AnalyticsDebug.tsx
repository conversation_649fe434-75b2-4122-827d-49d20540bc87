/**
 * Simple Analytics Debug Component
 * Only shows in development mode
 */

'use client';

import React, { useState, useEffect } from 'react';
import { 
  debugAnalytics, 
  trackEvent, 
  isAnalyticsReady,
  getPostHogInstance,
  handleConsentChange
} from '../lib/analytics';
import { isDevelopment } from '../lib/env';

export function AnalyticsDebug() {
  const [debugInfo, setDebugInfo] = useState({
    ready: false,
    hasConsent: false,
    distinctId: '',
    sessionId: ''
  });

  // Only show in development
  if (!isDevelopment()) {
    return null;
  }

  const updateDebugInfo = () => {
    const posthog = getPostHogInstance();
    const hasConsent = typeof window !== 'undefined' && 
      (() => {
        try {
          const consent = localStorage.getItem('ja_privacy_consent');
          return consent ? JSON.parse(consent).analytics === true : false;
        } catch {
          return false;
        }
      })();

    setDebugInfo({
      ready: isAnalyticsReady(),
      hasConsent,
      distinctId: posthog?.get_distinct_id?.() || '',
      sessionId: posthog?.get_session_id?.() || ''
    });
  };

  useEffect(() => {
    updateDebugInfo();
    const interval = setInterval(updateDebugInfo, 2000);
    return () => clearInterval(interval);
  }, []);

  const sendTestEvent = () => {
    trackEvent('cta_click', {
      cta_type: 'debug_test',
      location: 'debug_component',
      text: 'Test Button'
    });
    console.log('🧪 Test event sent');
  };

  const toggleConsent = () => {
    const newConsent = !debugInfo.hasConsent;
    
    // Update localStorage
    localStorage.setItem('ja_privacy_consent', JSON.stringify({
      analytics: newConsent,
      timestamp: new Date().toISOString()
    }));
    
    // Update PostHog
    handleConsentChange(newConsent);
    
    // Update debug info
    setTimeout(updateDebugInfo, 500);
  };

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-sm z-50">
      <h3 className="font-bold text-sm mb-2">📊 Analytics Debug</h3>
      
      <div className="text-xs space-y-1 mb-3">
        <div>Ready: <span className={debugInfo.ready ? 'text-green-600' : 'text-red-600'}>
          {debugInfo.ready ? '✅' : '❌'}
        </span></div>
        <div>Consent: <span className={debugInfo.hasConsent ? 'text-green-600' : 'text-red-600'}>
          {debugInfo.hasConsent ? '✅' : '❌'}
        </span></div>
        <div>Distinct ID: {debugInfo.distinctId}</div>
        <div>Session ID: {debugInfo.sessionId}</div>
      </div>

      <div className="space-y-2">
        <button
          onClick={toggleConsent}
          className={`w-full px-3 py-1 rounded text-xs text-white ${
            debugInfo.hasConsent 
              ? 'bg-red-500 hover:bg-red-600' 
              : 'bg-green-500 hover:bg-green-600'
          }`}
        >
          {debugInfo.hasConsent ? '🚫 Withdraw Consent' : '✅ Grant Consent'}
        </button>
        
        <button
          onClick={sendTestEvent}
          className="w-full bg-blue-500 text-white px-3 py-1 rounded text-xs hover:bg-blue-600"
        >
          🧪 Send Test Event
        </button>
        
        <button
          onClick={debugAnalytics}
          className="w-full bg-gray-500 text-white px-3 py-1 rounded text-xs hover:bg-gray-600"
        >
          🔍 Log Debug Info
        </button>
      </div>
    </div>
  );
}