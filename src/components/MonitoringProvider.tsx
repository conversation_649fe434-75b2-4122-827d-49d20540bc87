/**
 * Monitoring provider component that initializes and manages all monitoring services
 */

'use client';

import React from 'react';
import { useMonitoring } from '../lib/hooks/useMonitoring';
import { isDevelopment } from '../lib/env';
import MonitoringDashboard from './dev/MonitoringDashboard';

interface MonitoringProviderProps {
  children: React.ReactNode;
  showDashboard?: boolean;
}

const MonitoringProvider: React.FC<MonitoringProviderProps> = ({ 
  children, 
  showDashboard = isDevelopment() 
}) => {
  const monitoring = useMonitoring(true); // Auto-initialize

  // Log monitoring status in development
  React.useEffect(() => {
    if (isDevelopment() && monitoring.isInitialized) {
      console.log('📊 Monitoring Provider Status:', {
        initialized: monitoring.isInitialized,
        config: monitoring.config,
        health: monitoring.healthStatus,
        error: monitoring.error,
      });
    }
  }, [monitoring.isInitialized, monitoring.config, monitoring.healthStatus, monitoring.error]);

  // Report initialization errors
  React.useEffect(() => {
    if (monitoring.error) {
      console.error('📊 Monitoring initialization error:', monitoring.error);
      
      // Report to monitoring service if possible
      monitoring.reportIssue('sentry', `Monitoring initialization failed: ${monitoring.error}`, 'high');
    }
  }, [monitoring.error, monitoring.reportIssue]);

  return (
    <>
      {children}
      
      {/* Development monitoring dashboard */}
      {showDashboard && isDevelopment() && (
        <div className="fixed bottom-4 right-4 z-50 max-w-md">
          <MonitoringDashboard />
        </div>
      )}
    </>
  );
};

export default MonitoringProvider;