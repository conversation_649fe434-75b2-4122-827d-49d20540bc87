'use client'

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { initSessionTracking, trackPageView } from '../lib/sessionTracking'

interface SessionTrackingProviderProps {
  children: React.ReactNode
}

export function SessionTrackingProvider({ children }: SessionTrackingProviderProps) {
  const pathname = usePathname()

  useEffect(() => {
    // Initialize session tracking
    initSessionTracking()
  }, [])

  useEffect(() => {
    // Track page views on route changes
    if (pathname) {
      const title = document.title || 'J&A Business Solutions LLC'
      const section = pathname === '/' ? 'home' : pathname.replace('/', '')
      
      trackPageView(window.location.href, title, section)
    }
  }, [pathname])

  return <>{children}</>
}