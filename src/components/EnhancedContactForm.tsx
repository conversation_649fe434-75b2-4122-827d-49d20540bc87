/**
 * Enhanced Contact Form demonstrating UniversalErrorWrapper integration
 * 
 * This component shows how to use the UniversalErrorWrapper in React components
 * for comprehensive error handling with user interaction tracking.
 */

import React, { useState, useCallback } from 'react';
import { 
  universalErrorWrapper,
  wrapAsync,
  wrapSync
} from '../lib/UniversalErrorWrapper';
import { EnhancedContactService, type ContactCreateData } from '../lib/services/EnhancedContactService';
import { 
  BusinessOperation
} from '../lib/errorHandling';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Alert } from './ui/alert';

interface FormData {
  name: string;
  email: string;
  message: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  message?: string;
  general?: string;
}

const EnhancedContactFormComponent: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    message: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const contactService = new EnhancedContactService();
  


  /**
   * Handle form field changes with error handling
   */
  const handleFieldChange = useCallback((field: keyof FormData, value: string) => {
    wrapSync(
      'form_field_change',
      () => {
        // Record user journey step
        universalErrorWrapper.recordUserJourneyStep(`form_field_${field}_change`);

        // Clear field-specific error when user starts typing
        if (errors[field]) {
          setErrors(prev => ({ ...prev, [field]: undefined }));
        }

        // Update form data
        setFormData(prev => ({ ...prev, [field]: value }));

        // Real-time validation for better UX
        if (field === 'email' && value.trim()) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(value)) {
            setErrors(prev => ({ ...prev, email: 'Please enter a valid email address' }));
          }
        }

        if (field === 'message' && value.trim() && value.length < 10) {
          setErrors(prev => ({ ...prev, message: 'Message must be at least 10 characters long' }));
        }
      },
      {
        entityType: 'form_field',
        entityId: field,
        customContext: {
          field_name: field,
          value_length: value.length,
          has_content: !!value.trim(),
        },
        context: {
          business: {
            operation: 'form_interaction',
            entityType: 'contact_form',
            workflow: 'field_change',
          },
        },
      }
    );
  }, [errors]);

  /**
   * Validate form data with comprehensive error handling
   */
  const validateForm = useCallback((): boolean => {
    return wrapSync(
      'form_validation',
      () => {
        const newErrors: FormErrors = {};

        // Name validation
        if (!formData.name.trim()) {
          newErrors.name = 'Name is required';
        } else if (formData.name.length > 100) {
          newErrors.name = 'Name is too long (max 100 characters)';
        }

        // Email validation
        if (!formData.email.trim()) {
          newErrors.email = 'Email is required';
        } else {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(formData.email)) {
            newErrors.email = 'Please enter a valid email address';
          }
        }

        // Message validation
        if (!formData.message.trim()) {
          newErrors.message = 'Message is required';
        } else if (formData.message.length < 10) {
          newErrors.message = 'Message must be at least 10 characters long';
        } else if (formData.message.length > 5000) {
          newErrors.message = 'Message is too long (max 5000 characters)';
        }

        // Check for spam patterns
        const spamPatterns = ['http://', 'https://', 'www.', 'click here', 'buy now'];
        const hasSpamPattern = spamPatterns.some(pattern => 
          formData.message.toLowerCase().includes(pattern)
        );
        if (hasSpamPattern) {
          newErrors.message = 'Message contains suspicious content';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
      },
      {
        entityType: 'contact_form',
        entityId: 'validation',
        customContext: {
          name_length: formData.name.length,
          email_domain: formData.email.split('@')[1] || 'unknown',
          message_length: formData.message.length,
        },
        context: {
          business: {
            operation: 'form_validation',
            entityType: 'contact_form',
            workflow: 'client_side_validation',
          },
        },
      }
    );
  }, [formData]);

  /**
   * Handle form submission with comprehensive error handling
   */
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    // Record user journey step
    universalErrorWrapper.recordUserJourneyStep('contact_form_submit_attempt');

    // Clear previous errors
    setErrors({});
    setSubmitSuccess(false);

    // Validate form
    const isValid = validateForm();
    if (!isValid) {
      return;
    }

    setIsSubmitting(true);

    try {
      await wrapAsync(
        'contact_form_submission',
        async () => {
          const contactData: ContactCreateData = {
            name: formData.name.trim(),
            email: formData.email.trim().toLowerCase(),
            message: formData.message.trim(),
          };

          // Submit the form
          const result = await contactService.createContactSubmission(contactData);

          // Record successful submission
          universalErrorWrapper.recordUserJourneyStep('contact_form_submit_success');

          // Reset form and show success
          setFormData({ name: '', email: '', message: '' });
          setSubmitSuccess(true);

          return result;
        },
        {
          entityType: 'contact_form',
          entityId: 'submission',
          timeout: 15000, // 15 second timeout
          customContext: {
            email_domain: formData.email.split('@')[1] || 'unknown',
            message_length: formData.message.length,
            name_length: formData.name.length,
            form_completion_time: Date.now(), // Could track how long user took to fill form
          },
          context: {
            business: {
              operation: BusinessOperation.CONTACT_FORM_SUBMIT,
              entityType: 'contact_form',
              workflow: 'form_submission',
            },
            user: {
              email: formData.email,
            },
          },
        }
      );
    } catch (error) {
      // Record failed submission
      universalErrorWrapper.recordUserJourneyStep('contact_form_submit_error');

      // Handle different error types for user-friendly messages
      const errorMessage = (error as Error).message;
      
      if (errorMessage.includes('validation')) {
        // Handle validation errors
        if (errorMessage.includes('email')) {
          setErrors({ email: errorMessage });
        } else if (errorMessage.includes('name')) {
          setErrors({ name: errorMessage });
        } else if (errorMessage.includes('message')) {
          setErrors({ message: errorMessage });
        } else {
          setErrors({ general: errorMessage });
        }
      } else if (errorMessage.includes('rate limit') || errorMessage.includes('recently')) {
        setErrors({ 
          general: 'You have already submitted a message recently. Please wait 24 hours before submitting again.' 
        });
      } else if (errorMessage.includes('timeout')) {
        setErrors({ 
          general: 'The request timed out. Please check your connection and try again.' 
        });
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        setErrors({ 
          general: 'Network error. Please check your connection and try again.' 
        });
      } else {
        setErrors({ 
          general: 'An unexpected error occurred. Please try again later.' 
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, validateForm, contactService]);

  /**
   * Handle form reset with tracking
   */
  const handleReset = useCallback(() => {
    wrapSync(
      'form_reset',
      () => {
        universalErrorWrapper.recordUserJourneyStep('contact_form_reset');
        setFormData({ name: '', email: '', message: '' });
        setErrors({});
        setSubmitSuccess(false);
      },
      {
        entityType: 'contact_form',
        entityId: 'reset',
        context: {
          business: {
            operation: 'form_reset',
            entityType: 'contact_form',
            workflow: 'user_reset',
          },
        },
      }
    );
  }, []);

  return (
    <Card className="w-full max-w-2xl mx-auto p-6">
      <h2 className="text-2xl font-bold mb-6 text-center">Contact Us</h2>
      
      {submitSuccess && (
        <Alert className="mb-6 bg-green-50 border-green-200 text-green-800">
          <div className="font-medium">Message sent successfully!</div>
          <div className="text-sm mt-1">Thank you for contacting us. We'll get back to you soon.</div>
        </Alert>
      )}

      {errors.general && (
        <Alert className="mb-6 bg-red-50 border-red-200 text-red-800">
          <div className="font-medium">Error</div>
          <div className="text-sm mt-1">{errors.general}</div>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            Name *
          </label>
          <input
            type="text"
            id="name"
            value={formData.name}
            onChange={(e) => handleFieldChange('name', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.name ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Your full name"
            maxLength={100}
            disabled={isSubmitting}
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
            Email *
          </label>
          <input
            type="email"
            id="email"
            value={formData.email}
            onChange={(e) => handleFieldChange('email', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.email ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="<EMAIL>"
            disabled={isSubmitting}
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email}</p>
          )}
        </div>

        <div>
          <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
            Message *
          </label>
          <textarea
            id="message"
            value={formData.message}
            onChange={(e) => handleFieldChange('message', e.target.value)}
            rows={6}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.message ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Please describe how we can help you..."
            maxLength={5000}
            disabled={isSubmitting}
          />
          <div className="flex justify-between items-center mt-1">
            {errors.message ? (
              <p className="text-sm text-red-600">{errors.message}</p>
            ) : (
              <p className="text-sm text-gray-500">
                Minimum 10 characters ({formData.message.length}/5000)
              </p>
            )}
          </div>
        </div>

        <div className="flex gap-4">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Sending...' : 'Send Message'}
          </Button>
          
          <Button
            type="button"
            onClick={handleReset}
            disabled={isSubmitting}
            className="px-6 bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Reset
          </Button>
        </div>
      </form>

      <div className="mt-6 text-center text-sm text-gray-500">
        <p>* Required fields</p>
        <p className="mt-1">We typically respond within 24 hours.</p>
      </div>
    </Card>
  );
};

export default EnhancedContactFormComponent;