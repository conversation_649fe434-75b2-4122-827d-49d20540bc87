'use client'

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, <PERSON>ie } from 'lucide-react';

interface ConsentPreferences {
  analytics: boolean;
  marketing: boolean;
  functional: boolean;
}

interface PrivacyConsentProps {
  onConsentChange?: (preferences: ConsentPreferences) => void;
}

export const PrivacyConsent: React.FC<PrivacyConsentProps> = ({ onConsentChange }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [preferences, setPreferences] = useState<ConsentPreferences>({
    analytics: false,
    marketing: false,
    functional: true, // Essential cookies always enabled
  });

  useEffect(() => {
    // Check if user has already made a consent choice
    const consentData = localStorage.getItem('ja_privacy_consent');
    if (!consentData) {
      // Show consent banner after a short delay
      const timer = setTimeout(() => setIsVisible(true), 2000);
      return () => clearTimeout(timer);
    } else {
      // Load existing preferences
      try {
        const savedPreferences = JSON.parse(consentData);
        setPreferences(savedPreferences);
        onConsentChange?.(savedPreferences);
      } catch (error) {
        console.error('Error loading consent preferences:', error);
      }
    }
  }, [onConsentChange]);

  const saveConsent = (newPreferences: ConsentPreferences) => {
    const consentData = {
      ...newPreferences,
      timestamp: new Date().toISOString(),
      version: '1.0',
    };

    localStorage.setItem('ja_privacy_consent', JSON.stringify(consentData));
    setPreferences(newPreferences);
    onConsentChange?.(newPreferences);
    setIsVisible(false);
  };

  const handleAcceptAll = () => {
    saveConsent({
      analytics: true,
      marketing: true,
      functional: true,
    });
  };

  const handleRejectAll = () => {
    saveConsent({
      analytics: false,
      marketing: false,
      functional: true, // Essential cookies cannot be disabled
    });
  };

  const handleSavePreferences = () => {
    saveConsent(preferences);
  };

  const updatePreference = (key: keyof ConsentPreferences, value: boolean) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-end justify-center p-4 bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-lg shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Shield className="w-6 h-6 text-gold-500 mr-3" />
            <h2 className="text-xl font-bold text-navy-900">Privacy & Cookies</h2>
          </div>
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <p className="text-navy-700 mb-6">
            We use cookies and similar technologies to enhance your experience, analyze site usage, 
            and assist with our marketing efforts. You can customize your preferences below.
          </p>

          {!showDetails ? (
            /* Simple View */
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <Cookie className="w-5 h-5 text-gold-500 mr-3" />
                  <div>
                    <h3 className="font-medium text-navy-900">Cookie Preferences</h3>
                    <p className="text-sm text-navy-600">
                      Choose which cookies you'd like to allow
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setShowDetails(true)}
                  className="text-gold-500 hover:text-gold-600 text-sm font-medium"
                >
                  Customize
                </button>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={handleAcceptAll}
                  className="flex-1 bg-gold-500 hover:bg-gold-600 text-white px-6 py-3 rounded-md font-medium transition-colors"
                >
                  Accept All
                </button>
                <button
                  onClick={handleRejectAll}
                  className="flex-1 bg-gray-200 hover:bg-gray-300 text-navy-900 px-6 py-3 rounded-md font-medium transition-colors"
                >
                  Reject All
                </button>
              </div>
            </div>
          ) : (
            /* Detailed View */
            <div className="space-y-6">
              <button
                onClick={() => setShowDetails(false)}
                className="text-gold-500 hover:text-gold-600 text-sm font-medium mb-4"
              >
                ← Back to simple view
              </button>

              {/* Essential Cookies */}
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-navy-900">Essential Cookies</h3>
                  <span className="text-sm text-green-600 font-medium">Always Active</span>
                </div>
                <p className="text-sm text-navy-600 mb-3">
                  These cookies are necessary for the website to function and cannot be disabled.
                  They include session management, security, and basic functionality.
                </p>
                <div className="text-xs text-navy-500">
                  Examples: Session cookies, security tokens, preference settings
                </div>
              </div>

              {/* Analytics Cookies */}
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-navy-900">Analytics Cookies</h3>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.analytics}
                      onChange={(e) => updatePreference('analytics', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-gold-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold-500"></div>
                  </label>
                </div>
                <p className="text-sm text-navy-600 mb-3">
                  Help us understand how visitors interact with our website by collecting 
                  anonymous usage data and performance metrics.
                </p>
                <div className="text-xs text-navy-500">
                  Examples: Page views, click tracking, scroll depth, session duration
                </div>
              </div>

              {/* Marketing Cookies */}
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-navy-900">Marketing Cookies</h3>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.marketing}
                      onChange={(e) => updatePreference('marketing', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-gold-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold-500"></div>
                  </label>
                </div>
                <p className="text-sm text-navy-600 mb-3">
                  Allow us to show you relevant content and advertisements based on your interests 
                  and browsing behavior.
                </p>
                <div className="text-xs text-navy-500">
                  Examples: Advertising cookies, social media integration, remarketing
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 pt-4">
                <button
                  onClick={handleSavePreferences}
                  className="flex-1 bg-gold-500 hover:bg-gold-600 text-white px-6 py-3 rounded-md font-medium transition-colors"
                >
                  Save Preferences
                </button>
                <button
                  onClick={handleAcceptAll}
                  className="flex-1 bg-gray-200 hover:bg-gray-300 text-navy-900 px-6 py-3 rounded-md font-medium transition-colors"
                >
                  Accept All
                </button>
              </div>
            </div>
          )}

          {/* Privacy Policy Link */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <p className="text-xs text-navy-500 text-center">
              By using our website, you agree to our use of cookies as described in our{' '}
              <a href="/privacy-policy" className="text-gold-500 hover:text-gold-600 underline">
                Privacy Policy
              </a>
              . You can change your preferences at any time.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Hook to manage privacy consent state
 */
export function usePrivacyConsent() {
  const [consent, setConsent] = useState<ConsentPreferences | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Load consent preferences from localStorage
    try {
      const consentData = localStorage.getItem('ja_privacy_consent');
      if (consentData) {
        const parsed = JSON.parse(consentData);
        setConsent(parsed);
      }
    } catch (error) {
      console.error('Error loading consent preferences:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateConsent = (preferences: ConsentPreferences) => {
    setConsent(preferences);
  };

  const hasConsent = (type: keyof ConsentPreferences): boolean => {
    return consent?.[type] ?? false;
  };

  const clearConsent = () => {
    localStorage.removeItem('ja_privacy_consent');
    setConsent(null);
  };

  return {
    consent,
    isLoading,
    hasConsent,
    updateConsent,
    clearConsent,
  };
}