import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import * as Sentry from '@sentry/nextjs';
import ErrorBoundary, { withErrorBoundary } from '../ErrorBoundary';
import { NetworkError, ValidationError, DatabaseError, BusinessLogicError } from '../../lib/errorHandling';

// Mock Sentry
jest.mock('@sentry/nextjs', () => ({
  captureException: jest.fn(),
  withScope: jest.fn((callback) => callback(mockScope)),
  addBreadcrumb: jest.fn(),
}));

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    refresh: jest.fn(),
  }),
  usePathname: () => '/test-path',
}));

// Mock sentry utilities
jest.mock('../../lib/sentry', () => ({
  addBreadcrumb: jest.fn(),
  reportError: jest.fn(),
}));

// Mock UniversalErrorWrapper
jest.mock('../../lib/UniversalErrorWrapper', () => ({
  UniversalErrorWrapper: {
    wrapSync: jest.fn().mockImplementation((operation, fn) => {
      try {
        return fn();
      } catch (error) {
        return Promise.reject(error);
      }
    }),
  },
}));

const mockScope = {
  setTag: jest.fn(),
  setContext: jest.fn(),
  setUser: jest.fn(),
};

// Test components
const ThrowError: React.FC<{ errorType?: string; message?: string }> = ({ 
  errorType = 'generic', 
  message = 'Test error' 
}) => {
  React.useEffect(() => {
    let error: Error;
    
    switch (errorType) {
      case 'network':
        error = new NetworkError(message, 500, '/api/test');
        break;
      case 'validation':
        error = new ValidationError(message, 'testField', 'invalidValue');
        break;
      case 'database':
        error = new DatabaseError(message, 'SELECT', 'users');
        break;
      case 'business':
        error = new BusinessLogicError(message, 'test_operation', 'user', '123');
        break;
      default:
        error = new Error(message);
    }
    
    throw error;
  }, [errorType, message]);

  return <div>Component that throws error</div>;
};

const WorkingComponent: React.FC = () => <div>Working component</div>;

describe('Enhanced ErrorBoundary', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Suppress console.error for cleaner test output
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Error Classification and Fallbacks', () => {
    it('should display appropriate fallback for network errors', () => {
      render(
        <ErrorBoundary componentName="TestComponent">
          <ThrowError errorType="network" message="Network connection failed" />
        </ErrorBoundary>
      );

      expect(screen.getByText('Connection Problem')).toBeInTheDocument();
      expect(screen.getByText(/having trouble connecting to our servers/)).toBeInTheDocument();
    });

    it('should display appropriate fallback for validation errors', () => {
      render(
        <ErrorBoundary componentName="TestComponent">
          <ThrowError errorType="validation" message="Invalid input provided" />
        </ErrorBoundary>
      );

      expect(screen.getByText('Invalid Input')).toBeInTheDocument();
      expect(screen.getByText(/problem with the information provided/)).toBeInTheDocument();
    });

    it('should display appropriate fallback for database errors', () => {
      render(
        <ErrorBoundary componentName="TestComponent">
          <ThrowError errorType="database" message="Database connection failed" />
        </ErrorBoundary>
      );

      expect(screen.getByText('Service Temporarily Unavailable')).toBeInTheDocument();
      expect(screen.getByText(/services are temporarily unavailable/)).toBeInTheDocument();
    });

    it('should display appropriate fallback for business logic errors', () => {
      render(
        <ErrorBoundary componentName="TestComponent">
          <ThrowError errorType="business" message="Business rule violation" />
        </ErrorBoundary>
      );

      expect(screen.getByText('Operation Failed')).toBeInTheDocument();
      expect(screen.getByText(/requested operation could not be completed/)).toBeInTheDocument();
    });
  });

  describe('Enhanced Error Context', () => {
    it('should capture enhanced error context with performance metrics', async () => {
      render(
        <ErrorBoundary 
          componentName="TestComponent" 
          enablePerformanceMonitoring={true}
          captureUserInteractions={true}
        >
          <ThrowError message="Test error with context" />
        </ErrorBoundary>
      );

      await waitFor(() => {
        expect(Sentry.withScope).toHaveBeenCalled();
        expect(mockScope.setTag).toHaveBeenCalledWith('componentName', 'TestComponent');
        expect(mockScope.setTag).toHaveBeenCalledWith('errorBoundary', true);
        expect(mockScope.setContext).toHaveBeenCalledWith('performanceMetrics', expect.objectContaining({
          componentLifetime: expect.any(Number),
          errorTime: expect.any(Number),
        }));
      });
    });

    it('should track retry attempts', async () => {
      render(
        <ErrorBoundary componentName="TestComponent" maxRetries={2}>
          <ThrowError message="Retryable error" />
        </ErrorBoundary>
      );

      const retryButton = screen.getByText(/Try Again/);
      
      // First retry
      fireEvent.click(retryButton);
      
      await waitFor(() => {
        expect(screen.getByText(/Try Again \(Attempt 2\)/)).toBeInTheDocument();
      });

      // Second retry
      fireEvent.click(screen.getByText(/Try Again \(Attempt 2\)/));
      
      await waitFor(() => {
        expect(screen.getByText(/Maximum retry attempts reached/)).toBeInTheDocument();
      });
    });
  });

  describe('Retry Mechanism', () => {
    it('should allow retries up to maxRetries limit', async () => {
      const { rerender } = render(
        <ErrorBoundary componentName="TestComponent" maxRetries={2}>
          <ThrowError message="Retryable error" />
        </ErrorBoundary>
      );

      // Should show retry button initially
      expect(screen.getByText(/Try Again/)).toBeInTheDocument();

      // First retry
      fireEvent.click(screen.getByText(/Try Again/));
      
      // Component should re-render and throw error again
      rerender(
        <ErrorBoundary componentName="TestComponent" maxRetries={2}>
          <ThrowError message="Retryable error" />
        </ErrorBoundary>
      );

      await waitFor(() => {
        expect(screen.getByText(/Try Again \(Attempt 2\)/)).toBeInTheDocument();
      });

      // Second retry
      fireEvent.click(screen.getByText(/Try Again \(Attempt 2\)/));
      
      rerender(
        <ErrorBoundary componentName="TestComponent" maxRetries={2}>
          <ThrowError message="Retryable error" />
        </ErrorBoundary>
      );

      // Should show max retries reached message
      await waitFor(() => {
        expect(screen.getByText(/Maximum retry attempts reached/)).toBeInTheDocument();
      });
    });

    it('should reset error state on successful retry', async () => {
      let shouldThrow = true;
      
      const ConditionalThrowComponent: React.FC = () => {
        if (shouldThrow) {
          throw new Error('Conditional error');
        }
        return <div>Success after retry</div>;
      };

      const { rerender } = render(
        <ErrorBoundary componentName="TestComponent">
          <ConditionalThrowComponent />
        </ErrorBoundary>
      );

      // Should show error fallback
      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      // Simulate fixing the error condition
      shouldThrow = false;

      // Click retry
      fireEvent.click(screen.getByText(/Try Again/));

      // Should show success message
      rerender(
        <ErrorBoundary componentName="TestComponent">
          <ConditionalThrowComponent />
        </ErrorBoundary>
      );

      await waitFor(() => {
        expect(screen.getByText('Success after retry')).toBeInTheDocument();
      });
    });
  });

  describe('Higher-Order Component', () => {
    it('should wrap component with error boundary using withErrorBoundary', () => {
      const EnhancedComponent = withErrorBoundary(
        ThrowError,
        undefined,
        true,
        {
          maxRetries: 3,
          enablePerformanceMonitoring: true
        }
      );

      render(<EnhancedComponent errorType="network" message="HOC test error" />);

      expect(screen.getByText('Connection Problem')).toBeInTheDocument();
    });

    it('should pass component name to error boundary', () => {
      const TestComponent: React.FC = () => {
        throw new Error('Test error');
      };
      TestComponent.displayName = 'TestComponent';

      const EnhancedComponent = withErrorBoundary(TestComponent);

      render(<EnhancedComponent />);

      expect(Sentry.withScope).toHaveBeenCalled();
      expect(mockScope.setTag).toHaveBeenCalledWith('componentName', 'TestComponent');
    });
  });

  describe('Performance Monitoring', () => {
    it('should initialize performance monitoring when enabled', () => {
      // Mock PerformanceObserver
      const mockObserver = {
        observe: jest.fn(),
        disconnect: jest.fn(),
      };
      
      global.PerformanceObserver = jest.fn().mockImplementation(() => mockObserver);

      render(
        <ErrorBoundary 
          componentName="TestComponent" 
          enablePerformanceMonitoring={true}
        >
          <WorkingComponent />
        </ErrorBoundary>
      );

      expect(global.PerformanceObserver).toHaveBeenCalled();
      expect(mockObserver.observe).toHaveBeenCalledWith({ entryTypes: ['measure', 'navigation'] });
    });

    it('should handle performance monitoring initialization failure gracefully', () => {
      // Mock PerformanceObserver to throw error
      global.PerformanceObserver = jest.fn().mockImplementation(() => {
        throw new Error('PerformanceObserver not supported');
      });

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});

      render(
        <ErrorBoundary 
          componentName="TestComponent" 
          enablePerformanceMonitoring={true}
        >
          <WorkingComponent />
        </ErrorBoundary>
      );

      expect(consoleSpy).toHaveBeenCalledWith(
        'Performance monitoring initialization failed:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Custom Error Handler', () => {
    it('should call custom error handler when provided', async () => {
      const mockErrorHandler = jest.fn();

      render(
        <ErrorBoundary 
          componentName="TestComponent"
          onError={mockErrorHandler}
        >
          <ThrowError message="Custom handler test" />
        </ErrorBoundary>
      );

      await waitFor(() => {
        expect(mockErrorHandler).toHaveBeenCalledWith(
          expect.any(Error),
          expect.objectContaining({
            componentStack: expect.any(String)
          }),
          expect.objectContaining({
            page: expect.objectContaining({
              component: 'ErrorBoundary'
            })
          })
        );
      });
    });
  });
});