/**
 * Tests for ErrorBoundary components
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useRouter, usePathname } from 'next/navigation'
import ErrorBoundary, { 
  NextErrorBoundary, 
  withErrorBoundary,
  withNetworkErrorBoundary,
  withValidationErrorBoundary,
  useErrorBoundaryReset 
} from '../ErrorBoundary'
import { 
  NetworkError, 
  ValidationError, 
  DatabaseError, 
  BusinessLogicError,
  BusinessOperation 
} from '../../lib/errorHandling'

// Mock Next.js navigation hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(),
}))

// Mock Sentry
jest.mock('@sentry/nextjs', () => ({
  withScope: jest.fn((callback) => callback({
    setTag: jest.fn(),
    setContext: jest.fn(),
    captureException: jest.fn(),
  })),
  captureException: jest.fn(),
}))

// Mock Sentry utilities
jest.mock('../../lib/sentry', () => ({
  addBreadcrumb: jest.fn(),
}))

// Test component that throws errors
const ThrowError: React.FC<{ error?: Error; shouldThrow?: boolean }> = ({ 
  error = new Error('Test error'), 
  shouldThrow = true 
}) => {
  if (shouldThrow) {
    throw error
  }
  return <div>No error</div>
}

describe('ErrorBoundary', () => {
  const mockRouter = {
    refresh: jest.fn(),
    push: jest.fn(),
    back: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
    ;(useRouter as jest.Mock).mockReturnValue(mockRouter)
    ;(usePathname as jest.Mock).mockReturnValue('/test-path')
  })

  it('should render children when no error occurs', () => {
    render(
      <ErrorBoundary>
        <div>Test content</div>
      </ErrorBoundary>
    )

    expect(screen.getByText('Test content')).toBeInTheDocument()
  })

  it('should render error fallback when error occurs', () => {
    // Suppress console.error for this test
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    render(
      <ErrorBoundary>
        <ThrowError />
      </ErrorBoundary>
    )

    expect(screen.getByText('Something went wrong')).toBeInTheDocument()
    expect(screen.getByText('Try Again')).toBeInTheDocument()

    consoleSpy.mockRestore()
  })

  it('should render network error fallback for NetworkError', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    render(
      <ErrorBoundary>
        <ThrowError error={new NetworkError('Network failed', 500, '/api/test')} />
      </ErrorBoundary>
    )

    expect(screen.getByText('Connection Problem')).toBeInTheDocument()
    expect(screen.getByText(/We're having trouble connecting to our servers/)).toBeInTheDocument()

    consoleSpy.mockRestore()
  })

  it('should render validation error fallback for ValidationError', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    render(
      <ErrorBoundary>
        <ThrowError error={new ValidationError('Invalid input', 'email')} />
      </ErrorBoundary>
    )

    expect(screen.getByText('Invalid Input')).toBeInTheDocument()
    expect(screen.getByText(/There was a problem with the information provided/)).toBeInTheDocument()

    consoleSpy.mockRestore()
  })

  it('should render database error fallback for DatabaseError', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    render(
      <ErrorBoundary>
        <ThrowError error={new DatabaseError('Connection failed', 'SELECT', 'users')} />
      </ErrorBoundary>
    )

    expect(screen.getByText('Service Temporarily Unavailable')).toBeInTheDocument()

    consoleSpy.mockRestore()
  })

  it('should render business error fallback for BusinessLogicError', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    render(
      <ErrorBoundary>
        <ThrowError error={new BusinessLogicError(
          'Operation failed', 
          'BOOKING_CONFLICT', 
          BusinessOperation.BOOKING_CREATE,
          'booking_123'
        )} />
      </ErrorBoundary>
    )

    expect(screen.getByText('Operation Failed')).toBeInTheDocument()

    consoleSpy.mockRestore()
  })

  it('should reset error boundary when retry is clicked', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    const { rerender } = render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )

    expect(screen.getByText('Something went wrong')).toBeInTheDocument()

    // Click retry button
    fireEvent.click(screen.getByText('Try Again'))

    // Re-render with no error
    rerender(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    )

    await waitFor(() => {
      expect(screen.getByText('No error')).toBeInTheDocument()
    })

    consoleSpy.mockRestore()
  })

  it('should show error details in development mode', () => {
    const originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'development'

    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    render(
      <ErrorBoundary>
        <ThrowError error={new Error('Detailed test error')} />
      </ErrorBoundary>
    )

    // Click to expand error details
    fireEvent.click(screen.getByText('Error Details'))

    expect(screen.getByText('Detailed test error')).toBeInTheDocument()

    process.env.NODE_ENV = originalEnv
    consoleSpy.mockRestore()
  })

  it('should use custom fallback component when provided', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    const CustomFallback: React.FC<{ error: Error; retry: () => void }> = ({ error }) => (
      <div>Custom error: {error.message}</div>
    )

    render(
      <ErrorBoundary fallback={CustomFallback}>
        <ThrowError error={new Error('Custom test error')} />
      </ErrorBoundary>
    )

    expect(screen.getByText('Custom error: Custom test error')).toBeInTheDocument()

    consoleSpy.mockRestore()
  })

  it('should call onError callback when provided', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
    const onError = jest.fn()

    render(
      <ErrorBoundary onError={onError}>
        <ThrowError error={new Error('Callback test error')} />
      </ErrorBoundary>
    )

    expect(onError).toHaveBeenCalledWith(
      expect.objectContaining({ message: 'Callback test error' }),
      expect.any(Object)
    )

    consoleSpy.mockRestore()
  })
})

describe('NextErrorBoundary', () => {
  const mockRouter = {
    refresh: jest.fn(),
    push: jest.fn(),
    back: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
    ;(useRouter as jest.Mock).mockReturnValue(mockRouter)
    ;(usePathname as jest.Mock).mockReturnValue('/test-path')
  })

  it('should reset on route change when resetOnRouteChange is true', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    const { rerender } = render(
      <NextErrorBoundary resetOnRouteChange={true}>
        <ThrowError shouldThrow={true} />
      </NextErrorBoundary>
    )

    expect(screen.getByText('Something went wrong')).toBeInTheDocument()

    // Simulate route change
    ;(usePathname as jest.Mock).mockReturnValue('/new-path')

    rerender(
      <NextErrorBoundary resetOnRouteChange={true}>
        <ThrowError shouldThrow={false} />
      </NextErrorBoundary>
    )

    await waitFor(() => {
      expect(screen.getByText('No error')).toBeInTheDocument()
    })

    consoleSpy.mockRestore()
  })

  it('should not reset on route change when resetOnRouteChange is false', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    const { rerender } = render(
      <NextErrorBoundary resetOnRouteChange={false}>
        <ThrowError shouldThrow={true} />
      </NextErrorBoundary>
    )

    expect(screen.getByText('Something went wrong')).toBeInTheDocument()

    // Simulate route change
    ;(usePathname as jest.Mock).mockReturnValue('/new-path')

    rerender(
      <NextErrorBoundary resetOnRouteChange={false}>
        <ThrowError shouldThrow={false} />
      </NextErrorBoundary>
    )

    // Should still show error
    expect(screen.getByText('Something went wrong')).toBeInTheDocument()

    consoleSpy.mockRestore()
  })
})

describe('withErrorBoundary HOC', () => {
  it('should wrap component with error boundary', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    const TestComponent: React.FC = () => {
      throw new Error('HOC test error')
    }

    const WrappedComponent = withErrorBoundary(TestComponent)

    render(<WrappedComponent />)

    expect(screen.getByText('Something went wrong')).toBeInTheDocument()

    consoleSpy.mockRestore()
  })

  it('should use specific error boundary for network errors', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    const TestComponent: React.FC = () => {
      throw new NetworkError('Network test error', 500)
    }

    const WrappedComponent = withNetworkErrorBoundary(TestComponent)

    render(<WrappedComponent />)

    expect(screen.getByText('Connection Problem')).toBeInTheDocument()

    consoleSpy.mockRestore()
  })

  it('should use specific error boundary for validation errors', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    const TestComponent: React.FC = () => {
      throw new ValidationError('Validation test error')
    }

    const WrappedComponent = withValidationErrorBoundary(TestComponent)

    render(<WrappedComponent />)

    expect(screen.getByText('Invalid Input')).toBeInTheDocument()

    consoleSpy.mockRestore()
  })
})

describe('useErrorBoundaryReset', () => {
  it('should call router.refresh when reset function is called', () => {
    const mockRouter = {
      refresh: jest.fn(),
      push: jest.fn(),
      back: jest.fn(),
    }

    ;(useRouter as jest.Mock).mockReturnValue(mockRouter)

    const TestComponent: React.FC = () => {
      const reset = useErrorBoundaryReset()
      
      return (
        <button onClick={reset}>
          Reset
        </button>
      )
    }

    render(<TestComponent />)

    fireEvent.click(screen.getByText('Reset'))

    expect(mockRouter.refresh).toHaveBeenCalled()
  })
})