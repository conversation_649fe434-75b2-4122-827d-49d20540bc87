'use client'

import { useState } from 'react'
import { isPostHogReady, getPostHogInstance } from '../lib/posthog'
import { forceOptInPostHog } from '../lib/analytics'
import { immediatePostHogTest, testPostHogNow } from '../lib/immediate-posthog-test'

export function PostHogTest() {
  const [testResult, setTestResult] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)

  const testPostHog = async () => {
    setIsLoading(true)
    setTestResult('')

    try {
      // Force opt-in first to ensure events are tracked
      forceOptInPostHog()
      
      // Check if PostHog is ready
      const isReady = isPostHogReady()
      console.log('PostHog ready:', isReady)

      if (!isReady) {
        setTestResult('❌ PostHog is not ready. Check console for initialization errors.')
        return
      }

      // Get PostHog instance
      const posthog = getPostHogInstance()
      if (!posthog) {
        setTestResult('❌ PostHog instance not available.')
        return
      }

      // Check opt-out status
      const hasOptedOut = posthog.has_opted_out_capturing?.()
      console.log('PostHog opted out status:', hasOptedOut)
      
      if (hasOptedOut) {
        console.log('PostHog is opted out, attempting to opt in...')
        posthog.opt_in_capturing()
        
        // Verify opt-in was successful
        const stillOptedOut = posthog.has_opted_out_capturing?.()
        if (stillOptedOut) {
          setTestResult('⚠️ PostHog is still opted out after opt-in attempt. Events may not be tracked.')
        } else {
          console.log('✅ PostHog successfully opted in')
        }
      }

      // Send a test event
      const testEventName = 'posthog_test_event'
      const testProperties = {
        test_timestamp: new Date().toISOString(),
        test_source: 'manual_test',
        page_url: window.location.href,
        user_agent: navigator.userAgent,
        opted_out_status: hasOptedOut,
        distinct_id: posthog.get_distinct_id?.() || 'unknown'
      }

      console.log('Sending test event:', testEventName, testProperties)
      
      posthog.capture(testEventName, testProperties)

      // Also test the API endpoint
      const response = await fetch('/api/test-posthog', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event: testEventName,
          properties: testProperties
        })
      })

      const apiResult = await response.json()
      console.log('API test result:', apiResult)

      setTestResult(`✅ PostHog test successful! Event "${testEventName}" sent. Check your PostHog dashboard for the event. Opted out: ${hasOptedOut}`)
      
    } catch (error) {
      console.error('PostHog test failed:', error)
      setTestResult(`❌ PostHog test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsLoading(false)
    }
  }

  const checkStatus = async () => {
    setIsLoading(true)
    try {
      // Check PostHog status
      const isReady = isPostHogReady()
      const posthog = getPostHogInstance()
      
      // Check API configuration
      const response = await fetch('/api/test-posthog')
      const config = await response.json()
      
      const status = {
        ready: isReady,
        instance: !!posthog,
        distinctId: posthog?.get_distinct_id?.() || 'unknown',
        config
      }
      
      console.log('PostHog status:', status)
      setTestResult(`📊 PostHog Status:\n${JSON.stringify(status, null, 2)}`)
      
    } catch (error) {
      console.error('Status check failed:', error)
      setTestResult(`❌ Status check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsLoading(false)
    }
  }

  // Only show in development or with debug flag (client-side only)
  const isDev = process.env.NODE_ENV === 'development'
  const showDebug = isDev || (typeof window !== 'undefined' && window.location.search.includes('debug=true'))

  // Don't render on server-side
  if (typeof window === 'undefined' || !showDebug) {
    return null
  }

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      background: 'white',
      border: '2px solid #ccc',
      borderRadius: '8px',
      padding: '16px',
      boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
      zIndex: 9999,
      maxWidth: '400px',
      fontSize: '14px'
    }}>
      <h4 style={{ margin: '0 0 12px 0', color: '#333' }}>PostHog Debug</h4>
      
      <div style={{ marginBottom: '12px' }}>
        <button 
          onClick={checkStatus}
          disabled={isLoading}
          style={{
            marginRight: '8px',
            padding: '6px 12px',
            background: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          {isLoading ? 'Checking...' : 'Check Status'}
        </button>
        
        <button 
          onClick={() => {
            forceOptInPostHog()
            setTestResult('✅ Forced PostHog opt-in. Check console for details.')
          }}
          disabled={isLoading}
          style={{
            marginRight: '8px',
            padding: '6px 12px',
            background: '#ffc107',
            color: 'black',
            border: 'none',
            borderRadius: '4px',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          Force Opt-In
        </button>
        
        <button 
          onClick={testPostHog}
          disabled={isLoading}
          style={{
            marginRight: '8px',
            padding: '6px 12px',
            background: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          {isLoading ? 'Testing...' : 'Send Test Event'}
        </button>
        
        <button 
          onClick={() => {
            immediatePostHogTest();
            setTestResult('🧪 Immediate PostHog test started - check console');
          }}
          disabled={isLoading}
          style={{
            marginRight: '8px',
            padding: '6px 12px',
            background: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          Immediate Test
        </button>
        
        <button 
          onClick={() => {
            testPostHogNow();
            setTestResult('🧪 Direct PostHog test - check console');
          }}
          disabled={isLoading}
          style={{
            padding: '6px 12px',
            background: '#6f42c1',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          Test Now
        </button>
      </div>
      
      {testResult && (
        <div style={{
          background: '#f8f9fa',
          border: '1px solid #dee2e6',
          borderRadius: '4px',
          padding: '8px',
          whiteSpace: 'pre-wrap',
          fontSize: '12px',
          maxHeight: '200px',
          overflow: 'auto'
        }}>
          {testResult}
        </div>
      )}
    </div>
  )
}
