'use client'

import { useEffect, useState, useCallback } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'
import {
  initPostHog,
  trackPageView,
  postHogInitManager,
  withdrawConsent,
  grantConsent
} from '../lib/posthog'
import { PrivacyConsent, usePrivacyConsent } from './PrivacyConsent'

export function PostHogProvider({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const { isLoading, hasConsent, updateConsent } = usePrivacyConsent()
  const [isPostHogInitialized, setIsPostHogInitialized] = useState(false)

  // Initialize PostHog only when we have consent
  useEffect(() => {
    if (isLoading) return

    const analyticsConsent = hasConsent('analytics')
    const currentlyInitialized = postHogInitManager.isReady()

    // In development, bypass consent for testing
    const shouldInitialize = process.env.NODE_ENV === 'development' ? true : analyticsConsent

    console.log('PostHog Provider Effect:', {
      analyticsConsent,
      currentlyInitialized,
      isLoading,
      shouldInitialize,
      isDevelopment: process.env.NODE_ENV === 'development'
    })

    if (shouldInitialize && !currentlyInitialized) {
      // Grant consent and initialize
      console.log('🚀 Initializing PostHog', process.env.NODE_ENV === 'development' ? '(development mode - bypassing consent)' : 'with consent')
      if (process.env.NODE_ENV !== 'development') {
        grantConsent() // Grant consent BEFORE initialization (only in production)
      }
      initPostHog()
        .then(() => {
          setIsPostHogInitialized(postHogInitManager.isReady())
          console.log('✅ PostHog initialized successfully')
        })
        .catch((error) => {
          console.error('❌ Failed to initialize PostHog:', error)
          setIsPostHogInitialized(false)
        })
    } else if (!shouldInitialize && currentlyInitialized) {
      // Withdraw consent
      console.log('🛑 Withdrawing PostHog consent')
      withdrawConsent()
      setIsPostHogInitialized(false)
    } else if (!shouldInitialize && !currentlyInitialized) {
      // No consent and not initialized - this is correct
      console.log('⏸️ No analytics consent - PostHog not initialized')
      setIsPostHogInitialized(false)
    } else {
      // Sync state with manager
      setIsPostHogInitialized(currentlyInitialized)
    }
  }, [hasConsent, isLoading])

  // Track page views only if analytics consent is given
  useEffect(() => {
    if (pathname && hasConsent('analytics') && isPostHogInitialized) {
      const url = pathname + (searchParams?.toString() ? `?${searchParams.toString()}` : '')
      const section = pathname === '/' ? 'home' : pathname.replace('/', '')
      
      trackPageView(url, section, document.referrer)
    }
  }, [pathname, searchParams, hasConsent, isPostHogInitialized])

  // Handle consent changes
  const handleConsentChange = useCallback((preferences: any) => {
    updateConsent(preferences)
    
    if (preferences.analytics && !postHogInitManager.isReady()) {
      // Grant consent
      grantConsent()
      setIsPostHogInitialized(true)
    } else if (!preferences.analytics && postHogInitManager.isReady()) {
      // Withdraw consent
      withdrawConsent()
      setIsPostHogInitialized(false)
    }
  }, [updateConsent])

  return (
    <>
      {children}
      <PrivacyConsent onConsentChange={handleConsentChange} />
    </>
  )
}