'use client'

import posthog from 'posthog-js'
import { PostHogProvider } from 'posthog-js/react'
import { useEffect } from 'react'

export function SimplePostHogProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Only initialize on client side
    if (typeof window !== 'undefined') {
      posthog.init(process.env.NEXT_PUBLIC_POSTHOG_API_KEY!, {
        api_host: process.env.NEXT_PUBLIC_POSTHOG_API_HOST || 'https://us.i.posthog.com',
        // This is CRITICAL for Next.js pageview tracking
        // defaults: process.env.NEXT_PUBLIC_POSTHOG_PROJECT || 'default',
        
        // Enable debug in development
        debug: process.env.NODE_ENV === 'development',
        
        // Enable automatic pageview capture
        capture_pageview: true,
        
        // Disable opt-out by default (for testing)
        opt_out_capturing_by_default: false,
        
        // Add before_send to log all events being sent
        before_send: (event) => {
          console.log('📤 PostHog sending event:', event);
          return event;
        },

        // Loaded callback for debugging
        loaded: (posthog) => {
          if (process.env.NODE_ENV === 'development') {
            console.log('🚀 PostHog loaded successfully!');
            console.log('- Distinct ID:', posthog.get_distinct_id());
            console.log('- Has opted out:', posthog.has_opted_out_capturing());

            // Send a test event immediately
            posthog.capture('posthog_loaded', {
              environment: process.env.NODE_ENV,
              timestamp: new Date().toISOString()
            });

            // Send another event after a delay
            setTimeout(() => {
              posthog.capture('delayed_test_event', {
                message: 'This event was sent 2 seconds after load'
              });
            }, 2000);
          }
        }
      });
    }
  }, []);

  return <PostHogProvider client={posthog}>{children}</PostHogProvider>;
}
