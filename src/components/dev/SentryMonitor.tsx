"use client";

import React, { useState, useEffect } from 'react';
import { sentryRateLimiter } from '@/lib/sentry-rate-limiter';

interface SentryStats {
  errors: number;
  transactions: number;
  other: number;
}

export function SentryMonitor() {
  const [stats, setStats] = useState<SentryStats>({ errors: 0, transactions: 0, other: 0 });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV !== 'development') return;

    const updateStats = () => {
      const stats = sentryRateLimiter.getStats();
      setStats({
        errors: stats.errors || 0,
        transactions: stats.transactions || 0,
        other: stats.other || 0,
      });
    };

    // Update stats every 5 seconds
    const interval = setInterval(updateStats, 5000);
    updateStats(); // Initial update

    return () => clearInterval(interval);
  }, []);

  // Don't render in production
  if (process.env.NODE_ENV !== 'development') return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-blue-600 text-white px-3 py-2 rounded-lg shadow-lg text-sm font-medium hover:bg-blue-700 transition-colors"
      >
        Sentry Monitor
      </button>
      
      {isVisible && (
        <div className="absolute bottom-12 right-0 bg-white border border-gray-200 rounded-lg shadow-xl p-4 min-w-[250px]">
          <h3 className="font-semibold text-gray-900 mb-3">Sentry Usage (Last Minute)</h3>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Errors:</span>
              <span className={`font-medium ${stats.errors > 8 ? 'text-red-600' : 'text-green-600'}`}>
                {stats.errors}/10
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Transactions:</span>
              <span className={`font-medium ${stats.transactions > 20 ? 'text-red-600' : 'text-green-600'}`}>
                {stats.transactions}/25
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Other Events:</span>
              <span className={`font-medium ${stats.other > 40 ? 'text-red-600' : 'text-green-600'}`}>
                {stats.other}/50
              </span>
            </div>
          </div>
          
          <div className="mt-4 pt-3 border-t border-gray-200">
            <button
              onClick={() => {
                sentryRateLimiter.reset();
                setStats({ errors: 0, transactions: 0, other: 0 });
              }}
              className="w-full bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200 transition-colors"
            >
              Reset Counters
            </button>
          </div>
          
          <div className="mt-2">
            <button
              onClick={() => {
                if (typeof window !== 'undefined' && (window as any).testSentry) {
                  (window as any).testSentry.runAll();
                }
              }}
              className="w-full bg-blue-100 text-blue-700 px-3 py-1 rounded text-sm hover:bg-blue-200 transition-colors"
            >
              Run Test Suite
            </button>
          </div>
          
          <div className="mt-3 pt-2 border-t border-gray-200 text-xs text-gray-500">
            <p>Rate limits help prevent 429 errors</p>
            <a 
              href="https://sentry.io/organizations/atemkeng/projects/proptech/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              View Sentry Dashboard →
            </a>
          </div>
        </div>
      )}
    </div>
  );
}