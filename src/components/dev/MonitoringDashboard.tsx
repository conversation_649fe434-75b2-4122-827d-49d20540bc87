/**
 * Development monitoring dashboard for debugging monitoring services
 */

'use client';

import React from 'react';
import { useMonitoringDashboard } from '../../lib/hooks/useMonitoring';
import { validateMonitoringForProduction } from '../../lib/monitoring';
import { isDevelopment } from '../../lib/env';

interface MonitoringDashboardProps {
  className?: string;
}

const StatusBadge: React.FC<{ 
  status: 'up' | 'down' | 'degraded' | 'healthy' | 'unhealthy';
  children: React.ReactNode;
}> = ({ status, children }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'up':
      case 'healthy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'degraded':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'down':
      case 'unhealthy':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor()}`}>
      {children}
    </span>
  );
};

const MonitoringDashboard: React.FC<MonitoringDashboardProps> = ({ className = '' }) => {
  const {
    isInitialized,
    isInitializing,
    config,
    healthStatus,
    error,
    initialize,
    checkHealth,
    autoRefresh,
    toggleAutoRefresh,
  } = useMonitoringDashboard();

  const [validationResult, setValidationResult] = React.useState<{
    valid: boolean;
    issues: string[];
    warnings: string[];
  } | null>(null);

  // Validate monitoring configuration
  React.useEffect(() => {
    const result = validateMonitoringForProduction();
    setValidationResult(result);
  }, [config]);

  // Don't render in production
  if (!isDevelopment()) {
    return null;
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Monitoring Dashboard</h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleAutoRefresh}
            className={`px-3 py-1 text-xs rounded-md ${
              autoRefresh 
                ? 'bg-blue-100 text-blue-800 border border-blue-200' 
                : 'bg-gray-100 text-gray-800 border border-gray-200'
            }`}
          >
            Auto-refresh: {autoRefresh ? 'ON' : 'OFF'}
          </button>
          <button
            onClick={checkHealth}
            disabled={isInitializing}
            className="px-3 py-1 text-xs bg-gray-100 text-gray-800 border border-gray-200 rounded-md hover:bg-gray-200 disabled:opacity-50"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Initialization Status */}
      <div className="mb-4">
        <div className="flex items-center space-x-2 mb-2">
          <span className="text-sm font-medium text-gray-700">Initialization:</span>
          {isInitializing ? (
            <StatusBadge status="degraded">Initializing...</StatusBadge>
          ) : isInitialized ? (
            <StatusBadge status="up">Initialized</StatusBadge>
          ) : (
            <StatusBadge status="down">Not Initialized</StatusBadge>
          )}
        </div>
        
        {error && (
          <div className="text-sm text-red-600 bg-red-50 p-2 rounded-md">
            Error: {error}
          </div>
        )}

        {!isInitialized && !isInitializing && (
          <button
            onClick={initialize}
            className="text-sm bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600"
          >
            Initialize Monitoring
          </button>
        )}
      </div>

      {/* Overall Health Status */}
      {healthStatus && (
        <div className="mb-4">
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-sm font-medium text-gray-700">Overall Health:</span>
            <StatusBadge status={healthStatus.overall}>
              {healthStatus.overall.toUpperCase()}
            </StatusBadge>
          </div>
          <div className="text-xs text-gray-500">
            Last checked: {new Date(healthStatus.lastChecked).toLocaleTimeString()}
          </div>
        </div>
      )}

      {/* Service Status */}
      {healthStatus && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Services</h4>
          <div className="space-y-2">
            {/* Sentry */}
            <div className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-700">Sentry</span>
                <StatusBadge status={healthStatus.services.sentry.status}>
                  {healthStatus.services.sentry.status.toUpperCase()}
                </StatusBadge>
              </div>
              <div className="text-xs text-gray-500">
                Configured: {healthStatus.services.sentry.configured ? 'Yes' : 'No'}
                {healthStatus.services.sentry.error && (
                  <div className="text-red-600">Error: {healthStatus.services.sentry.error}</div>
                )}
              </div>
            </div>

            {/* PostHog */}
            <div className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-700">PostHog</span>
                <StatusBadge status={healthStatus.services.posthog.status}>
                  {healthStatus.services.posthog.status.toUpperCase()}
                </StatusBadge>
              </div>
              <div className="text-xs text-gray-500">
                Init: {healthStatus.services.posthog.initialized ? 'Yes' : 'No'} | 
                Healthy: {healthStatus.services.posthog.healthy ? 'Yes' : 'No'}
                {healthStatus.services.posthog.error && (
                  <div className="text-red-600">Error: {healthStatus.services.posthog.error}</div>
                )}
              </div>
            </div>

            {/* Performance */}
            <div className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-700">Performance</span>
                <StatusBadge status={healthStatus.services.performance.status}>
                  {healthStatus.services.performance.status.toUpperCase()}
                </StatusBadge>
              </div>
              <div className="text-xs text-gray-500">
                Web Vitals: {healthStatus.services.performance.webVitals ? 'Yes' : 'No'} | 
                Observer: {healthStatus.services.performance.observer ? 'Yes' : 'No'}
                {healthStatus.services.performance.error && (
                  <div className="text-red-600">Error: {healthStatus.services.performance.error}</div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Configuration */}
      {config && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Configuration</h4>
          <div className="bg-gray-50 p-3 rounded-md">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
              <div>
                <strong>Sentry:</strong>
                <div>Enabled: {config.sentry.enabled ? 'Yes' : 'No'}</div>
                <div>Environment: {config.sentry.environment}</div>
                <div>Traces Sample Rate: {config.sentry.tracesSampleRate}</div>
              </div>
              <div>
                <strong>PostHog:</strong>
                <div>Enabled: {config.posthog.enabled ? 'Yes' : 'No'}</div>
                <div>Host: {config.posthog.apiHost}</div>
                <div>Initialized: {config.posthog.initialized ? 'Yes' : 'No'}</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Validation Results */}
      {validationResult && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Production Validation</h4>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">Status:</span>
              <StatusBadge status={validationResult.valid ? 'up' : 'down'}>
                {validationResult.valid ? 'VALID' : 'INVALID'}
              </StatusBadge>
            </div>
            
            {validationResult.issues.length > 0 && (
              <div>
                <div className="text-sm font-medium text-red-700 mb-1">Issues:</div>
                <ul className="text-xs text-red-600 space-y-1">
                  {validationResult.issues.map((issue, index) => (
                    <li key={index} className="flex items-start space-x-1">
                      <span>•</span>
                      <span>{issue}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            {validationResult.warnings.length > 0 && (
              <div>
                <div className="text-sm font-medium text-yellow-700 mb-1">Warnings:</div>
                <ul className="text-xs text-yellow-600 space-y-1">
                  {validationResult.warnings.map((warning, index) => (
                    <li key={index} className="flex items-start space-x-1">
                      <span>•</span>
                      <span>{warning}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="border-t pt-3">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Quick Actions</h4>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => window.open('/api/health', '_blank')}
            className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600"
          >
            View Health API
          </button>
          <button
            onClick={() => console.log('Monitoring Config:', config)}
            className="text-xs bg-gray-500 text-white px-2 py-1 rounded hover:bg-gray-600"
          >
            Log Config
          </button>
          <button
            onClick={() => console.log('Health Status:', healthStatus)}
            className="text-xs bg-gray-500 text-white px-2 py-1 rounded hover:bg-gray-600"
          >
            Log Health
          </button>
        </div>
      </div>
    </div>
  );
};

export default MonitoringDashboard;