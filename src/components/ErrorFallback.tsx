'use client'

import React, { useState } from 'react'
import { AlertTriangle, RefreshCw, Home, ChevronDown, ChevronUp } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from './ui/alert'
import { Button } from './ui/button'
import { Card } from './ui/card'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from './ui/alert-dialog'

interface ErrorFallbackProps {
  error: Error
  retry: () => void
  showDetails?: boolean
  title?: string
  description?: string
  showHomeButton?: boolean
  showReloadButton?: boolean
  className?: string
}

export const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  retry,
  showDetails = true,
  title = "Something went wrong",
  description = "We're sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.",
  showHomeButton = true,
  showReloadButton = true,
  className = "",
}) => {
  const [showErrorDetails, setShowErrorDetails] = useState(false)
  const [isRetrying, setIsRetrying] = useState(false)

  const handleRetry = async () => {
    setIsRetrying(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 500)) // Brief delay for UX
      retry()
    } finally {
      setIsRetrying(false)
    }
  }

  const handleGoHome = () => {
    window.location.href = '/'
  }

  const handleReload = () => {
    window.location.reload()
  }

  return (
    <div className={`min-h-screen flex items-center justify-center bg-gray-50 p-4 ${className}`}>
      <Card className="w-full max-w-md p-6">
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>{title}</AlertTitle>
          <AlertDescription>{description}</AlertDescription>
        </Alert>

        {/* Action Buttons */}
        <div className="space-y-3 mb-4">
          <Button 
            onClick={handleRetry} 
            disabled={isRetrying}
            className="w-full"
          >
            {isRetrying ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Retrying...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </>
            )}
          </Button>

          <div className="flex gap-2">
            {showHomeButton && (
              <Button 
                variant="outline" 
                onClick={handleGoHome}
                className="flex-1"
              >
                <Home className="mr-2 h-4 w-4" />
                Go Home
              </Button>
            )}

            {showReloadButton && (
              <Button 
                variant="outline" 
                onClick={handleReload}
                className="flex-1"
              >
                Reload Page
              </Button>
            )}
          </div>
        </div>

        {/* Error Details (Development/Debug) */}
        {showDetails && (process.env.NODE_ENV === 'development' || showErrorDetails) && (
          <div className="border-t pt-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowErrorDetails(!showErrorDetails)}
              className="w-full justify-between text-sm text-muted-foreground"
            >
              Error Details
              {showErrorDetails ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>

            {showErrorDetails && (
              <div className="mt-3 p-3 bg-muted rounded-md">
                <div className="text-sm space-y-2">
                  <div>
                    <span className="font-medium">Error:</span> {error.name}
                  </div>
                  <div>
                    <span className="font-medium">Message:</span> {error.message}
                  </div>
                  {error.stack && (
                    <div>
                      <span className="font-medium">Stack Trace:</span>
                      <pre className="mt-1 text-xs bg-background p-2 rounded border overflow-auto max-h-32">
                        {error.stack}
                      </pre>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Report Error Dialog */}
        <div className="border-t pt-4 mt-4">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="ghost" size="sm" className="w-full text-sm text-muted-foreground">
                Report this issue
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Report Error</AlertDialogTitle>
                <AlertDialogDescription>
                  This error has been automatically reported to our team. If you'd like to provide 
                  additional context or contact us directly, you can reach us through our contact form.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Close</AlertDialogCancel>
                <AlertDialogAction onClick={() => window.location.href = '/#contact'}>
                  Contact Us
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </Card>
    </div>
  )
}

// Specialized error fallbacks for different scenarios
export const NetworkErrorFallback: React.FC<Omit<ErrorFallbackProps, 'title' | 'description'>> = (props) => (
  <ErrorFallback
    {...props}
    title="Connection Problem"
    description="We're having trouble connecting to our servers. Please check your internet connection and try again."
  />
)

export const ValidationErrorFallback: React.FC<Omit<ErrorFallbackProps, 'title' | 'description' | 'showHomeButton'>> = (props) => (
  <ErrorFallback
    {...props}
    title="Invalid Input"
    description="There was a problem with the information provided. Please check your input and try again."
    showHomeButton={false}
  />
)

export const DatabaseErrorFallback: React.FC<Omit<ErrorFallbackProps, 'title' | 'description'>> = (props) => (
  <ErrorFallback
    {...props}
    title="Service Temporarily Unavailable"
    description="Our services are temporarily unavailable. We're working to restore them as quickly as possible."
  />
)

export const BusinessLogicErrorFallback: React.FC<Omit<ErrorFallbackProps, 'title' | 'description'>> = (props) => (
  <ErrorFallback
    {...props}
    title="Operation Failed"
    description="The requested operation could not be completed. Please try again or contact support if the problem persists."
  />
)

export default ErrorFallback