/**
 * Analytics Test Button Component
 * 
 * Provides a simple button to test analytics functionality
 * Only visible in development mode
 */

'use client';

import React from 'react';
import { isDevelopment } from '../lib/env';

export function AnalyticsTestButton() {
  // Only show in development
  if (!isDevelopment()) {
    return null;
  }

  const handleTestClick = () => {
    // Test if global analytics functions are available
    if (typeof window !== 'undefined') {
      const analytics = (window as any).analytics;
      const trackEvent = (window as any).trackEvent;
      const sendTestEvent = (window as any).sendTestEvent;
      
      console.log('🧪 Testing analytics functions...');
      console.log('Analytics object available:', !!analytics);
      console.log('trackEvent function available:', typeof trackEvent === 'function');
      console.log('sendTestEvent function available:', typeof sendTestEvent === 'function');
      
      // Send test event using different methods
      if (sendTestEvent) {
        sendTestEvent();
      }
      
      if (trackEvent) {
        trackEvent('button_test_click', {
          button_type: 'analytics_test',
          location: 'test_component',
          timestamp: new Date().toISOString(),
          test_method: 'direct_function_call'
        });
      }
      
      if (analytics && analytics.trackEvent) {
        analytics.trackEvent('analytics_object_test', {
          test_type: 'object_method_call',
          timestamp: new Date().toISOString(),
          source: 'AnalyticsTestButton'
        });
      }
      
      // Get status
      const getStatus = (window as any).getAnalyticsStatus;
      if (getStatus) {
        const status = getStatus();
        console.log('📊 Analytics status:', status);
      }
    }
  };

  return (
    <div 
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        zIndex: 9999,
        backgroundColor: '#1e3a8a',
        color: 'white',
        padding: '10px 15px',
        borderRadius: '8px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: 'bold',
        boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
        border: '2px solid #fbbf24'
      }}
      onClick={handleTestClick}
    >
      🧪 Test Analytics
    </div>
  );
}