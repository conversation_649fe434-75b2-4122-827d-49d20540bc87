/**
 * Simple Analytics Provider
 * Handles initialization and provides debug component in development
 */

'use client';

import React, { useEffect } from 'react';
import { initializeAnalytics } from '../lib/analytics';
import { AnalyticsDebug } from './AnalyticsDebug';
import { isDevelopment } from '../lib/env';
import { initializeDebugUtils } from '../lib/debug';
import '../lib/immediate-posthog-test'; // Import to make functions available

// Import test utilities in development (conditional import)
if (typeof window !== 'undefined' && isDevelopment()) {
  import('../lib/test-analytics').catch(() => {
    // Ignore import errors in production builds
  });
}

interface AnalyticsProviderProps {
  children: React.ReactNode;
}

export function AnalyticsProvider({ children }: AnalyticsProviderProps) {
  useEffect(() => {
    // Initialize analytics on mount with enhanced error handling
    console.log('🚀 AnalyticsProvider: Starting initialization...');
    
    initializeAnalytics()
      .then(async (success) => {
        console.log(`📊 AnalyticsProvider: Initialization ${success ? 'succeeded' : 'failed'}`);
        
        if (success) {
          // Import and use forceOptInPostHog after successful initialization
          const { forceOptInPostHog } = await import('../lib/analytics');
          
          // Wait a bit for PostHog to fully initialize, then force opt-in
          setTimeout(() => {
            forceOptInPostHog();
          }, 1000);
          
          // Initialize debug utilities in development
          if (isDevelopment()) {
            initializeDebugUtils();
            
            // Also make simple PostHog available for testing
            setTimeout(() => {
              console.log('🛠️ Simple PostHog functions available via window object');
            }, 100);
          }
        }
      })
      .catch(error => {
        console.error('❌ AnalyticsProvider: Initialization failed:', error);
        
        // In production, still log the error for debugging
        if (!isDevelopment()) {
          console.error('Production analytics initialization error:', {
            message: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString()
          });
        }
      });
  }, []);

  return (
    <>
      {children}
      {isDevelopment() && <AnalyticsDebug />}
    </>
  );
}