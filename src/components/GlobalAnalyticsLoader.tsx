/**
 * Global Analytics Loader Component
 * 
 * This component handles the client-side loading of the global analytics system
 * to avoid server-side rendering issues.
 */

'use client';

import { useEffect } from 'react';

export function GlobalAnalyticsLoader() {
  useEffect(() => {
    // Dynamically import and initialize the global analytics system on the client side
    const loadGlobalAnalytics = async () => {
      try {
        const { initializeGlobalAnalytics } = await import('../lib/global-analytics');
        initializeGlobalAnalytics();
        console.log('🌐 Global analytics system loaded and initialized');
      } catch (error) {
        console.error('❌ Failed to load global analytics system:', error);
      }
    };

    loadGlobalAnalytics();
  }, []);

  // This component doesn't render anything
  return null;
}