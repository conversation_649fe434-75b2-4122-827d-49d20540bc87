'use client'

import React from "react";
import { useRouter, usePathname } from "next/navigation";
import * as Sentry from "@sentry/nextjs";
import { addBreadcrumb } from "../lib/sentry";
import { 
  ErrorFallback, 
  NetworkErrorFallback, 
  ValidationErrorFallback, 
  DatabaseErrorFallback, 
  BusinessLogicErrorFallback 
} from "./ErrorFallback";
import { 
  NetworkError, 
  ValidationError, 
  DatabaseError, 
  BusinessLogicError 
} from "../lib/errorHandling";

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
  errorId?: string;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  resetOnRouteChange?: boolean;
  resetKeys?: Array<string | number>;
}

// Enhanced error classification
function getErrorType(error: Error): 'network' | 'validation' | 'database' | 'business' | 'generic' {
  if (error instanceof NetworkError) return 'network';
  if (error instanceof ValidationError) return 'validation';
  if (error instanceof DatabaseError) return 'database';
  if (error instanceof BusinessLogicError) return 'business';
  return 'generic';
}

// Get appropriate fallback component based on error type
function getErrorFallback(error: Error): React.ComponentType<{ error: Error; retry: () => void }> {
  const errorType = getErrorType(error);
  
  switch (errorType) {
    case 'network':
      return NetworkErrorFallback;
    case 'validation':
      return ValidationErrorFallback;
    case 'database':
      return DatabaseErrorFallback;
    case 'business':
      return BusinessLogicErrorFallback;
    default:
      return ErrorFallback;
  }
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private resetTimeoutId: number | null = null;
  private previousResetKeys: Array<string | number> = [];

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
    this.previousResetKeys = props.resetKeys || [];
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Generate unique error ID for tracking
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void {
    const errorType = getErrorType(error);
    
    // Add breadcrumb for debugging with enhanced context
    addBreadcrumb(
      `Error boundary caught ${errorType} error: ${error.message}`,
      "error",
      "error"
    );

    // Report to Sentry with enhanced context
    Sentry.withScope((scope) => {
      scope.setTag("errorBoundary", true);
      scope.setTag("errorType", errorType);
      scope.setTag("errorId", this.state.errorId);
      
      scope.setContext("errorInfo", {
        componentStack: errorInfo.componentStack,
        errorBoundaryProps: {
          resetOnRouteChange: this.props.resetOnRouteChange,
          hasCustomFallback: !!this.props.fallback,
        },
      });

      // Add error-specific context
      if (error instanceof NetworkError) {
        scope.setContext("networkError", {
          statusCode: error.statusCode,
          endpoint: error.endpoint,
        });
      } else if (error instanceof ValidationError) {
        scope.setContext("validationError", {
          field: error.field,
          value: error.value,
        });
      } else if (error instanceof DatabaseError) {
        scope.setContext("databaseError", {
          operation: error.operation,
          table: error.table,
        });
      } else if (error instanceof BusinessLogicError) {
        scope.setContext("businessError", {
          operation: error.operation,
          entityId: error.entityId,
          entityType: error.entityType,
        });
      }

      Sentry.captureException(error);
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    this.setState({
      error,
      errorInfo,
    });
  }

  componentDidUpdate(prevProps: ErrorBoundaryProps): void {
    const { resetKeys } = this.props;
    const { hasError } = this.state;
    
    // Reset error boundary if resetKeys have changed
    if (hasError && resetKeys && prevProps.resetKeys !== resetKeys) {
      const hasResetKeyChanged = resetKeys.some(
        (key, index) => this.previousResetKeys[index] !== key
      );
      
      if (hasResetKeyChanged) {
        this.resetErrorBoundary();
      }
    }
    
    this.previousResetKeys = resetKeys || [];
  }

  componentWillUnmount(): void {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  resetErrorBoundary = (): void => {
    // Clear any pending reset timeout
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
      this.resetTimeoutId = null;
    }

    this.setState({ 
      hasError: false, 
      error: undefined, 
      errorInfo: undefined,
      errorId: undefined,
    });
  };

  handleRetry = (): void => {
    // Add breadcrumb for retry attempt
    addBreadcrumb(
      `User initiated retry for error: ${this.state.errorId}`,
      "user",
      "info"
    );

    this.resetErrorBoundary();
  };

  render(): React.ReactNode {
    if (this.state.hasError && this.state.error) {
      // Use custom fallback or determine appropriate fallback based on error type
      const FallbackComponent = this.props.fallback || getErrorFallback(this.state.error);
      return <FallbackComponent error={this.state.error} retry={this.handleRetry} />;
    }

    return this.props.children;
  }
}

// Hook for Next.js route change integration
export function useErrorBoundaryReset() {
  const router = useRouter();
  
  return React.useCallback(() => {
    // Force a route refresh to reset any error states
    router.refresh();
  }, [router]);
}

// Enhanced error boundary with Next.js integration
export const NextErrorBoundary: React.FC<ErrorBoundaryProps> = ({ 
  children, 
  resetOnRouteChange = true,
  ...props 
}) => {
  const pathname = usePathname();
  const [routeKey, setRouteKey] = React.useState(pathname);
  
  React.useEffect(() => {
    if (resetOnRouteChange && pathname !== routeKey) {
      setRouteKey(pathname);
    }
  }, [pathname, routeKey, resetOnRouteChange]);
  
  return (
    <ErrorBoundary 
      {...props}
      resetKeys={resetOnRouteChange ? [routeKey] : props.resetKeys}
    >
      {children}
    </ErrorBoundary>
  );
};

// Higher-order component for wrapping components with error boundary
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>,
  resetOnRouteChange = true
) => {
  const WrappedComponent: React.FC<P> = (props) => (
    <NextErrorBoundary fallback={fallback} resetOnRouteChange={resetOnRouteChange}>
      <Component {...props} />
    </NextErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

// Higher-order component with specific error type handling
export const withNetworkErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>
) => withErrorBoundary(Component, NetworkErrorFallback);

export const withValidationErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>
) => withErrorBoundary(Component, ValidationErrorFallback);

export const withDatabaseErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>
) => withErrorBoundary(Component, DatabaseErrorFallback);

export const withBusinessErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>
) => withErrorBoundary(Component, BusinessLogicErrorFallback);

// Sentry's built-in error boundary (alternative approach)
export const SentryErrorBoundary = Sentry.ErrorBoundary;

export default ErrorBoundary;