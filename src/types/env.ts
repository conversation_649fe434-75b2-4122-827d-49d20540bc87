/**
 * Environment variable types and validation
 */

export interface EnvironmentConfig {
  // Supabase Configuration
  supabase: {
    url: string;
    anonKey: string;
  };
  
  // Sentry Configuration
  sentry: {
    dsn: string;
    environment: 'development' | 'staging' | 'production';
  };
  
  // PostHog Configuration
  posthog: {
    apiKey: string;
    apiHost: string;
  };
  
  // Application Configuration
  app: {
    environment: string;
    version: string;
  };
}

export interface CustomProcessEnv {
  // Supabase (Public)
  NEXT_PUBLIC_SUPABASE_URL: string;
  NEXT_PUBLIC_SUPABASE_ANON_KEY: string;
  
  // Supabase (Server-only)
  SUPABASE_SERVICE_ROLE_KEY?: string;
  
  // Sentry (Public)
  NEXT_PUBLIC_SENTRY_DSN: string;
  NEXT_PUBLIC_SENTRY_ENVIRONMENT: 'development' | 'staging' | 'production';
  
  // Sentry (Server-only)
  SENTRY_AUTH_TOKEN?: string;
  SENTRY_ORG?: string;
  SENTRY_PROJECT?: string;
  
  // PostHog (Public)
  NEXT_PUBLIC_POSTHOG_API_KEY: string;
  NEXT_PUBLIC_POSTHOG_API_HOST: string;
  
  // Security (Server-only)
  CORS_ORIGIN?: string;
  TRUSTED_HOSTS?: string;
  CSP_REPORT_URI?: string;
  
  // Database (Server-only)
  DATABASE_URL?: string;
  DB_CONNECTION_TIMEOUT?: string;
  DB_MAX_CONNECTIONS?: string;
  
  // Node Environment
  NODE_ENV: 'development' | 'production' | 'test';
  
  // Debug flags
  DEBUG?: string;
  NEXT_PUBLIC_DEBUG?: string;
  FEATURE_ANALYTICS_DEBUG?: string;
  FEATURE_SENTRY_DEBUG?: string;
}

/**
 * Server-only secure configuration interface
 * These variables should never be exposed to the client
 */
export interface SecureEnvironmentConfig {
  sentry: {
    authToken?: string;
    org?: string;
    project?: string;
  };
  supabase: {
    serviceRoleKey?: string;
  };
  security: {
    corsOrigin?: string;
    trustedHosts: string[];
    cspReportUri?: string;
  };
  database: {
    url?: string;
    connectionTimeout: number;
    maxConnections: number;
  };
}

// Extend the global ProcessEnv interface
declare global {
  namespace NodeJS {
    interface ProcessEnv extends Partial<CustomProcessEnv> {}
  }
}

export {};