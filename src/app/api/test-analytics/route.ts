import { NextRequest, NextResponse } from 'next/server';

/**
 * Test endpoint to verify analytics configuration and send test events
 * This endpoint helps debug PostHog issues in production
 */
export async function GET() {
  try {
    // Get environment configuration
    const config = {
      posthog: {
        apiKey: process.env.NEXT_PUBLIC_POSTHOG_API_KEY ? 'configured' : 'missing',
        apiKeyLength: process.env.NEXT_PUBLIC_POSTHOG_API_KEY?.length || 0,
        apiKeyPrefix: process.env.NEXT_PUBLIC_POSTHOG_API_KEY?.substring(0, 8) || 'none',
        apiHost: process.env.NEXT_PUBLIC_POSTHOG_API_HOST || 'default',
      },
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString(),
    };

    return NextResponse.json({
      success: true,
      message: 'Analytics configuration status',
      config,
      instructions: {
        testEvent: 'POST to this endpoint with { "event": "test_event", "properties": {...} }',
        clientTest: 'Add ?debug=true to any page URL to see PostHog debug component'
      }
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Failed to get analytics configuration',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { event, properties } = await request.json();
    
    // This endpoint receives test events from the client
    // In a real implementation, you might want to forward these to PostHog server-side
    
    const testResult = {
      success: true,
      message: 'Test event received on server',
      event,
      properties: {
        ...properties,
        serverTimestamp: new Date().toISOString(),
        userAgent: request.headers.get('user-agent'),
        ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
      },
      config: {
        posthogConfigured: !!process.env.NEXT_PUBLIC_POSTHOG_API_KEY,
        environment: process.env.NODE_ENV
      }
    };
    
    // Log the test event for debugging
    console.log('📊 Analytics test event received:', testResult);
    
    return NextResponse.json(testResult);
  } catch (error) {
    console.error('Analytics test event error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to process test event',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 400 });
  }
}