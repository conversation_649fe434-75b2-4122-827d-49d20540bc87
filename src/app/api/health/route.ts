/**
 * Enhanced health check API route with comprehensive monitoring
 * Provides detailed health status for all system components and external services
 */

import { NextRequest, NextResponse } from "next/server";
import {
  withErrorHandling,
  withRateLimit,
} from "../../../lib/middleware/errorMiddleware";
import { TryCatchEnhancer } from "../../../lib/TryCatchEnhancer";
import { supabase, checkDatabaseConnection } from "../../../lib/supabase";
import { checkPostHogHealth, getPostHogHealthStatus } from "../../../lib/posthog";
import globalAnalytics from "../../../lib/global-analytics";
import { getSentryConfig } from "../../../lib/sentry";
import { getEnvironmentConfig } from "../../../lib/env";
import { validateEnvironmentForContext } from "../../../lib/security/envValidator";
import * as Sentry from "@sentry/nextjs";

interface ServiceHealthCheck {
  status: "up" | "down" | "degraded";
  responseTime?: number;
  error?: string;
  details?: Record<string, any>;
}

interface ExternalServiceCheck {
  status: "up" | "down";
  responseTime?: number;
  error?: string;
  lastChecked: string;
}

interface HealthCheckResult {
  status: "healthy" | "degraded" | "unhealthy";
  timestamp: string;
  version: string;
  environment: string;
  services: {
    database: ServiceHealthCheck & {
      connectionPool?: {
        active: number;
        idle: number;
        total: number;
      };
      tables?: {
        accessible: number;
        total: number;
      };
    };
    analytics: ServiceHealthCheck & {
      posthog: {
        initialized: boolean;
        healthy: boolean;
        lastError?: string;
      };
    };
    monitoring: ServiceHealthCheck & {
      sentry: {
        configured: boolean;
        dsn: string;
        environment: string;
      };
    };
    external: {
      status: "up" | "down" | "degraded";
      services: Record<string, ExternalServiceCheck>;
      summary: {
        total: number;
        up: number;
        down: number;
        degraded: number;
      };
    };
  };
  security: {
    environment_validation: {
      status: "healthy" | "degraded" | "unhealthy";
      errors: string[];
      warnings: string[];
      security_issues: string[];
    };
    configuration: {
      https_enforced: boolean;
      cors_configured: boolean;
      csp_configured: boolean;
      trusted_hosts_configured: boolean;
      sensitive_vars_protected: boolean;
    };
  };
  performance: {
    uptime: number;
    memoryUsage: NodeJS.MemoryUsage;
    cpuUsage?: NodeJS.CpuUsage;
    loadAverage?: number[];
  };
  configuration: {
    environment: string;
    nodeVersion: string;
    nextVersion?: string;
    timezone: string;
    env_vars_summary?: {
      total: number;
      public: number;
      server_only: number;
    };
  };
}

/**
 * Check database connectivity with comprehensive diagnostics
 */
async function checkDatabaseHealth(): Promise<ServiceHealthCheck & {
  connectionPool?: {
    active: number;
    idle: number;
    total: number;
  };
  tables?: {
    accessible: number;
    total: number;
  };
}> {
  return (
    (await TryCatchEnhancer.wrapAsync(
      async () => {
        const startTime = performance.now();
        
        // Test basic connectivity
        const connectionStatus = await checkDatabaseConnection();
        if (connectionStatus !== 'connected') {
          throw new Error(`Database connection failed: ${connectionStatus}`);
        }

        // Test table accessibility
        const tableTests = [
          'contact_submissions',
          'properties', 
          'bookings',
          'social_posts',
          'blog_articles',
          'availability_calendar'
        ];

        let accessibleTables = 0;
        const tableErrors: string[] = [];

        for (const tableName of tableTests) {
          try {
            const { error } = await supabase
              .from(tableName as any)
              .select('*')
              .limit(1);
            
            if (!error) {
              accessibleTables++;
            } else {
              tableErrors.push(`${tableName}: ${error.message}`);
            }
          } catch (error) {
            tableErrors.push(`${tableName}: ${(error as Error).message}`);
          }
        }

        const responseTime = performance.now() - startTime;

        // Determine status based on table accessibility
        let status: "up" | "down" | "degraded" = "up";
        if (accessibleTables === 0) {
          status = "down";
        } else if (accessibleTables < tableTests.length) {
          status = "degraded";
        }

        return {
          status,
          responseTime: Math.round(responseTime),
          tables: {
            accessible: accessibleTables,
            total: tableTests.length,
          },
          details: {
            connectionStatus,
            tableErrors: tableErrors.length > 0 ? tableErrors : undefined,
          },
        };
      },
      {
        operation: "database_health_check",
        category: "database",
        severity: "high",
        businessContext: {
          entityType: "health_check",
          workflow: "system_monitoring",
        },
        fallbackValue: {
          status: "down" as const,
          error: "Database connectivity check failed",
          tables: {
            accessible: 0,
            total: 6,
          },
        },
        onError: (error) => {
          console.error("Database health check failed:", error);
        },
      }
    )) || {
      status: "down" as const,
      error: "Database connectivity check failed",
      tables: {
        accessible: 0,
        total: 6,
      },
    }
  );
}

/**
 * Check analytics service health with detailed PostHog diagnostics
 */
async function checkAnalyticsHealth(): Promise<ServiceHealthCheck & {
  posthog: {
    initialized: boolean;
    healthy: boolean;
    lastError?: string;
  };
}> {
  return (
    (await TryCatchEnhancer.wrapAsync(
      async () => {
        const startTime = performance.now();

        // Get PostHog health status from the manager
        const posthogHealthStatus = getPostHogHealthStatus();
        const isPostHogHealthy = await checkPostHogHealth();
        
        // Also check global analytics status
        const globalAnalyticsStatus = globalAnalytics.getStatus();

        const responseTime = performance.now() - startTime;

        // Determine overall analytics status - prioritize global analytics
        let status: "up" | "down" | "degraded" = "up";
        if (globalAnalyticsStatus.ready && globalAnalyticsStatus.initialized) {
          status = "up";
        } else if (!posthogHealthStatus.initialized && !globalAnalyticsStatus.initialized) {
          status = "down";
        } else if (!isPostHogHealthy || !posthogHealthStatus.healthy) {
          status = "degraded";
        }

        return {
          status,
          responseTime: Math.round(responseTime),
          posthog: {
            initialized: posthogHealthStatus.initialized || globalAnalyticsStatus.initialized,
            healthy: isPostHogHealthy || globalAnalyticsStatus.ready,
            lastError: posthogHealthStatus.lastError || globalAnalyticsStatus.lastError,
          },
          details: {
            initializationAttempts: posthogHealthStatus.initializationAttempts,
            lastAttempt: posthogHealthStatus.lastAttempt,
            lastError: posthogHealthStatus.lastError,
            state: posthogHealthStatus.state,
            globalAnalytics: {
              mode: globalAnalyticsStatus.mode,
              ready: globalAnalyticsStatus.ready,
              eventsSent: globalAnalyticsStatus.eventsSent,
              distinctId: globalAnalyticsStatus.distinctId
            },
          },
        };
      },
      {
        operation: "analytics_health_check",
        category: "external",
        severity: "medium",
        businessContext: {
          entityType: "health_check",
          workflow: "system_monitoring",
        },
        fallbackValue: {
          status: "down" as const,
          error: "Analytics service check failed",
          posthog: {
            initialized: false,
            healthy: false,
            lastError: "Health check failed",
          },
        },
        onError: (error) => {
          console.error("Analytics health check failed:", error);
        },
      }
    )) || {
      status: "down" as const,
      error: "Analytics service check failed",
      posthog: {
        initialized: false,
        healthy: false,
        lastError: "Health check failed",
      },
    }
  );
}

/**
 * Check monitoring service health (Sentry)
 */
async function checkMonitoringHealth(): Promise<ServiceHealthCheck & {
  sentry: {
    configured: boolean;
    dsn: string;
    environment: string;
  };
}> {
  return (
    (await TryCatchEnhancer.wrapAsync(
      async () => {
        const startTime = performance.now();
        
        // Check Sentry configuration
        const sentryConfig = getSentryConfig();
        const isConfigured = sentryConfig !== null;
        
        let status: "up" | "down" | "degraded" = "up";
        let error: string | undefined;

        if (!isConfigured) {
          status = "down";
          error = "Sentry not configured";
        } else {
          // Test Sentry connectivity by sending a test event
          try {
            // Create a test transaction to verify Sentry is working
            Sentry.withScope((scope) => {
              scope.setTag('health_check', true);
              scope.setLevel('info');
              Sentry.addBreadcrumb({
                message: 'Health check test',
                category: 'health',
                level: 'info',
              });
            });
          } catch (sentryError) {
            status = "degraded";
            error = `Sentry test failed: ${(sentryError as Error).message}`;
          }
        }

        const responseTime = performance.now() - startTime;

        return {
          status,
          responseTime: Math.round(responseTime),
          error,
          sentry: {
            configured: isConfigured,
            dsn: sentryConfig?.dsn.substring(0, 20) + '...' || 'not configured',
            environment: sentryConfig?.environment || 'unknown',
          },
        };
      },
      {
        operation: "monitoring_health_check",
        category: "external",
        severity: "medium",
        businessContext: {
          entityType: "health_check",
          workflow: "system_monitoring",
        },
        fallbackValue: {
          status: "down" as const,
          error: "Monitoring service check failed",
          sentry: {
            configured: false,
            dsn: 'unknown',
            environment: 'unknown',
          },
        },
        onError: (error) => {
          console.error("Monitoring health check failed:", error);
        },
      }
    )) || {
      status: "down" as const,
      error: "Monitoring service check failed",
      sentry: {
        configured: false,
        dsn: 'unknown',
        environment: 'unknown',
      },
    }
  );
}

/**
 * Check security configuration and environment variable validation
 */
async function checkSecurityHealth(): Promise<{
  environment_validation: {
    status: "healthy" | "degraded" | "unhealthy";
    errors: string[];
    warnings: string[];
    security_issues: string[];
  };
  configuration: {
    https_enforced: boolean;
    cors_configured: boolean;
    csp_configured: boolean;
    trusted_hosts_configured: boolean;
    sensitive_vars_protected: boolean;
  };
}> {
  return (
    (await TryCatchEnhancer.wrapAsync(
      async () => {
        // Validate environment variables
        const envValidation = validateEnvironmentForContext('api');
        
        // Determine environment validation status
        let envStatus: "healthy" | "degraded" | "unhealthy" = "healthy";
        if (envValidation.securityIssues.length > 0) {
          envStatus = "unhealthy";
        } else if (envValidation.errors.length > 0) {
          envStatus = "degraded";
        }

        // Check security configuration
        const securityConfig = {
          https_enforced: process.env.NODE_ENV === 'production',
          cors_configured: !!process.env.CORS_ORIGIN,
          csp_configured: !!process.env.CSP_REPORT_URI,
          trusted_hosts_configured: !!process.env.TRUSTED_HOSTS,
          sensitive_vars_protected: typeof window === 'undefined', // Server-side check
        };

        return {
          environment_validation: {
            status: envStatus,
            errors: envValidation.errors,
            warnings: envValidation.warnings,
            security_issues: envValidation.securityIssues,
          },
          configuration: securityConfig,
        };
      },
      {
        operation: "security_health_check",
        category: "validation",
        severity: "high",
        businessContext: {
          entityType: "health_check",
          workflow: "security_monitoring",
        },
        fallbackValue: {
          environment_validation: {
            status: "unhealthy" as const,
            errors: ["Security health check failed"],
            warnings: [],
            security_issues: ["Unable to validate security configuration"],
          },
          configuration: {
            https_enforced: false,
            cors_configured: false,
            csp_configured: false,
            trusted_hosts_configured: false,
            sensitive_vars_protected: false,
          },
        },
        onError: (error) => {
          console.error("Security health check failed:", error);
        },
      }
    )) || {
      environment_validation: {
        status: "unhealthy" as const,
        errors: ["Security health check failed"],
        warnings: [],
        security_issues: ["Unable to validate security configuration"],
      },
      configuration: {
        https_enforced: false,
        cors_configured: false,
        csp_configured: false,
        trusted_hosts_configured: false,
        sensitive_vars_protected: false,
      },
    }
  );
}

/**
 * Check external services health with detailed diagnostics
 */
async function checkExternalServicesHealth(): Promise<{
  status: "up" | "down" | "degraded";
  services: Record<string, ExternalServiceCheck>;
  summary: {
    total: number;
    up: number;
    down: number;
    degraded: number;
  };
}> {
  return (
    (await TryCatchEnhancer.wrapAsync(
      async () => {
        const services: Record<string, ExternalServiceCheck> = {};
        const timestamp = new Date().toISOString();

        // Get environment config for service URLs
        const envConfig = getEnvironmentConfig();

        // Define external services to check
        const serviceChecks = [
          { 
            name: "supabase", 
            url: `${envConfig.supabase.url}/rest/v1/`,
            headers: {
              'apikey': envConfig.supabase.anonKey,
              'Authorization': `Bearer ${envConfig.supabase.anonKey}`
            } as Record<string, string>
          },
          { 
            name: "posthog", 
            url: `${envConfig.posthog.apiHost}/decide/`,
            headers: {} as Record<string, string>
          },
          { 
            name: "sentry", 
            url: "https://sentry.io/api/0/",
            headers: {} as Record<string, string>
          },
        ];

        // Check each service with timeout and error handling
        for (const service of serviceChecks) {
          const startTime = performance.now();
          
          try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout
            
            const response = await fetch(service.url, {
              method: "HEAD",
              headers: service.headers,
              signal: controller.signal,
            });
            
            clearTimeout(timeoutId);
            const responseTime = Math.round(performance.now() - startTime);

            services[service.name] = {
              status: response.ok ? "up" : "down",
              responseTime,
              lastChecked: timestamp,
              error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`,
            };
          } catch (error) {
            const responseTime = Math.round(performance.now() - startTime);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            services[service.name] = {
              status: "down",
              responseTime,
              lastChecked: timestamp,
              error: errorMessage,
            };
          }
        }

        // Calculate summary statistics
        const statuses = Object.values(services).map(s => s.status);
        const summary = {
          total: statuses.length,
          up: statuses.filter(s => s === "up").length,
          down: statuses.filter(s => s === "down").length,
          degraded: 0, // Services only return up/down, degraded is calculated at component level
        };

        // Determine overall external services status
        let overallStatus: "up" | "down" | "degraded" = "up";
        if (summary.down === summary.total) {
          overallStatus = "down";
        } else if (summary.down > 0 || summary.degraded > 0) {
          overallStatus = "degraded";
        }

        return {
          status: overallStatus,
          services,
          summary,
        };
      },
      {
        operation: "external_services_health_check",
        category: "external",
        severity: "low",
        businessContext: {
          entityType: "health_check",
          workflow: "system_monitoring",
        },
        fallbackValue: {
          status: "down" as const,
          services: {},
          summary: {
            total: 0,
            up: 0,
            down: 0,
            degraded: 0,
          },
        },
        onError: (error) => {
          console.error("External services health check failed:", error);
        },
      }
    )) || {
      status: "down" as const,
      services: {},
      summary: {
        total: 0,
        up: 0,
        down: 0,
        degraded: 0,
      },
    }
  );
}

/**
 * GET /api/health - Comprehensive system health check
 */
async function handleHealthCheck(request: NextRequest): Promise<NextResponse> {
  const result = await TryCatchEnhancer.wrapAsync(
    async () => {
      const startTime = performance.now();
      
      // Get environment configuration
      const envConfig = getEnvironmentConfig();
      
      // Run all health checks in parallel for better performance
      const [databaseHealth, analyticsHealth, monitoringHealth, externalHealth, securityHealth] =
        await Promise.all([
          checkDatabaseHealth(),
          checkAnalyticsHealth(),
          checkMonitoringHealth(),
          checkExternalServicesHealth(),
          checkSecurityHealth(),
        ]);

      // Determine overall system status based on service criticality
      let overallStatus: "healthy" | "degraded" | "unhealthy" = "healthy";

      // Security issues are critical - if unhealthy, system is unhealthy
      if (securityHealth.environment_validation.status === "unhealthy") {
        overallStatus = "unhealthy";
      }
      // Database is critical - if down, system is unhealthy
      else if (databaseHealth.status === "down") {
        overallStatus = "unhealthy";
      }
      // If security is degraded, database is degraded, or other critical services are down, system is degraded
      else if (
        securityHealth.environment_validation.status === "degraded" ||
        databaseHealth.status === "degraded" ||
        monitoringHealth.status === "down" ||
        externalHealth.status === "down"
      ) {
        overallStatus = "degraded";
      }
      // If non-critical services are degraded, system is still degraded
      else if (
        analyticsHealth.status === "degraded" ||
        monitoringHealth.status === "degraded" ||
        externalHealth.status === "degraded"
      ) {
        overallStatus = "degraded";
      }

      const totalResponseTime = Math.round(performance.now() - startTime);

      // Get environment variables summary
      const envVarsSummary = process.env.NODE_ENV === 'development' ? {
        total: Object.keys(process.env).length,
        public: Object.keys(process.env).filter(key => key.startsWith('NEXT_PUBLIC_')).length,
        server_only: Object.keys(process.env).filter(key => !key.startsWith('NEXT_PUBLIC_')).length,
      } : undefined;

      const healthResult: HealthCheckResult = {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || "1.0.0",
        environment: envConfig.app.environment,
        services: {
          database: databaseHealth,
          analytics: analyticsHealth,
          monitoring: monitoringHealth,
          external: externalHealth,
        },
        security: securityHealth,
        performance: {
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
          cpuUsage: process.cpuUsage ? process.cpuUsage() : undefined,
          loadAverage: 'loadavg' in process && typeof (process as any).loadavg === 'function' ? (process as any).loadavg() : undefined,
        },
        configuration: {
          environment: envConfig.app.environment,
          nodeVersion: process.version,
          nextVersion: process.env.npm_package_version,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          env_vars_summary: envVarsSummary,
        },
      };

      // Set appropriate HTTP status codes
      const statusCode =
        overallStatus === "healthy"
          ? 200
          : overallStatus === "degraded"
          ? 200  // 200 for degraded but functional
          : 503; // 503 for unhealthy/service unavailable

      // Add performance headers
      const response = NextResponse.json(healthResult, { status: statusCode });
      response.headers.set('X-Response-Time', `${totalResponseTime}ms`);
      response.headers.set('X-Health-Status', overallStatus);
      response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
      
      return response;
    },
    {
      operation: "system_health_check",
      category: "validation",
      severity: "medium",
      businessContext: {
        entityType: "system_health",
        workflow: "monitoring",
      },
      context: {
        page: {
          url: "server",
          section: "api/health",
          component: "health_check",
        },
        browser: {
          name: "server",
          version: "unknown",
          userAgent: request.headers.get('user-agent') || 'unknown',
        },
        technical: {
          apiEndpoint: "/api/health",
          requestId: `health_check_${Date.now()}`,
        },
      },
    }
  );

  // Handle the case where TryCatchEnhancer returns undefined
  if (!result) {
    const fallbackResult: HealthCheckResult = {
      status: "unhealthy",
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || "1.0.0",
      environment: process.env.NODE_ENV || "unknown",
      services: {
        database: { 
          status: "down", 
          error: "Health check failed",
          tables: { accessible: 0, total: 6 }
        },
        analytics: { 
          status: "down", 
          error: "Health check failed",
          posthog: { initialized: false, healthy: false, lastError: "Health check failed" }
        },
        monitoring: {
          status: "down",
          error: "Health check failed",
          sentry: { configured: false, dsn: 'unknown', environment: 'unknown' }
        },
        external: { 
          status: "down", 
          services: {},
          summary: { total: 0, up: 0, down: 0, degraded: 0 }
        },
      },
      security: {
        environment_validation: {
          status: "unhealthy",
          errors: ["Health check failed - unable to validate environment"],
          warnings: [],
          security_issues: ["Security validation failed"],
        },
        configuration: {
          https_enforced: false,
          cors_configured: false,
          csp_configured: false,
          trusted_hosts_configured: false,
          sensitive_vars_protected: false,
        },
      },
      performance: {
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
      },
      configuration: {
        environment: process.env.NODE_ENV || "unknown",
        nodeVersion: process.version,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      },
    };

    const response = NextResponse.json(fallbackResult, { status: 503 });
    response.headers.set('X-Health-Status', 'unhealthy');
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    
    return response;
  }

  return result;
}

// Apply rate limiting to prevent abuse of health check endpoint
export const GET = withRateLimit(
  100, // 100 requests
  60 * 1000, // per minute
  (request) => request.headers.get("x-forwarded-for") || "anonymous"
)(withErrorHandling(handleHealthCheck));
