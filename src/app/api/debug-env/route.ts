import { NextRequest, NextResponse } from 'next/server';

/**
 * Debug endpoint to check environment variables
 * This should only be used for debugging and should be removed in production
 */
export async function GET(request: NextRequest) {
  // Only allow in development or with a debug token
  const debugToken = request.nextUrl.searchParams.get('token');
  const isDev = process.env.NODE_ENV === 'development';
  const validToken = debugToken === 'debug-env-2024';
  
  if (!isDev && !validToken) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'NEXT_PUBLIC_SENTRY_DSN',
    'NEXT_PUBLIC_POSTHOG_API_KEY',
  ];

  const placeholderPatterns = [
    /your_.*_here/i,
    /replace.*with.*actual/i,
    /todo.*replace/i,
    /example/i,
    /test.*key/i,
    /dummy/i,
    /placeholder/i,
  ];

  function isPlaceholderValue(value: string): boolean {
    return placeholderPatterns.some(pattern => pattern.test(value));
  }

  function maskValue(value: string): string {
    if (!value) return '[EMPTY]';
    if (value.length <= 8) return '[REDACTED]';
    return `${value.substring(0, 4)}...${value.substring(value.length - 4)}`;
  }

  const results: Record<string, any> = {};
  const summary = {
    valid: 0,
    missing: 0,
    placeholders: 0,
    issues: [] as string[]
  };

  requiredVars.forEach(varName => {
    const value = process.env[varName];
    const exists = !!value;
    const isEmpty = !value || value.trim() === '';
    const isPlaceholder = value ? isPlaceholderValue(value) : false;

    results[varName] = {
      exists,
      isEmpty,
      isPlaceholder,
      masked: value ? maskValue(value) : '[NOT_SET]',
      length: value ? value.length : 0,
    };

    // Add specific validations
    if (varName.includes('POSTHOG') && value) {
      results[varName].startsWithPhc = value.startsWith('phc_');
      if (!value.startsWith('phc_')) {
        summary.issues.push(`${varName}: Should start with 'phc_'`);
      }
    }

    if (varName.includes('SUPABASE_URL') && value) {
      results[varName].isValidSupabaseUrl = value.includes('.supabase.co');
      if (!value.includes('.supabase.co')) {
        summary.issues.push(`${varName}: Should contain '.supabase.co'`);
      }
    }

    if (varName.includes('SENTRY_DSN') && value) {
      results[varName].isValidSentryDsn = value.includes('sentry.io');
      if (!value.includes('sentry.io')) {
        summary.issues.push(`${varName}: Should contain 'sentry.io'`);
      }
    }

    // Count for summary
    if (!exists || isEmpty) {
      summary.missing++;
      summary.issues.push(`${varName}: Missing or empty`);
    } else if (isPlaceholder) {
      summary.placeholders++;
      summary.issues.push(`${varName}: Contains placeholder value`);
    } else {
      summary.valid++;
    }
  });

  const response = {
    timestamp: new Date().toISOString(),
    environment: {
      NODE_ENV: process.env.NODE_ENV,
      hostname: process.env.HOSTNAME,
      platform: process.platform,
    },
    summary,
    variables: results,
    status: summary.issues.length === 0 ? 'healthy' : 'issues_found',
  };

  return NextResponse.json(response, {
    status: summary.issues.length === 0 ? 200 : 400,
    headers: {
      'Cache-Control': 'no-store, no-cache, must-revalidate',
    },
  });
}
