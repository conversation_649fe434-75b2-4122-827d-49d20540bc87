/**
 * Contact form API route with comprehensive error handling
 * Demonstrates enhanced middleware integration and error reporting
 */

import { NextRequest, NextResponse } from "next/server";
import {
  withErrorHandling,
  withBusinessOperation,
  withValidation,
} from "../../../lib/middleware/errorMiddleware";
import { BusinessOperation } from "../../../lib/errorHandling";
import { TryCatchEnhancer } from "../../../lib/TryCatchEnhancer";
import { ContactService } from "../../../lib/services/ContactService";

// Contact form validation schema
interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  message: string;
  subject?: string;
}

/**
 * Validate contact form data
 */
function validateContactForm(data: any): ContactFormData {
  return TryCatchEnhancer.wrapSync(
    () => {
      const errors: string[] = [];

      if (
        !data.name ||
        typeof data.name !== "string" ||
        data.name.trim().length < 2
      ) {
        errors.push("Name must be at least 2 characters long");
      }

      if (!data.email || typeof data.email !== "string") {
        errors.push("Email is required");
      } else {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
          errors.push("Invalid email format");
        }
      }

      if (
        !data.message ||
        typeof data.message !== "string" ||
        data.message.trim().length < 10
      ) {
        errors.push("Message must be at least 10 characters long");
      }

      if (data.phone && typeof data.phone === "string") {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        if (!phoneRegex.test(data.phone.replace(/[\s\-\(\)]/g, ""))) {
          errors.push("Invalid phone number format");
        }
      }

      if (errors.length > 0) {
        throw new Error(`Validation failed: ${errors.join(", ")}`);
      }

      return {
        name: data.name.trim(),
        email: data.email.toLowerCase().trim(),
        phone: data.phone?.trim(),
        message: data.message.trim(),
        subject: data.subject?.trim() || "Contact Form Submission",
      };
    },
    {
      operation: "validate_contact_form",
      category: "validation",
      severity: "medium",
      businessContext: {
        entityType: "contact_form",
        workflow: "form_submission",
      },
      context: {
        page: {
          url: 'server',
          section: 'api/contact',
          component: 'validation'
        },
        browser: {
          name: 'server',
          version: 'unknown',
          userAgent: 'server'
        },
        technical: {
          apiEndpoint: '/api/contact',
          requestId: `validation_${Date.now()}`
        },
      },
    }
  ) as ContactFormData;
}

/**
 * POST /api/contact - Submit contact form
 */
async function handleContactSubmission(
  request: NextRequest,
  _context: any,
  validatedData: ContactFormData
): Promise<NextResponse> {
  const result = await TryCatchEnhancer.wrapAsync(
    async () => {
      const contactService = new ContactService();

      // Check for recent submissions to prevent spam
      const hasRecentSubmission = await contactService.hasRecentSubmission(
        validatedData.email
      );

      if (hasRecentSubmission) {
        return NextResponse.json(
          {
            error:
              "Too many recent submissions. Please wait before submitting again.",
            code: "RATE_LIMITED",
          },
          { status: 429 }
        );
      }

      // Create contact submission
      const submission = await contactService.createContactSubmission({
        name: validatedData.name,
        email: validatedData.email,
        phone: validatedData.phone,
        message: validatedData.message,
        subject: validatedData.subject,
        source: "website_contact_form",
        metadata: {
          userAgent: request.headers.get("user-agent"),
          referer: request.headers.get("referer"),
          ip: request.headers.get("x-forwarded-for") || "unknown",
          timestamp: new Date().toISOString(),
        },
      });

      // Send notification email (would be implemented)
      // await sendNotificationEmail(submission);

      return NextResponse.json(
        {
          success: true,
          message: "Thank you for your message. We will get back to you soon!",
          submissionId: submission.id,
        },
        { status: 201 }
      );
    },
    {
      operation: "handle_contact_submission",
      category: "business",
      severity: "medium",
      businessContext: {
        entityType: "contact_submission",
        workflow: "form_submission",
      },
      context: {
        page: {
          url: 'server',
          section: 'api/contact',
          component: 'submission'
        },
        browser: {
          name: 'server',
          version: 'unknown',
          userAgent: request.headers.get("user-agent") || 'server'
        },
        technical: {
          apiEndpoint: '/api/contact',
          requestId: `submission_${Date.now()}`
        },
      },
    }
  );

  // Handle the case where TryCatchEnhancer returns undefined
  if (!result) {
    return NextResponse.json(
      { error: "Internal server error", code: "INTERNAL_ERROR" },
      { status: 500 }
    );
  }

  return result;
}

// Apply middleware layers for comprehensive error handling
export const POST = withBusinessOperation(
  BusinessOperation.CONTACT_FORM_SUBMIT,
  "contact_form",
  withValidation(validateContactForm, handleContactSubmission)
);

/**
 * GET /api/contact - Get contact form statistics (admin only)
 */
async function handleContactStats(
  request: NextRequest,
  _context: any
): Promise<NextResponse> {
  const result = await TryCatchEnhancer.wrapAsync(
    async () => {
      // In a real app, you'd check authentication here
      const authHeader = request.headers.get("authorization");
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      const contactService = new ContactService();
      const stats = await contactService.getContactStats();

      return NextResponse.json({
        success: true,
        data: stats,
      });
    },
    {
      operation: "get_contact_stats",
      category: "business",
      severity: "low",
      businessContext: {
        entityType: "contact_statistics",
        workflow: "admin_dashboard",
      },
      context: {
        page: {
          url: 'server',
          section: 'api/contact',
          component: 'stats'
        },
        browser: {
          name: 'server',
          version: 'unknown',
          userAgent: request.headers.get("user-agent") || 'server'
        },
        technical: {
          apiEndpoint: '/api/contact/stats',
          requestId: `stats_${Date.now()}`
        },
      },
    }
  );

  // Handle the case where TryCatchEnhancer returns undefined
  if (!result) {
    return NextResponse.json(
      { error: "Internal server error", code: "INTERNAL_ERROR" },
      { status: 500 }
    );
  }

  return result;
}

export const GET = withErrorHandling(handleContactStats);
