/**
 * Test Page - Verify styling and analytics
 */

'use client';

import React, { useEffect, useState } from 'react';

export default function TestPage() {
  const [analyticsStatus, setAnalyticsStatus] = useState<any>(null);
  const [healthStatus, setHealthStatus] = useState<any>(null);

  useEffect(() => {
    // Check analytics status
    const checkAnalytics = () => {
      if (typeof window !== 'undefined' && (window as any).getAnalyticsStatus) {
        const status = (window as any).getAnalyticsStatus();
        setAnalyticsStatus(status);
      }
    };

    // Check health API
    const checkHealth = async () => {
      try {
        const response = await fetch('/api/health');
        const health = await response.json();
        setHealthStatus(health.services?.analytics);
      } catch (error) {
        console.error('Health check failed:', error);
      }
    };

    // Initial checks
    checkAnalytics();
    checkHealth();

    // Check analytics every 2 seconds
    const interval = setInterval(checkAnalytics, 2000);

    return () => clearInterval(interval);
  }, []);

  const sendTestEvent = () => {
    if (typeof window !== 'undefined') {
      if ((window as any).trackEvent) {
        (window as any).trackEvent('test_page_click', {
          test_id: 'test_page_' + Date.now(),
          location: 'test_page',
          timestamp: new Date().toISOString()
        });
        alert('Test event sent! Check console and PostHog dashboard.');
      } else {
        alert('trackEvent function not available');
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-serif font-bold text-navy-900 mb-4">
              Test Page
            </h1>
            <p className="text-lg text-navy-700">
              Testing styling and analytics functionality
            </p>
          </div>

          {/* Styling Test */}
          <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-navy-900 mb-6">
              🎨 Styling Test
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="bg-navy-900 text-white p-4 rounded-lg">
                  <h3 className="font-bold">Navy Background</h3>
                  <p>This should have a navy background with white text</p>
                </div>
                
                <div className="bg-gold-500 text-white p-4 rounded-lg">
                  <h3 className="font-bold">Gold Background</h3>
                  <p>This should have a gold background with white text</p>
                </div>
              </div>
              
              <div className="space-y-4">
                <button className="w-full bg-gold-500 hover:bg-gold-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                  Gold Button (Hover Effect)
                </button>
                
                <button className="w-full bg-navy-900 hover:bg-navy-800 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                  Navy Button (Hover Effect)
                </button>
              </div>
            </div>

            <div className="mt-6 p-4 bg-gray-100 rounded-lg">
              <p className="text-sm text-gray-600">
                ✅ If you can see the colors and styling above, Tailwind CSS is working correctly!
              </p>
            </div>
          </div>

          {/* Analytics Test */}
          <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-navy-900 mb-6">
              📊 Analytics Test
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-bold text-lg mb-4">Global Analytics Status</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <pre className="text-sm overflow-auto">
                    {analyticsStatus ? JSON.stringify(analyticsStatus, null, 2) : 'Loading...'}
                  </pre>
                </div>
              </div>
              
              <div>
                <h3 className="font-bold text-lg mb-4">Health API Status</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <pre className="text-sm overflow-auto">
                    {healthStatus ? JSON.stringify(healthStatus, null, 2) : 'Loading...'}
                  </pre>
                </div>
              </div>
            </div>

            <div className="mt-6 space-y-4">
              <button
                onClick={sendTestEvent}
                className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                🧪 Send Test Event
              </button>
              
              <div className="p-4 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Instructions:</strong>
                  <br />
                  1. Click "Send Test Event" to test analytics
                  <br />
                  2. Check browser console for messages
                  <br />
                  3. Check PostHog dashboard for events
                  <br />
                  4. Look for events with test_id starting with "test_page_"
                </p>
              </div>
            </div>
          </div>

          {/* Global Functions Test */}
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-2xl font-bold text-navy-900 mb-6">
              🔧 Global Functions Test
            </h2>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[
                'analytics',
                'trackEvent',
                'sendTestEvent',
                'getAnalyticsStatus',
                'initializeAnalytics',
                'isAnalyticsReady'
              ].map((funcName) => {
                const isAvailable = typeof window !== 'undefined' && !!(window as any)[funcName];
                return (
                  <div
                    key={funcName}
                    className={`p-3 rounded-lg text-center text-sm ${
                      isAvailable
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    <div className="font-mono text-xs mb-1">{funcName}</div>
                    <div className="font-bold">
                      {isAvailable ? '✅ Available' : '❌ Missing'}
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
              <p className="text-sm text-yellow-800">
                <strong>Note:</strong> Global analytics functions should be available if the system is working correctly.
                If you see "Missing" functions, check the browser console for errors.
              </p>
            </div>
          </div>

          {/* Navigation */}
          <div className="mt-12 text-center">
            <a
              href="/"
              className="inline-flex items-center bg-navy-900 hover:bg-navy-800 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              ← Back to Home
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}