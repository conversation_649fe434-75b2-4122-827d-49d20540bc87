'use client'

import React from 'react'
import Header from '@/components/Header'
import Hero from '@/components/Hero'
import About from '@/components/About'
import Services from '@/components/Services'
// import Gallery from '@/components/Gallery'
import PropertyOwners from '@/components/PropertyOwners'
import Testimonials from '@/components/Testimonials'
import Contact from '@/components/Contact'
import Footer from '@/components/Footer'
import PWAInstallPrompt from '@/components/PWAInstallPrompt'
import { PrivacyConsent } from '@/components/PrivacyConsent'
import { ScrollProvider } from '@/components/ScrollContext'
import { PostHogTest } from '@/components/PostHogTest'
import { handleConsentChange } from '@/lib/analytics'

export default function HomePage() {
  const handlePrivacyConsentChange = (preferences: { analytics: boolean; marketing: boolean; functional: boolean }) => {
    // Update analytics consent based on user preferences
    handleConsentChange(preferences.analytics);
  };

  return (
    <ScrollProvider>
      <div className="font-sans antialiased">
        <Header />
        <main>
          <Hero />
          <PropertyOwners />
          <Services />
          {/* <Gallery /> */}
          <Testimonials />
          <About />
          <Contact />
        </main>
        <Footer />
        <PWAInstallPrompt />
        <PrivacyConsent onConsentChange={handlePrivacyConsentChange} />
        <PostHogTest />
      </div>
    </ScrollProvider>
  )
}