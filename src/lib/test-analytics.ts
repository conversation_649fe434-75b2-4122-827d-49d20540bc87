/**
 * Analytics Testing Utilities
 * Run these functions in the browser console to test PostHog
 */

import { debugAnalytics, sendTestEvent, trackEvent, isAnalyticsReady } from './analytics';

// Make functions available globally in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).testAnalytics = {
    debug: debugAnalytics,
    sendTest: sendTestEvent,
    isReady: isAnalyticsReady,
    trackTest: () => {
      trackEvent('page_view', {
        page: 'test',
        section: 'console',
        referrer: 'manual_test'
      });
      console.log('📊 Test page_view event sent');
    },
    trackCTA: () => {
      trackEvent('cta_click', {
        cta_type: 'console_test',
        location: 'browser_console',
        text: 'Console Test Button'
      });
      console.log('📊 Test cta_click event sent');
    }
  };
  
  console.log('🧪 Analytics test functions available:');
  console.log('- testAnalytics.debug() - Show debug info');
  console.log('- testAnalytics.sendTest() - Send test event');
  console.log('- testAnalytics.trackTest() - Send test page view');
  console.log('- testAnalytics.trackCTA() - Send test CTA click');
  console.log('- testAnalytics.isReady() - Check if ready');
}