import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database'

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

if (!supabaseUrl) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable')
}

if (!supabaseAnonKey) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable')
}

// Create Supabase client with TypeScript types
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true
  },
  db: {
    schema: 'public'
  },
  global: {
    headers: {
      'X-Client-Info': 'jna-business-solutions'
    }
  }
})

// Export types for convenience
export type { Database, Tables, TablesInsert, TablesUpdate, Enums } from '@/types/database'

// Helper types for common operations
export type Property = Database['public']['Tables']['properties']['Row']
export type PropertyInsert = Database['public']['Tables']['properties']['Insert']
export type PropertyUpdate = Database['public']['Tables']['properties']['Update']

export type Booking = Database['public']['Tables']['bookings']['Row']
export type BookingInsert = Database['public']['Tables']['bookings']['Insert']
export type BookingUpdate = Database['public']['Tables']['bookings']['Update']

export type ContactSubmission = Database['public']['Tables']['contact_submissions']['Row']
export type ContactSubmissionInsert = Database['public']['Tables']['contact_submissions']['Insert']

export type SocialPost = Database['public']['Tables']['social_posts']['Row']
export type SocialPostInsert = Database['public']['Tables']['social_posts']['Insert']
export type SocialPostUpdate = Database['public']['Tables']['social_posts']['Update']

export type BlogArticle = Database['public']['Tables']['blog_articles']['Row']
export type BlogArticleInsert = Database['public']['Tables']['blog_articles']['Insert']
export type BlogArticleUpdate = Database['public']['Tables']['blog_articles']['Update']

export type AvailabilityCalendar = Database['public']['Tables']['availability_calendar']['Row']
export type AvailabilityCalendarInsert = Database['public']['Tables']['availability_calendar']['Insert']
export type AvailabilityCalendarUpdate = Database['public']['Tables']['availability_calendar']['Update']

// Enum types
export type BookingStatus = Database['public']['Enums']['booking_status']
export type Platform = Database['public']['Enums']['platform']
export type PostType = Database['public']['Enums']['post_type']
export type AvailabilitySource = Database['public']['Enums']['availability_source']

// Database operation result types
export interface DatabaseResult<T> {
  success: boolean;
  data: T | null;
  error?: string;
}

// Connection status type
export type ConnectionStatus = 'connected' | 'disconnected' | 'error';

// Extended connection status interface for health checks
export interface ConnectionStatusExtended {
  isConnected: boolean;
  error?: string;
  timestamp: Date;
}

// Base database service class
export class DatabaseService {
  protected client = supabase;

  /**
   * Execute a database query with error handling
   */
  protected async executeQuery<T>(
    operation: string,
    queryFn: () => Promise<{ data: T | null; error: any }>
  ): Promise<T> {
    try {
      const result = await queryFn();
      
      if (result.error) {
        throw new Error(`${operation} failed: ${result.error.message}`);
      }
      
      if (result.data === null) {
        throw new Error(`${operation} returned no data`);
      }
      
      return result.data;
    } catch (error) {
      console.error(`Database operation "${operation}" failed:`, error);
      throw error;
    }
  }

  /**
   * Execute a database query with safe error handling (returns result object)
   */
  protected async executeQuerySafe<T>(
    operation: string,
    queryFn: () => Promise<{ data: T | null; error: any }>
  ): Promise<DatabaseResult<T>> {
    try {
      const result = await queryFn();
      
      if (result.error) {
        return {
          success: false,
          data: null,
          error: `${operation} failed: ${result.error.message}`
        };
      }
      
      return {
        success: true,
        data: result.data,
      };
    } catch (error) {
      console.error(`Database operation "${operation}" failed:`, error);
      return {
        success: false,
        data: null,
        error: (error as Error).message
      };
    }
  }
}

// Database health check function
export async function checkDatabaseConnection(): Promise<ConnectionStatus> {
  try {
    const { error } = await supabase
      .from('properties')
      .select('id')
      .limit(1);
    
    if (error) {
      console.error('Database connection check failed:', error);
      return 'error';
    }
    
    return 'connected';
  } catch (error) {
    console.error('Database connection check failed:', error);
    return 'disconnected';
  }
}

// Database utilities class
export class DatabaseUtils {
  /**
   * Check if a table exists in the database
   */
  static async tableExists(tableName: string): Promise<boolean> {
    try {
      // Use a more generic approach to avoid TypeScript strict table name checking
      const { error } = await (supabase as any)
        .from(tableName)
        .select('*')
        .limit(1);
      
      return !error;
    } catch {
      return false;
    }
  }

  /**
   * Get the count of records in a table
   */
  static async getTableCount(tableName: string): Promise<number> {
    try {
      // Use a more generic approach to avoid TypeScript strict table name checking
      const { count, error } = await (supabase as any)
        .from(tableName)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.error(`Error getting count for ${tableName}:`, error);
        return 0;
      }
      
      return count || 0;
    } catch (error) {
      console.error(`Error getting count for ${tableName}:`, error);
      return 0;
    }
  }
}