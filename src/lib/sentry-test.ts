/**
 * Sentry Test Utilities
 * Use these functions to test Sentry integration without overwhelming the service
 */

import * as Sentry from "@sentry/nextjs";

export function testSentryError() {
  console.log("Testing Sentry error capture...");
  
  try {
    throw new Error("Test error from Sentry integration test");
  } catch (error) {
    Sentry.captureException(error, {
      tags: {
        test: true,
        component: "sentry-test"
      },
      contexts: {
        test: {
          type: "integration_test",
          timestamp: new Date().toISOString()
        }
      }
    });
    
    console.log("Test error sent to Sentry");
  }
}

export function testSentryMessage() {
  console.log("Testing Sentry message capture...");
  
  Sentry.captureMessage("Test message from Sentry integration", {
    level: "info",
    tags: {
      test: true,
      component: "sentry-test"
    }
  });
  
  console.log("Test message sent to Sentry");
}

export function testSentryTransaction() {
  console.log("Testing Sentry transaction...");
  
  return Sentry.startSpan(
    {
      op: "test.integration",
      name: "Sentry Integration Test Transaction",
      attributes: {
        test: true,
        timestamp: Date.now()
      }
    },
    async (span) => {
      // Simulate some work
      await new Promise(resolve => setTimeout(resolve, 100));
      
      span?.setAttribute("test.completed", true);
      span?.setAttribute("test.duration", 100);
      
      console.log("Test transaction completed");
      return "success";
    }
  );
}

export function testSentryBreadcrumb() {
  console.log("Testing Sentry breadcrumb...");
  
  Sentry.addBreadcrumb({
    message: "Test breadcrumb from integration test",
    category: "test",
    level: "info",
    data: {
      test: true,
      timestamp: new Date().toISOString()
    }
  });
  
  console.log("Test breadcrumb added");
}

// Comprehensive test function
export async function runSentryIntegrationTest() {
  console.log("🧪 Running Sentry Integration Test...");
  
  try {
    // Test breadcrumb
    testSentryBreadcrumb();
    
    // Wait a bit between tests
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Test message
    testSentryMessage();
    
    // Wait a bit between tests
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Test transaction
    await testSentryTransaction();
    
    // Wait a bit between tests
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Test error (do this last)
    testSentryError();
    
    console.log("✅ Sentry integration test completed successfully!");
    console.log("Check your Sentry dashboard at: https://sentry.io/organizations/atemkeng/projects/proptech/");
    
  } catch (error) {
    console.error("❌ Sentry integration test failed:", error);
    Sentry.captureException(error);
  }
}

// Make it available globally in development
if (process.env.NODE_ENV === "development" && typeof window !== "undefined") {
  (window as any).testSentry = {
    error: testSentryError,
    message: testSentryMessage,
    transaction: testSentryTransaction,
    breadcrumb: testSentryBreadcrumb,
    runAll: runSentryIntegrationTest
  };
  
  console.log("🔧 Sentry test functions available:");
  console.log("- window.testSentry.error()");
  console.log("- window.testSentry.message()");
  console.log("- window.testSentry.transaction()");
  console.log("- window.testSentry.breadcrumb()");
  console.log("- window.testSentry.runAll()");
}