/**
 * Unit tests for Supabase client configuration and utilities
 */

import { describe, it, expect, jest, beforeEach } from '@jest/globals';

// Mock environment configuration
jest.mock('../env', () => ({
  getEnvironmentConfig: jest.fn(() => ({
    supabase: {
      url: 'https://test-project.supabase.co',
      anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRlc3QtcHJvamVjdCIsInJvbGUiOiJhbm9uIiwiaWF0IjoxNjQwOTk1MjAwLCJleHAiOjE5NTY1NzEyMDB9.test-key',
    },
  })),
  isDevelopment: jest.fn(() => true),
}));

// Mock Supabase client
const mockSupabaseClient = {
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      limit: jest.fn(() => Promise.resolve({
        data: [],
        error: null,
      })),
      eq: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({
            data: null,
            error: null,
          })),
        })),
      })),
    })),
  })),
  rpc: jest.fn(() => Promise.resolve({
    data: null,
    error: null,
  })),
};

jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => mockSupabaseClient),
}));

// Import after mocking
import {
  getSupabaseClient,
  checkDatabaseConnection,
  isDatabaseConnected,
  DatabaseService,
  DatabaseUtils,
  initializeSupabase,
  type ConnectionStatus,
  type DatabaseResult,
} from '../supabase';

describe('Supabase Client Configuration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getSupabaseClient', () => {
    it('should create and return a Supabase client instance', () => {
      const client = getSupabaseClient();
      expect(client).toBeDefined();
      expect(client).toBe(mockSupabaseClient);
    });
  });

  describe('checkDatabaseConnection', () => {
    it('should return connected status when database is accessible', async () => {
      mockSupabaseClient.from.mockReturnValue({
        select: jest.fn(() => ({
          limit: jest.fn(() => Promise.resolve({
            data: [],
            error: null,
          })),
        })),
      });

      const status: ConnectionStatus = await checkDatabaseConnection();
      
      expect(status.isConnected).toBe(true);
      expect(status.error).toBeUndefined();
      expect(status.timestamp).toBeInstanceOf(Date);
    });

    it('should return disconnected status when database is not accessible', async () => {
      const mockError = { message: 'Connection failed' };
      mockSupabaseClient.from.mockReturnValue({
        select: jest.fn(() => ({
          limit: jest.fn(() => Promise.resolve({
            data: null,
            error: mockError,
          })),
        })),
      });

      const status: ConnectionStatus = await checkDatabaseConnection();
      
      expect(status.isConnected).toBe(false);
      expect(status.error).toBe('Connection failed');
      expect(status.timestamp).toBeInstanceOf(Date);
    });
  });

  describe('isDatabaseConnected', () => {
    it('should return true when database is connected', async () => {
      mockSupabaseClient.from.mockReturnValue({
        select: jest.fn(() => ({
          limit: jest.fn(() => Promise.resolve({
            data: [],
            error: null,
          })),
        })),
      });

      const isConnected = await isDatabaseConnected();
      expect(isConnected).toBe(true);
    });
  });

  describe('initializeSupabase', () => {
    it('should initialize Supabase and return connection status', async () => {
      mockSupabaseClient.from.mockReturnValue({
        select: jest.fn(() => ({
          limit: jest.fn(() => Promise.resolve({
            data: [],
            error: null,
          })),
        })),
      });

      const status = await initializeSupabase();
      
      expect(status.isConnected).toBe(true);
      expect(status.timestamp).toBeInstanceOf(Date);
    });
  });
});

describe('DatabaseService', () => {
  class TestDatabaseService extends DatabaseService {
    async testExecuteQuery() {
      return this.executeQuery('test operation', async () => ({
        data: { test: 'data' },
        error: null,
      }));
    }

    async testExecuteQuerySafe() {
      return this.executeQuerySafe('test operation', async () => ({
        data: { test: 'data' },
        error: null,
      }));
    }

    async testCheckConnection() {
      return this.checkConnection();
    }
  }

  let service: TestDatabaseService;

  beforeEach(() => {
    jest.clearAllMocks();
    service = new TestDatabaseService();
  });

  describe('executeQuery', () => {
    it('should return data when query succeeds', async () => {
      const result = await service.testExecuteQuery();
      expect(result).toEqual({ test: 'data' });
    });
  });

  describe('executeQuerySafe', () => {
    it('should return success result when query succeeds', async () => {
      const result: DatabaseResult<any> = await service.testExecuteQuerySafe();
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual({ test: 'data' });
      expect(result.error).toBeNull();
    });
  });

  describe('checkConnection', () => {
    it('should return connection status', async () => {
      mockSupabaseClient.from.mockReturnValue({
        select: jest.fn(() => ({
          limit: jest.fn(() => Promise.resolve({
            data: [],
            error: null,
          })),
        })),
      });

      const isConnected = await service.testCheckConnection();
      expect(isConnected).toBe(true);
    });
  });
});

describe('DatabaseUtils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('tableExists', () => {
    it('should return true when table exists', async () => {
      mockSupabaseClient.from.mockReturnValue({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            eq: jest.fn(() => ({
              single: jest.fn(() => Promise.resolve({
                data: { table_name: 'test_table' },
                error: null,
              })),
            })),
          })),
        })),
      });

      const exists = await DatabaseUtils.tableExists('test_table');
      expect(exists).toBe(true);
    });

    it('should return false when table does not exist', async () => {
      mockSupabaseClient.from.mockReturnValue({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            eq: jest.fn(() => ({
              single: jest.fn(() => Promise.resolve({
                data: null,
                error: { message: 'Table not found' },
              })),
            })),
          })),
        })),
      });

      const exists = await DatabaseUtils.tableExists('nonexistent_table');
      expect(exists).toBe(false);
    });
  });

  describe('getTableCount', () => {
    it('should return table count when query succeeds', async () => {
      mockSupabaseClient.from.mockReturnValue({
        select: jest.fn(() => Promise.resolve({
          count: 42,
          error: null,
        })),
      });

      const count = await DatabaseUtils.getTableCount('test_table');
      expect(count).toBe(42);
    });

    it('should return 0 when query fails', async () => {
      mockSupabaseClient.from.mockReturnValue({
        select: jest.fn(() => Promise.resolve({
          count: null,
          error: { message: 'Query failed' },
        })),
      });

      const count = await DatabaseUtils.getTableCount('test_table');
      expect(count).toBe(0);
    });
  });

  describe('executeRawQuery', () => {
    it('should return success result when query succeeds', async () => {
      mockSupabaseClient.rpc.mockResolvedValue({
        data: { result: 'success' },
        error: null,
      });

      const result = await DatabaseUtils.executeRawQuery('SELECT 1');
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual({ result: 'success' });
      expect(result.error).toBeNull();
    });

    it('should return error result when query fails', async () => {
      mockSupabaseClient.rpc.mockResolvedValue({
        data: null,
        error: { message: 'SQL error' },
      });

      const result = await DatabaseUtils.executeRawQuery('INVALID SQL');
      
      expect(result.success).toBe(false);
      expect(result.data).toBeNull();
      expect(result.error).toBe('SQL error');
    });
  });
});