/**
 * Enhanced session tracking tests
 */

import { 
  SessionManager, 
  SessionTrackingError,
  UserInteractionType,
  getSessionManager,
  trackPageView,
  trackUserInteraction,
  setUser,
  initSessionTracking 
} from '../sessionTracking';
import { UniversalErrorWrapper } from '../UniversalErrorWrapper';
import * as Sentry from '@sentry/nextjs';

// Mock dependencies
jest.mock('../UniversalErrorWrapper');
jest.mock('@sentry/nextjs');
jest.mock('../sentry', () => ({
  setUserSessionContext: jest.fn(),
  addUserInteractionBreadcrumb: jest.fn(),
  addBreadcrumb: jest.fn(),
}));

const mockUniversalErrorWrapper = UniversalErrorWrapper as jest.Mocked<typeof UniversalErrorWrapper>;
const mockSentry = Sentry as jest.Mocked<typeof Sentry>;

// Mock storage
const mockSessionStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

describe('Enhanced Session Tracking', () => {
  const originalWindow = global.window;
  const originalDocument = global.document;
  const originalNavigator = global.navigator;

  beforeEach(() => {
    jest.resetAllMocks();
    
    // Mock UniversalErrorWrapper to execute functions directly
    mockUniversalErrorWrapper.wrapSync.mockImplementation((operation, fn) => fn());
    
    // Mock Sentry methods
    mockSentry.addBreadcrumb = jest.fn();
    mockSentry.captureException = jest.fn();

    // Mock window and document
    Object.defineProperty(global, 'window', {
      value: {
        location: {
          href: 'https://example.com/test',
          pathname: '/test',
        },
        addEventListener: jest.fn(),
      },
      writable: true,
    });

    Object.defineProperty(global, 'document', {
      value: {
        title: 'Test Page',
        referrer: 'https://referrer.com',
        addEventListener: jest.fn(),
        visibilityState: 'visible',
      },
      writable: true,
    });

    Object.defineProperty(global, 'navigator', {
      value: {
        userAgent: 'Test User Agent',
      },
      writable: true,
    });

    // Mock sessionStorage
    Object.defineProperty(global, 'sessionStorage', {
      value: mockSessionStorage,
      writable: true,
    });

    // Reset storage mock
    mockSessionStorage.getItem.mockReturnValue(null);
    mockSessionStorage.setItem.mockImplementation(() => {});
    mockSessionStorage.removeItem.mockImplementation(() => {});
  });

  afterEach(() => {
    global.window = originalWindow;
    global.document = originalDocument;
    global.navigator = originalNavigator;
    
    // Clear singleton instance
    (SessionManager as any).instance = undefined;
  });

  describe('SessionManager', () => {
    describe('initialization', () => {
      it('should initialize successfully with all APIs available', () => {
        const manager = getSessionManager();
        const health = manager.getSessionHealth();

        expect(health.initialized).toBe(true);
        expect(health.fallbackMode).toBe(false);
        expect(mockSentry.addBreadcrumb).toHaveBeenCalledWith(
          expect.objectContaining({
            message: 'Session manager initialized successfully',
            category: 'session',
            level: 'info',
          })
        );
      });

      it('should handle server-side environment', () => {
        delete (global as any).window;
        delete (global as any).document;

        const manager = getSessionManager();
        const sessionData = manager.getSessionData();

        expect(sessionData.userAgent).toBe('server');
        expect(sessionData.referrer).toBeUndefined();
      });

      it('should handle sessionStorage not available', () => {
        delete (global as any).sessionStorage;

        const manager = getSessionManager();
        const health = manager.getSessionHealth();

        expect(health.fallbackMode).toBe(true);
        expect(health.storageAvailable).toBe(false);
        expect(mockSentry.addBreadcrumb).toHaveBeenCalledWith(
          expect.objectContaining({
            message: 'Session storage not available, using fallback mode',
            level: 'warning',
          })
        );
      });

      it('should restore existing session if valid', () => {
        const existingSessionId = 'existing-session-123';
        const sessionStart = (Date.now() - 10 * 60 * 1000).toString(); // 10 minutes ago

        mockSessionStorage.getItem.mockImplementation((key) => {
          if (key === 'ja_session_id') return existingSessionId;
          if (key === 'ja_session_start') return sessionStart;
          return null;
        });

        const manager = getSessionManager();
        const sessionData = manager.getSessionData();

        expect(sessionData.sessionId).toBe(existingSessionId);
        expect(sessionData.startTime).toBe(parseInt(sessionStart));
      });

      it('should create new session if existing session is expired', () => {
        const existingSessionId = 'expired-session-123';
        const sessionStart = (Date.now() - 40 * 60 * 1000).toString(); // 40 minutes ago (expired)

        mockSessionStorage.getItem.mockImplementation((key) => {
          if (key === 'ja_session_id') return existingSessionId;
          if (key === 'ja_session_start') return sessionStart;
          return null;
        });

        const manager = getSessionManager();
        const sessionData = manager.getSessionData();

        expect(sessionData.sessionId).not.toBe(existingSessionId);
        expect(mockSentry.addBreadcrumb).toHaveBeenCalledWith(
          expect.objectContaining({
            message: 'Session expired, creating new session',
            level: 'info',
          })
        );
      });

      it('should handle initialization errors gracefully', () => {
        // Mock storage to throw error
        mockSessionStorage.getItem.mockImplementation(() => {
          throw new Error('Storage error');
        });

        const manager = getSessionManager();
        const health = manager.getSessionHealth();

        expect(health.fallbackMode).toBe(true);
        expect(health.errors.length).toBeGreaterThan(0);
        expect(mockSentry.captureException).toHaveBeenCalledWith(
          expect.any(SessionTrackingError),
          expect.objectContaining({
            tags: expect.objectContaining({
              operation: 'session_manager_init',
            }),
          })
        );
      });
    });

    describe('trackPageView', () => {
      it('should track page view successfully', () => {
        const manager = getSessionManager();
        
        manager.trackPageView('/test-page', 'Test Page', 'test-section');
        
        const sessionData = manager.getSessionData();
        expect(sessionData.pageViews).toHaveLength(1);
        expect(sessionData.pageViews[0]).toEqual(
          expect.objectContaining({
            url: '/test-page',
            title: 'Test Page',
            section: 'test-section',
          })
        );
      });

      it('should use current page data when parameters not provided', () => {
        const manager = getSessionManager();
        
        manager.trackPageView();
        
        const sessionData = manager.getSessionData();
        expect(sessionData.pageViews[0]).toEqual(
          expect.objectContaining({
            url: 'https://example.com/test',
            title: 'Test Page',
            section: '/test',
          })
        );
      });

      it('should end previous page view when tracking new one', () => {
        const manager = getSessionManager();
        
        // Track first page view
        manager.trackPageView('/page1', 'Page 1');
        
        // Mock time passage
        const originalNow = Date.now;
        Date.now = jest.fn().mockReturnValue(originalNow() + 5000);
        
        // Track second page view
        manager.trackPageView('/page2', 'Page 2');
        
        const sessionData = manager.getSessionData();
        expect(sessionData.pageViews).toHaveLength(2);
        expect(sessionData.pageViews[0].timeOnPage).toBe(5000);
        
        Date.now = originalNow;
      });

      it('should handle page view tracking errors gracefully', () => {
        const manager = getSessionManager();
        
        // Mock storage to fail
        mockSessionStorage.setItem.mockImplementation(() => {
          throw new Error('Storage quota exceeded');
        });

        // Should not throw
        manager.trackPageView('/test-page');
        
        const sessionData = manager.getSessionData();
        expect(sessionData.pageViews).toHaveLength(1);
      });
    });

    describe('trackUserInteraction', () => {
      it('should track user interaction successfully', () => {
        const manager = getSessionManager();
        
        manager.trackUserInteraction(
          UserInteractionType.CLICK,
          'button.submit',
          { buttonText: 'Submit' }
        );
        
        const sessionData = manager.getSessionData();
        expect(sessionData.userInteractions).toHaveLength(1);
        expect(sessionData.userInteractions[0]).toEqual(
          expect.objectContaining({
            type: UserInteractionType.CLICK,
            element: 'button.submit',
            page: '/test',
            data: { buttonText: 'Submit' },
          })
        );
      });

      it('should validate interaction parameters', () => {
        const manager = getSessionManager();
        
        expect(() => manager.trackUserInteraction('' as any, 'element'))
          .toThrow(SessionTrackingError);
        
        expect(() => manager.trackUserInteraction(UserInteractionType.CLICK, ''))
          .toThrow(SessionTrackingError);
      });

      it('should sanitize interaction data', () => {
        const manager = getSessionManager();
        
        const sensitiveData = {
          password: 'secret123',
          token: 'auth-token',
          normalData: 'safe-value',
          longString: 'a'.repeat(300), // Long string
        };
        
        manager.trackUserInteraction(
          UserInteractionType.FORM_SUBMIT,
          'form.login',
          sensitiveData
        );
        
        const sessionData = manager.getSessionData();
        const interaction = sessionData.userInteractions[0];
        
        expect(interaction.data).not.toHaveProperty('password');
        expect(interaction.data).not.toHaveProperty('token');
        expect(interaction.data).toHaveProperty('normalData', 'safe-value');
        expect(interaction.data?.longString).toHaveLength(203); // 200 + '...'
      });

      it('should limit interactions to 100', () => {
        const manager = getSessionManager();
        
        // Add 150 interactions
        for (let i = 0; i < 150; i++) {
          manager.trackUserInteraction(
            UserInteractionType.CLICK,
            `element-${i}`
          );
        }
        
        const sessionData = manager.getSessionData();
        expect(sessionData.userInteractions).toHaveLength(100);
        expect(sessionData.userInteractions[0].element).toBe('element-50'); // First 50 removed
      });

      it('should handle interaction tracking errors gracefully', () => {
        const manager = getSessionManager();
        
        // Mock storage to fail
        mockSessionStorage.setItem.mockImplementation(() => {
          throw new Error('Storage error');
        });

        // Should not throw
        manager.trackUserInteraction(UserInteractionType.CLICK, 'button');
        
        const sessionData = manager.getSessionData();
        expect(sessionData.userInteractions).toHaveLength(1);
      });
    });

    describe('setUser', () => {
      it('should set user information successfully', () => {
        const manager = getSessionManager();
        
        manager.setUser('user123', '<EMAIL>', 'admin');
        
        const sessionData = manager.getSessionData();
        expect(sessionData.userId).toBe('user123');
        expect(sessionData.userEmail).toBe('<EMAIL>');
        expect(sessionData.userRole).toBe('admin');
      });
    });

    describe('clearSession', () => {
      it('should clear session successfully', () => {
        const manager = getSessionManager();
        const originalSessionId = manager.getSessionData().sessionId;
        
        // Add some data
        manager.trackPageView('/test');
        manager.trackUserInteraction(UserInteractionType.CLICK, 'button');
        
        manager.clearSession();
        
        const sessionData = manager.getSessionData();
        expect(sessionData.sessionId).not.toBe(originalSessionId);
        expect(sessionData.pageViews).toHaveLength(0);
        expect(sessionData.userInteractions).toHaveLength(0);
        expect(mockSessionStorage.removeItem).toHaveBeenCalled();
      });

      it('should handle clear errors gracefully', () => {
        const manager = getSessionManager();
        
        // Mock storage to fail
        mockSessionStorage.removeItem.mockImplementation(() => {
          throw new Error('Remove failed');
        });

        // Should not throw
        manager.clearSession();
        
        expect(mockSentry.captureException).toHaveBeenCalledWith(
          expect.any(SessionTrackingError),
          expect.objectContaining({
            tags: expect.objectContaining({
              operation: 'session_clear',
            }),
          })
        );
      });
    });

    describe('storage quota handling', () => {
      it('should handle storage quota exceeded error', () => {
        const manager = getSessionManager();
        
        // Mock quota exceeded error
        const quotaError = new Error('QuotaExceededError');
        quotaError.name = 'QuotaExceededError';
        mockSessionStorage.setItem.mockImplementation(() => {
          throw quotaError;
        });

        // Should not throw and should switch to fallback mode
        manager.trackUserInteraction(UserInteractionType.CLICK, 'button');
        
        const health = manager.getSessionHealth();
        expect(health.fallbackMode).toBe(true);
        expect(mockSentry.addBreadcrumb).toHaveBeenCalledWith(
          expect.objectContaining({
            message: 'Session storage quota exceeded, clearing old data',
            level: 'warning',
          })
        );
      });
    });

    describe('getSessionHealth', () => {
      it('should return session health status', () => {
        const manager = getSessionManager();
        const health = manager.getSessionHealth();

        expect(health).toEqual({
          isHealthy: expect.any(Boolean),
          initialized: expect.any(Boolean),
          fallbackMode: expect.any(Boolean),
          errors: expect.any(Array),
          storageAvailable: expect.any(Boolean),
          sessionAge: expect.any(Number),
          interactionCount: expect.any(Number),
        });
      });
    });
  });

  describe('Utility Functions', () => {
    describe('trackPageView', () => {
      it('should track page view using session manager', () => {
        trackPageView('/test', 'Test', 'section');
        
        const manager = getSessionManager();
        const sessionData = manager.getSessionData();
        expect(sessionData.pageViews).toHaveLength(1);
      });
    });

    describe('trackUserInteraction', () => {
      it('should track interaction using session manager', () => {
        trackUserInteraction(UserInteractionType.CLICK, 'button', { test: 'data' });
        
        const manager = getSessionManager();
        const sessionData = manager.getSessionData();
        expect(sessionData.userInteractions).toHaveLength(1);
      });
    });

    describe('setUser', () => {
      it('should set user using session manager', () => {
        setUser('user123', '<EMAIL>', 'admin');
        
        const manager = getSessionManager();
        const sessionData = manager.getSessionData();
        expect(sessionData.userId).toBe('user123');
      });
    });

    describe('initSessionTracking', () => {
      it('should initialize session tracking successfully', () => {
        initSessionTracking();
        
        expect(mockSentry.addBreadcrumb).toHaveBeenCalledWith(
          expect.objectContaining({
            message: 'Session tracking initialized',
            level: 'info',
          })
        );
      });

      it('should handle server-side environment', () => {
        delete (global as any).window;
        
        initSessionTracking();
        
        expect(mockSentry.addBreadcrumb).toHaveBeenCalledWith(
          expect.objectContaining({
            message: 'Session tracking initialization skipped: server environment',
            level: 'debug',
          })
        );
      });

      it('should handle initialization failure gracefully', () => {
        // Mock getSessionManager to throw
        jest.doMock('../sessionTracking', () => ({
          ...jest.requireActual('../sessionTracking'),
          getSessionManager: () => {
            throw new Error('Manager creation failed');
          },
        }));

        initSessionTracking();
        
        expect(mockSentry.captureException).toHaveBeenCalledWith(
          expect.any(SessionTrackingError),
          expect.objectContaining({
            tags: expect.objectContaining({
              operation: 'session_tracking_init',
            }),
          })
        );
      });
    });
  });

  describe('SessionTrackingError', () => {
    it('should create error with operation and cause', () => {
      const cause = new Error('Original error');
      const error = new SessionTrackingError(
        'Test error',
        'test_operation',
        cause
      );

      expect(error.name).toBe('SessionTrackingError');
      expect(error.message).toBe('Test error');
      expect(error.operation).toBe('test_operation');
      expect(error.cause).toBe(cause);
    });
  });
});