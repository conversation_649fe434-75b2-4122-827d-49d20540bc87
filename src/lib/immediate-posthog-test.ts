/**
 * Immediate PostHog test - can be run directly in console
 * This bypasses all initialization systems and tests PostHog directly
 */

declare global {
  interface Window {
    immediatePostHogTest: () => void;
    testPostHogNow: () => void;
  }
}

function immediatePostHogTest() {
  console.log('🧪 Starting immediate PostHog test...');
  
  // Import PostHog directly
  import('posthog-js').then((posthogModule) => {
    const posthog = posthogModule.default;
    
    console.log('📦 PostHog module loaded');
    
    // Get environment variables
    const apiKey = process.env.NEXT_PUBLIC_POSTHOG_API_KEY;
    const apiHost = process.env.NEXT_PUBLIC_POSTHOG_API_HOST || 'https://us.i.posthog.com';
    
    console.log('🔑 API Key:', apiKey ? apiKey.substring(0, 10) + '...' : 'MISSING');
    console.log('🌐 API Host:', apiHost);
    
    if (!apiKey) {
      console.error('❌ No API key found');
      return;
    }
    
    // Initialize PostHog with minimal config
    console.log('🚀 Initializing PostHog...');
    
    posthog.init(apiKey, {
      api_host: apiHost,
      debug: true,
      opt_out_capturing_by_default: false,
      request_batching: false,
      
      loaded: (instance) => {
        console.log('✅ PostHog loaded!');
        console.log('- Distinct ID:', instance.get_distinct_id());
        console.log('- Opted out:', instance.has_opted_out_capturing());
        
        // Send immediate test event
        const testEvent = {
          timestamp: new Date().toISOString(),
          test_type: 'immediate',
          random_id: Math.random().toString(36).substring(7)
        };
        
        console.log('📤 Sending test event:', testEvent);
        instance.capture('immediate_test_event', testEvent);
        
        console.log('✅ Test event sent! Check your PostHog dashboard.');
      },
      
      before_send: (event) => {
        console.log('📤 PostHog sending event:', event);
        return event;
      }
    });
  }).catch(error => {
    console.error('❌ Failed to load PostHog:', error);
  });
}

function testPostHogNow() {
  console.log('🧪 Testing PostHog now...');
  
  // Check if PostHog is already available
  const posthog = (window as any).posthog;
  
  if (!posthog) {
    console.error('❌ PostHog not found on window object');
    console.log('🔄 Trying to initialize...');
    immediatePostHogTest();
    return;
  }
  
  if (!posthog.__loaded) {
    console.error('❌ PostHog not loaded');
    return;
  }
  
  console.log('✅ PostHog found and loaded');
  console.log('📊 Status:', {
    distinctId: posthog.get_distinct_id?.(),
    hasOptedOut: posthog.has_opted_out_capturing?.(),
    apiHost: posthog.config?.api_host,
    apiKey: posthog.config?.token?.substring(0, 10) + '...'
  });
  
  // Force opt-in if needed
  if (posthog.has_opted_out_capturing?.()) {
    console.log('📊 Opting in...');
    posthog.opt_in_capturing();
  }
  
  // Send test event
  const testEvent = {
    timestamp: new Date().toISOString(),
    test_type: 'direct_now',
    random_id: Math.random().toString(36).substring(7)
  };
  
  console.log('📤 Sending test event:', testEvent);
  posthog.capture('test_now_event', testEvent);
  
  console.log('✅ Test event sent! Check your PostHog dashboard.');
}

// Make functions available immediately
if (typeof window !== 'undefined') {
  window.immediatePostHogTest = immediatePostHogTest;
  window.testPostHogNow = testPostHogNow;
  
  console.log('🛠️ Immediate PostHog test functions loaded:');
  console.log('- window.immediatePostHogTest() - Initialize and test PostHog');
  console.log('- window.testPostHogNow() - Test existing PostHog instance');
}

export { immediatePostHogTest, testPostHogNow };