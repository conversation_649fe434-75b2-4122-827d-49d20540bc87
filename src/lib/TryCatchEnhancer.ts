import { reportError, reportBusinessError, addBreadcrumb, type ErrorContext, type BusinessError } from './sentry';
import { universalErrorWrapper } from './UniversalErrorWrapper';

/**
 * Enhanced try-catch wrapper utility for standardizing error handling across the application
 * This utility provides consistent Sentry integration, error classification, and context enrichment
 */

export interface TryCatchConfig {
  operation: string;
  component?: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  category?: 'validation' | 'network' | 'database' | 'auth' | 'business' | 'external' | 'ui' | 'performance';
  context?: ErrorContext;
  businessContext?: {
    entityType?: string;
    entityId?: string;
    workflow?: string;
  };
  skipSentryCapture?: boolean;
  fallbackValue?: any;
  onError?: (error: Error) => void;
}

export class TryCatchEnhancer {
  /**
   * Enhanced synchronous try-catch wrapper with comprehensive error handling
   */
  static wrapSync<T>(
    fn: () => T,
    config: TryCatchConfig
  ): T | undefined {
    try {
      // Add operation breadcrumb
      addBreadcrumb(
        `Starting operation: ${config.operation}`,
        config.category || 'operation',
        'info',
        {
          operation: config.operation,
          component: config.component,
          type: 'sync'
        }
      );

      const result = fn();

      // Add success breadcrumb
      addBreadcrumb(
        `Operation completed: ${config.operation}`,
        config.category || 'operation',
        'info',
        {
          operation: config.operation,
          component: config.component,
          result: 'success'
        }
      );

      return result;
    } catch (error) {
      return this.handleError(error as Error, config);
    }
  }

  /**
   * Enhanced asynchronous try-catch wrapper with comprehensive error handling
   */
  static async wrapAsync<T>(
    fn: () => Promise<T>,
    config: TryCatchConfig
  ): Promise<T | undefined> {
    try {
      // Add operation breadcrumb
      addBreadcrumb(
        `Starting async operation: ${config.operation}`,
        config.category || 'operation',
        'info',
        {
          operation: config.operation,
          component: config.component,
          type: 'async'
        }
      );

      const result = await fn();

      // Add success breadcrumb
      addBreadcrumb(
        `Async operation completed: ${config.operation}`,
        config.category || 'operation',
        'info',
        {
          operation: config.operation,
          component: config.component,
          result: 'success'
        }
      );

      return result;
    } catch (error) {
      return this.handleError(error as Error, config);
    }
  }

  /**
   * Enhanced error handling with automatic classification and context enrichment
   */
  private static handleError<T>(
    error: Error,
    config: TryCatchConfig
  ): T | undefined {
    // Classify error severity automatically if not provided
    const severity = config.severity || this.classifyErrorSeverity(error, config.category);
    
    // Create enhanced error context
    const enhancedContext: ErrorContext = {
      ...config.context,
      page: {
        url: typeof window !== 'undefined' ? window.location.href : 'server',
        section: config.component || 'unknown',
        component: config.component,
        ...config.context?.page
      },
      business: {
        operation: config.operation,
        entityType: config.businessContext?.entityType,
        entityId: config.businessContext?.entityId,
        workflow: config.businessContext?.workflow,
        ...config.context?.business
      },
      browser: {
        name: typeof window !== 'undefined' ? navigator.userAgent : 'server',
        version: 'unknown',
        userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server',
        ...config.context?.browser
      },
      technical: {
        requestId: `error_${Date.now()}`,
        ...config.context?.technical
      }
    };

    // Add error breadcrumb
    addBreadcrumb(
      `Operation failed: ${config.operation}`,
      config.category || 'error',
      severity === 'critical' ? 'fatal' : 
      severity === 'high' ? 'error' :
      severity === 'medium' ? 'warning' : 'info',
      {
        operation: config.operation,
        component: config.component,
        errorType: error.name,
        errorMessage: error.message,
        category: config.category
      }
    );

    // Record user journey step for error tracking
    universalErrorWrapper.recordUserJourneyStep(`${config.operation}_error`);

    // Report to Sentry unless explicitly skipped
    if (!config.skipSentryCapture) {
      if (this.isBusinessError(error, config.category)) {
        const businessError: BusinessError = {
          code: this.generateErrorCode(config.operation, error.name),
          message: error.message,
          severity,
          category: (config.category === 'performance' || config.category === 'ui') ? 'business' : (config.category || 'business'),
          context: {
            operation: config.operation,
            component: config.component,
            originalError: error.name,
            ...config.businessContext
          }
        };
        reportBusinessError(businessError, enhancedContext);
      } else {
        reportError(error, enhancedContext);
      }
    }

    // Call custom error handler if provided
    if (config.onError) {
      try {
        config.onError(error);
      } catch (handlerError) {
        // Don't let error handler failures break the flow
        console.warn('Error handler failed:', handlerError);
      }
    }

    // Return fallback value if provided, otherwise re-throw
    if (config.fallbackValue !== undefined) {
      return config.fallbackValue;
    }

    throw error;
  }

  /**
   * Automatically classify error severity based on error type and category
   */
  private static classifyErrorSeverity(
    error: Error,
    category?: string
  ): 'low' | 'medium' | 'high' | 'critical' {
    // Critical errors that require immediate attention
    if (
      error.name === 'TypeError' ||
      error.name === 'ReferenceError' ||
      error.message.includes('Cannot read property') ||
      error.message.includes('is not a function') ||
      category === 'database' && error.message.includes('connection')
    ) {
      return 'critical';
    }

    // High severity errors that impact functionality
    if (
      error.name === 'NetworkError' ||
      error.name === 'DatabaseError' ||
      error.name === 'AuthenticationError' ||
      category === 'auth' ||
      category === 'database' ||
      error.message.includes('timeout') ||
      error.message.includes('failed to fetch')
    ) {
      return 'high';
    }

    // Medium severity errors that may impact user experience
    if (
      error.name === 'ValidationError' ||
      category === 'validation' ||
      category === 'business' ||
      error.message.includes('invalid') ||
      error.message.includes('required')
    ) {
      return 'medium';
    }

    // Low severity errors for minor issues
    return 'low';
  }

  /**
   * Determine if an error should be treated as a business error
   */
  private static isBusinessError(error: Error, category?: string): boolean {
    return (
      category === 'business' ||
      category === 'validation' ||
      error.name === 'ValidationError' ||
      error.name === 'BusinessError' ||
      error.message.includes('business rule') ||
      error.message.includes('validation failed')
    );
  }

  /**
   * Generate a standardized error code for business errors
   */
  private static generateErrorCode(operation: string, errorName: string): string {
    const operationCode = operation.toUpperCase().replace(/[^A-Z0-9]/g, '_');
    const errorCode = errorName.toUpperCase().replace(/[^A-Z0-9]/g, '_');
    return `${operationCode}_${errorCode}`;
  }

  /**
   * Convenience method for wrapping database operations
   */
  static wrapDatabaseOperation<T>(
    fn: () => Promise<T>,
    operation: string,
    tableName?: string,
    context?: Partial<ErrorContext>
  ): Promise<T | undefined> {
    return this.wrapAsync(fn, {
      operation: `database.${operation}`,
      category: 'database',
      severity: 'high',
      businessContext: {
        entityType: 'database',
        entityId: tableName
      },
      context: {
        ...context,
        page: context?.page || {
          url: typeof window !== 'undefined' ? window.location.href : 'server',
          section: 'database',
          component: 'database_operation'
        },
        browser: context?.browser || {
          name: typeof window !== 'undefined' ? navigator.userAgent : 'server',
          version: 'unknown',
          userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server'
        },
        technical: {
          ...context?.technical,
          requestId: `db_${operation}_${Date.now()}`
        }
      }
    });
  }

  /**
   * Convenience method for wrapping API operations
   */
  static wrapAPIOperation<T>(
    fn: () => Promise<T>,
    endpoint: string,
    method: string = 'GET',
    context?: Partial<ErrorContext>
  ): Promise<T | undefined> {
    return this.wrapAsync(fn, {
      operation: `api.${method.toLowerCase()}.${endpoint}`,
      category: 'network',
      severity: 'high',
      businessContext: {
        entityType: 'api_endpoint',
        entityId: endpoint
      },
      context: {
        ...context,
        page: context?.page || {
          url: typeof window !== 'undefined' ? window.location.href : 'server',
          section: 'api',
          component: 'api_operation'
        },
        browser: context?.browser || {
          name: typeof window !== 'undefined' ? navigator.userAgent : 'server',
          version: 'unknown',
          userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server'
        },
        technical: {
          ...context?.technical,
          apiEndpoint: endpoint,
          requestId: `api_${method}_${Date.now()}`
        }
      }
    });
  }

  /**
   * Convenience method for wrapping component operations
   */
  static wrapComponentOperation<T>(
    fn: () => T,
    componentName: string,
    operation: string,
    context?: Partial<ErrorContext>
  ): T | undefined {
    return this.wrapSync(fn, {
      operation: `component.${componentName}.${operation}`,
      component: componentName,
      category: 'ui',
      severity: 'medium',
      businessContext: {
        entityType: 'ui_component',
        entityId: componentName
      },
      context: {
        ...context,
        page: context?.page || {
          url: typeof window !== 'undefined' ? window.location.href : 'server',
          section: 'component',
          component: componentName
        },
        browser: context?.browser || {
          name: typeof window !== 'undefined' ? navigator.userAgent : 'server',
          version: 'unknown',
          userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server'
        }
      }
    });
  }

  /**
   * Convenience method for wrapping validation operations
   */
  static wrapValidation<T>(
    fn: () => T,
    validationType: string,
    fieldName?: string,
    context?: Partial<ErrorContext>
  ): T | undefined {
    return this.wrapSync(fn, {
      operation: `validation.${validationType}`,
      category: 'validation',
      severity: 'medium',
      businessContext: {
        entityType: 'validation',
        entityId: fieldName
      },
      context: {
        ...context,
        page: context?.page || {
          url: typeof window !== 'undefined' ? window.location.href : 'server',
          section: 'validation',
          component: 'validation_operation'
        },
        browser: context?.browser || {
          name: typeof window !== 'undefined' ? navigator.userAgent : 'server',
          version: 'unknown',
          userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server'
        },
        technical: {
          ...context?.technical,
          requestId: `validation_${validationType}_${Date.now()}`
        }
      }
    });
  }
}

// Export convenience functions for easier usage
export const wrapSync = TryCatchEnhancer.wrapSync;
export const wrapAsync = TryCatchEnhancer.wrapAsync;
export const wrapDatabaseOperation = TryCatchEnhancer.wrapDatabaseOperation;
export const wrapAPIOperation = TryCatchEnhancer.wrapAPIOperation;
export const wrapComponentOperation = TryCatchEnhancer.wrapComponentOperation;
export const wrapValidation = TryCatchEnhancer.wrapValidation;