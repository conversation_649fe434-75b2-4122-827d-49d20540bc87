/**
 * Custom hook for tracking scroll depth and engagement
 */

import { useEffect, useRef, useCallback } from 'react';
import { trackTimeOnSection } from '../../components/CleanPostHogProvider';
import posthog from 'posthog-js';

interface ScrollTrackingOptions {
  sectionName: string;
  trackDepth?: boolean;
  trackTime?: boolean;
  depthThresholds?: number[];
}

export function useScrollTracking({
  sectionName,
  trackDepth = true,
  trackTime = true,
  depthThresholds = [25, 50, 75, 100],
}: ScrollTrackingOptions) {
  const sectionRef = useRef<HTMLElement>(null);
  const startTimeRef = useRef<number>(0);
  const trackedDepthsRef = useRef<Set<number>>(new Set());
  const isVisibleRef = useRef<boolean>(false);

  const handleScroll = useCallback(() => {
    if (!sectionRef.current || !trackDepth) return;

    const element = sectionRef.current;
    const rect = element.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    const elementHeight = rect.height;

    // Check if element is in viewport
    const isVisible = rect.top < windowHeight && rect.bottom > 0;

    if (isVisible) {
      // Calculate scroll depth within the element
      const visibleTop = Math.max(0, -rect.top);
      const visibleBottom = Math.min(elementHeight, windowHeight - rect.top);
      const visibleHeight = Math.max(0, visibleBottom - visibleTop);
      const scrollDepth = Math.round((visibleHeight / elementHeight) * 100);

      // Track depth milestones
      depthThresholds.forEach((threshold) => {
        if (scrollDepth >= threshold && !trackedDepthsRef.current.has(threshold)) {
          trackedDepthsRef.current.add(threshold);
          // Track scroll depth with clean PostHog
          if (typeof window !== 'undefined' && posthog.__loaded) {
            console.log('📊 Tracking scroll depth:', { threshold, sectionName })
            posthog.capture('scroll_depth', {
              depth_percentage: threshold,
              section: sectionName,
              timestamp: new Date().toISOString()
            })
          }
        }
      });

      // Start timing if not already started
      if (!isVisibleRef.current && trackTime) {
        startTimeRef.current = Date.now();
        isVisibleRef.current = true;
      }
    } else if (isVisibleRef.current && trackTime) {
      // Element left viewport, track time spent
      const timeSpent = Math.round((Date.now() - startTimeRef.current) / 1000);
      if (timeSpent > 0) {
        trackTimeOnSection(sectionName, timeSpent);
      }
      isVisibleRef.current = false;
    }
  }, [sectionName, trackDepth, trackTime, depthThresholds]);

  useEffect(() => {
    if (!trackDepth && !trackTime) return;

    const throttledScroll = throttle(handleScroll, 100);
    window.addEventListener('scroll', throttledScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', throttledScroll);
      
      // Track final time if still visible
      if (isVisibleRef.current && trackTime && startTimeRef.current > 0) {
        const timeSpent = Math.round((Date.now() - startTimeRef.current) / 1000);
        if (timeSpent > 0) {
          trackTimeOnSection(sectionName, timeSpent);
        }
      }
    };
  }, [handleScroll, trackDepth, trackTime]);

  return sectionRef;
}

/**
 * Throttle function to limit scroll event frequency
 */
function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return function (this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}