/**
 * React hook for monitoring initialization and health checking
 */

import { useEffect, useState, useCallback } from 'react';
import { 
  initializeMonitoring, 
  checkMonitoringHealth, 
  getMonitoringConfig,
  getLastMonitoringHealthCheck,
  isMonitoringInitialized,
  reportMonitoringIssue,
  type MonitoringConfig,
  type MonitoringHealthStatus 
} from '../monitoring';
import { isDevelopment } from '../env';

export interface UseMonitoringReturn {
  isInitialized: boolean;
  isInitializing: boolean;
  config: MonitoringConfig | null;
  healthStatus: MonitoringHealthStatus | null;
  error: string | null;
  initialize: () => Promise<void>;
  checkHealth: () => Promise<void>;
  reportIssue: (service: 'sentry' | 'posthog' | 'performance', issue: string, severity?: 'low' | 'medium' | 'high') => void;
}

/**
 * Hook for managing monitoring services initialization and health
 */
export function useMonitoring(autoInitialize = true): UseMonitoringReturn {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [config, setConfig] = useState<MonitoringConfig | null>(null);
  const [healthStatus, setHealthStatus] = useState<MonitoringHealthStatus | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Initialize monitoring services
  const initialize = useCallback(async () => {
    if (isInitializing || isInitialized) {
      return;
    }

    setIsInitializing(true);
    setError(null);

    try {
      const success = await initializeMonitoring();
      
      if (success) {
        setIsInitialized(true);
        setConfig(getMonitoringConfig());
        
        // Perform initial health check
        const health = await checkMonitoringHealth();
        setHealthStatus(health);
        
        if (isDevelopment()) {
          console.log('📊 Monitoring initialized successfully:', {
            config: getMonitoringConfig(),
            health
          });
        }
      } else {
        setError('Failed to initialize monitoring services');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Failed to initialize monitoring:', err);
    } finally {
      setIsInitializing(false);
    }
  }, [isInitializing, isInitialized]);

  // Check monitoring health
  const checkHealth = useCallback(async () => {
    try {
      const health = await checkMonitoringHealth();
      setHealthStatus(health);
      
      if (isDevelopment()) {
        console.log('📊 Monitoring health check:', health);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Health check failed';
      console.error('Monitoring health check failed:', err);
      
      // Update health status to reflect the error
      setHealthStatus({
        overall: 'unhealthy',
        services: {
          sentry: { status: 'down', configured: false, error: errorMessage },
          posthog: { status: 'down', initialized: false, healthy: false, error: errorMessage },
          performance: { status: 'down', webVitals: false, observer: false, error: errorMessage },
        },
        lastChecked: new Date().toISOString(),
      });
    }
  }, []);

  // Report monitoring issues
  const reportIssue = useCallback((
    service: 'sentry' | 'posthog' | 'performance', 
    issue: string, 
    severity: 'low' | 'medium' | 'high' = 'medium'
  ) => {
    reportMonitoringIssue(service, issue, severity);
  }, []);

  // Auto-initialize on mount
  useEffect(() => {
    if (autoInitialize && !isMonitoringInitialized() && !isInitializing) {
      initialize();
    }
  }, [autoInitialize, initialize, isInitializing]);

  // Periodic health checks (every 5 minutes in production, 30 seconds in development)
  useEffect(() => {
    if (!isInitialized) return;

    const interval = isDevelopment() ? 30000 : 300000; // 30s dev, 5min prod
    
    const healthCheckInterval = setInterval(() => {
      checkHealth();
    }, interval);

    return () => clearInterval(healthCheckInterval);
  }, [isInitialized, checkHealth]);

  // Update state when monitoring is already initialized (e.g., SSR)
  useEffect(() => {
    if (isMonitoringInitialized() && !isInitialized) {
      setIsInitialized(true);
      setConfig(getMonitoringConfig());
      
      const lastHealth = getLastMonitoringHealthCheck();
      if (lastHealth) {
        setHealthStatus(lastHealth);
      }
    }
  }, [isInitialized]);

  return {
    isInitialized,
    isInitializing,
    config,
    healthStatus,
    error,
    initialize,
    checkHealth,
    reportIssue,
  };
}

/**
 * Hook for monitoring a specific service health
 */
export function useServiceHealth(service: 'sentry' | 'posthog' | 'performance') {
  const { healthStatus, checkHealth } = useMonitoring(false);
  
  const serviceHealth = healthStatus?.services[service];
  
  return {
    status: serviceHealth?.status || 'down',
    isHealthy: serviceHealth?.status === 'up',
    isDegraded: serviceHealth?.status === 'degraded',
    isDown: serviceHealth?.status === 'down',
    error: serviceHealth?.error,
    lastChecked: healthStatus?.lastChecked,
    checkHealth,
  };
}

/**
 * Hook for monitoring overall system health
 */
export function useSystemHealth() {
  const { healthStatus, checkHealth } = useMonitoring(false);
  
  return {
    overall: healthStatus?.overall || 'unhealthy',
    isHealthy: healthStatus?.overall === 'healthy',
    isDegraded: healthStatus?.overall === 'degraded',
    isUnhealthy: healthStatus?.overall === 'unhealthy',
    services: healthStatus?.services,
    lastChecked: healthStatus?.lastChecked,
    checkHealth,
  };
}

/**
 * Hook for development monitoring dashboard
 */
export function useMonitoringDashboard() {
  const monitoring = useMonitoring();
  const [autoRefresh, setAutoRefresh] = useState(isDevelopment());
  
  // Auto-refresh in development
  useEffect(() => {
    if (!autoRefresh || !monitoring.isInitialized) return;
    
    const interval = setInterval(() => {
      monitoring.checkHealth();
    }, 10000); // 10 seconds
    
    return () => clearInterval(interval);
  }, [autoRefresh, monitoring.isInitialized, monitoring.checkHealth]);
  
  return {
    ...monitoring,
    autoRefresh,
    setAutoRefresh,
    toggleAutoRefresh: () => setAutoRefresh(prev => !prev),
  };
}