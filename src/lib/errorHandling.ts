/**
 * Enhanced error handling utilities for business logic and application errors
 */

import { 
  reportError, 
  reportBusinessError, 
  addBusinessBreadcrumb, 
  addAPIBreadcrumb,
  type BusinessError,
  type ErrorContext 
} from './sentry';

// Error categories for business operations
export enum ErrorCategory {
  VALIDATION = 'validation',
  NETWORK = 'network',
  DATABASE = 'database',
  AUTH = 'auth',
  BUSINESS = 'business',
  EXTERNAL = 'external',
  PERFORMANCE = 'performance',
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// Business operation types
export enum BusinessOperation {
  CONTACT_FORM_SUBMIT = 'contact_form_submit',
  PROPERTY_SEARCH = 'property_search',
  BOOKING_CREATE = 'booking_create',
  BOOKING_UPDATE = 'booking_update',
  CALENDAR_SYNC = 'calendar_sync',
  CONTENT_CREATE = 'content_create',
  CONTENT_UPDATE = 'content_update',
  USER_AUTH = 'user_auth',
  DATA_FETCH = 'data_fetch',
  DATA_SAVE = 'data_save',
}

// Custom error classes for different business scenarios
export class ValidationError extends Error {
  public readonly code: string;
  public readonly field?: string;
  public readonly value?: any;

  constructor(message: string, field?: string, value?: any) {
    super(message);
    this.name = 'ValidationError';
    this.code = 'VALIDATION_ERROR';
    this.field = field;
    this.value = value;
  }
}

export class NetworkError extends Error {
  public readonly code: string;
  public readonly statusCode?: number;
  public readonly endpoint?: string;

  constructor(message: string, statusCode?: number, endpoint?: string) {
    super(message);
    this.name = 'NetworkError';
    this.code = 'NETWORK_ERROR';
    this.statusCode = statusCode;
    this.endpoint = endpoint;
  }
}

export class DatabaseError extends Error {
  public readonly code: string;
  public readonly operation?: string;
  public readonly table?: string;

  constructor(message: string, operation?: string, table?: string) {
    super(message);
    this.name = 'DatabaseError';
    this.code = 'DATABASE_ERROR';
    this.operation = operation;
    this.table = table;
  }
}

export class BusinessLogicError extends Error {
  public readonly code: string;
  public readonly operation: BusinessOperation;
  public readonly entityId?: string;
  public readonly entityType?: string;

  constructor(
    message: string, 
    code: string, 
    operation: BusinessOperation,
    entityId?: string,
    entityType?: string
  ) {
    super(message);
    this.name = 'BusinessLogicError';
    this.code = code;
    this.operation = operation;
    this.entityId = entityId;
    this.entityType = entityType;
  }
}

// Error handler for business operations
export class BusinessErrorHandler {
  private static getBrowserInfo() {
    if (typeof window === 'undefined') return { name: 'server', version: 'unknown', userAgent: 'server' };
    
    const userAgent = navigator.userAgent;
    let browserName = 'unknown';
    let browserVersion = 'unknown';

    if (userAgent.includes('Chrome')) {
      browserName = 'Chrome';
      const match = userAgent.match(/Chrome\/(\d+)/);
      browserVersion = match ? match[1] : 'unknown';
    } else if (userAgent.includes('Firefox')) {
      browserName = 'Firefox';
      const match = userAgent.match(/Firefox\/(\d+)/);
      browserVersion = match ? match[1] : 'unknown';
    } else if (userAgent.includes('Safari')) {
      browserName = 'Safari';
      const match = userAgent.match(/Version\/(\d+)/);
      browserVersion = match ? match[1] : 'unknown';
    }

    return { name: browserName, version: browserVersion, userAgent };
  }

  private static getPageInfo() {
    if (typeof window === 'undefined') return { url: 'server', section: 'server' };
    
    return {
      url: window.location.href,
      section: window.location.pathname,
      component: document.title || 'unknown',
    };
  }

  // Handle validation errors
  static handleValidationError(
    error: ValidationError,
    operation: BusinessOperation,
    additionalContext?: Record<string, any>
  ): void {
    const businessError: BusinessError = {
      code: error.code,
      message: error.message,
      severity: ErrorSeverity.MEDIUM,
      category: ErrorCategory.VALIDATION,
      context: {
        field: error.field,
        value: error.value,
        operation,
        ...additionalContext,
      },
    };

    const errorContext: ErrorContext = {
      page: this.getPageInfo(),
      browser: this.getBrowserInfo(),
      business: {
        operation,
        workflow: 'validation',
      },
    };

    addBusinessBreadcrumb(operation, 'validation', error.field, 'error', {
      field: error.field,
      message: error.message,
    });

    reportBusinessError(businessError, errorContext);
  }

  // Handle network errors
  static handleNetworkError(
    error: NetworkError,
    operation: BusinessOperation,
    additionalContext?: Record<string, any>
  ): void {
    const severity = error.statusCode && error.statusCode >= 500 
      ? ErrorSeverity.HIGH 
      : ErrorSeverity.MEDIUM;

    const businessError: BusinessError = {
      code: error.code,
      message: error.message,
      severity,
      category: ErrorCategory.NETWORK,
      context: {
        statusCode: error.statusCode,
        endpoint: error.endpoint,
        operation,
        ...additionalContext,
      },
    };

    const errorContext: ErrorContext = {
      page: this.getPageInfo(),
      browser: this.getBrowserInfo(),
      business: {
        operation,
        workflow: 'network_request',
      },
      technical: {
        apiEndpoint: error.endpoint,
        statusCode: error.statusCode,
      },
    };

    addAPIBreadcrumb(
      'unknown',
      error.endpoint || 'unknown',
      error.statusCode,
      undefined,
      { operation, error: error.message }
    );

    reportBusinessError(businessError, errorContext);
  }

  // Handle database errors
  static handleDatabaseError(
    error: DatabaseError,
    operation: BusinessOperation,
    additionalContext?: Record<string, any>
  ): void {
    const businessError: BusinessError = {
      code: error.code,
      message: error.message,
      severity: ErrorSeverity.HIGH,
      category: ErrorCategory.DATABASE,
      context: {
        operation: error.operation,
        table: error.table,
        businessOperation: operation,
        ...additionalContext,
      },
    };

    const errorContext: ErrorContext = {
      page: this.getPageInfo(),
      browser: this.getBrowserInfo(),
      business: {
        operation,
        workflow: 'database_operation',
      },
      technical: {
        apiEndpoint: `database/${error.table}`,
      },
    };

    addBusinessBreadcrumb(operation, 'database', error.table, 'error', {
      operation: error.operation,
      table: error.table,
      message: error.message,
    });

    reportBusinessError(businessError, errorContext);
  }

  // Handle business logic errors
  static handleBusinessLogicError(
    error: BusinessLogicError,
    additionalContext?: Record<string, any>
  ): void {
    const businessError: BusinessError = {
      code: error.code,
      message: error.message,
      severity: ErrorSeverity.HIGH,
      category: ErrorCategory.BUSINESS,
      context: {
        operation: error.operation,
        entityId: error.entityId,
        entityType: error.entityType,
        ...additionalContext,
      },
    };

    const errorContext: ErrorContext = {
      page: this.getPageInfo(),
      browser: this.getBrowserInfo(),
      business: {
        operation: error.operation,
        entityId: error.entityId,
        entityType: error.entityType,
        workflow: 'business_logic',
      },
    };

    addBusinessBreadcrumb(
      error.operation,
      error.entityType || 'unknown',
      error.entityId,
      'error',
      { code: error.code, message: error.message }
    );

    reportBusinessError(businessError, errorContext);
  }

  // Handle generic errors with business context
  static handleGenericError(
    error: Error,
    operation: BusinessOperation,
    entityType?: string,
    entityId?: string,
    additionalContext?: Record<string, any>
  ): void {
    const errorContext: ErrorContext = {
      page: this.getPageInfo(),
      browser: this.getBrowserInfo(),
      business: {
        operation,
        entityId,
        entityType,
        workflow: 'generic_operation',
      },
    };

    addBusinessBreadcrumb(operation, entityType || 'unknown', entityId, 'error', {
      message: error.message,
      name: error.name,
      ...additionalContext,
    });

    reportError(error, errorContext);
  }

  // Async error wrapper for business operations
  static async wrapBusinessOperation<T>(
    operation: BusinessOperation,
    entityType: string,
    asyncFn: () => Promise<T>,
    entityId?: string,
    additionalContext?: Record<string, any>
  ): Promise<T> {
    try {
      addBusinessBreadcrumb(operation, entityType, entityId, 'success', {
        status: 'started',
        ...additionalContext,
      });

      const result = await asyncFn();

      addBusinessBreadcrumb(operation, entityType, entityId, 'success', {
        status: 'completed',
        ...additionalContext,
      });

      return result;
    } catch (error) {
      if (error instanceof ValidationError) {
        this.handleValidationError(error, operation, additionalContext);
      } else if (error instanceof NetworkError) {
        this.handleNetworkError(error, operation, additionalContext);
      } else if (error instanceof DatabaseError) {
        this.handleDatabaseError(error, operation, additionalContext);
      } else if (error instanceof BusinessLogicError) {
        this.handleBusinessLogicError(error, additionalContext);
      } else {
        this.handleGenericError(error as Error, operation, entityType, entityId, additionalContext);
      }
      
      throw error; // Re-throw to maintain error flow
    }
  }
}

// Utility functions for creating specific error types
export const createValidationError = (message: string, field?: string, value?: any): ValidationError => {
  return new ValidationError(message, field, value);
};

export const createNetworkError = (message: string, statusCode?: number, endpoint?: string): NetworkError => {
  return new NetworkError(message, statusCode, endpoint);
};

export const createDatabaseError = (message: string, operation?: string, table?: string): DatabaseError => {
  return new DatabaseError(message, operation, table);
};

export const createBusinessLogicError = (
  message: string,
  code: string,
  operation: BusinessOperation,
  entityId?: string,
  entityType?: string
): BusinessLogicError => {
  return new BusinessLogicError(message, code, operation, entityId, entityType);
};

// Export the error handler as default
export default BusinessErrorHandler;