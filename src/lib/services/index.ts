/**
 * Database services index - exports all service classes and types
 */

// Service classes
export { PropertyService } from './PropertyService';
export { BookingService } from './BookingService';
export { SocialPostService } from './SocialPostService';
export { ContactService } from './ContactService';

// Property service types
export type {
  PropertyFilters,
  PropertyCreateData,
  PropertyUpdateData,
} from './PropertyService';

// Booking service types
export type {
  BookingFilters,
  BookingCreateData,
  BookingUpdateData,
  BookingWithProperty,
} from './BookingService';

// Social post service types
export type {
  SocialPostFilters,
  SocialPostCreateData,
  SocialPostUpdateData,
  SocialPostWithBlogArticle,
} from './SocialPostService';

// Contact service types
export type {
  ContactFilters,
  ContactCreateData,
} from './ContactService';

// Re-export database types from supabase
export type {
  Database,
  Property,
  Booking,
  SocialPost,
  BlogArticle,
  AvailabilityCalendar,
  ContactSubmission,
  ConnectionStatus,
  DatabaseResult,
} from '../supabase';

// Export base service class
export { DatabaseService } from '../supabase';