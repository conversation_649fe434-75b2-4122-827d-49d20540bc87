/**
 * Property service for managing property data operations
 */

import { DatabaseService, type Property } from '../supabase';

export interface PropertyFilters {
  isActive?: boolean;
  maxGuests?: number;
  minPrice?: number;
  maxPrice?: number;
  location?: string;
  airbnbListingId?: string;
  hospitablePropertyKey?: string;
}

export interface PropertyCreateData {
  title: string;
  description?: string;
  location?: string;
  pricePerNight?: number;
  maxGuests?: number;
  amenities?: Record<string, any>;
  images?: string[];
  airbnbUrl?: string;
  airbnbListingId?: string;
  hospitableCalendarUrl?: string;
  hospitablePropertyKey?: string;
  isActive?: boolean;
}

export interface PropertyUpdateData extends Partial<PropertyCreateData> {}

export class PropertyService extends DatabaseService {
  /**
   * Get all properties with optional filtering
   */
  async getProperties(filters: PropertyFilters = {}): Promise<Property[]> {
    return this.executeQuery('get properties', async () => {
      let query = this.client.from('properties').select('*');

      // Apply filters
      if (filters.isActive !== undefined) {
        query = query.eq('is_active', filters.isActive);
      }
      if (filters.maxGuests) {
        query = query.gte('max_guests', filters.maxGuests);
      }
      if (filters.minPrice) {
        query = query.gte('price_per_night', filters.minPrice);
      }
      if (filters.maxPrice) {
        query = query.lte('price_per_night', filters.maxPrice);
      }
      if (filters.location) {
        query = query.ilike('location', `%${filters.location}%`);
      }
      if (filters.airbnbListingId) {
        query = query.eq('airbnb_listing_id', filters.airbnbListingId);
      }
      if (filters.hospitablePropertyKey) {
        query = query.eq('hospitable_property_key', filters.hospitablePropertyKey);
      }

      query = query.order('created_at', { ascending: false });

      return query;
    });
  }

  /**
   * Get a single property by ID
   */
  async getPropertyById(id: string): Promise<Property | null> {
    const result = await this.executeQuerySafe('get property by id', async () => {
      return this.client
        .from('properties')
        .select('*')
        .eq('id', id)
        .single();
    });

    return result.success ? result.data : null;
  }

  /**
   * Get a property by Airbnb listing ID
   */
  async getPropertyByAirbnbId(airbnbListingId: string): Promise<Property | null> {
    const result = await this.executeQuerySafe('get property by airbnb id', async () => {
      return this.client
        .from('properties')
        .select('*')
        .eq('airbnb_listing_id', airbnbListingId)
        .single();
    });

    return result.success ? result.data : null;
  }

  /**
   * Get a property by Hospitable property key
   */
  async getPropertyByHospitableKey(hospitableKey: string): Promise<Property | null> {
    const result = await this.executeQuerySafe('get property by hospitable key', async () => {
      return this.client
        .from('properties')
        .select('*')
        .eq('hospitable_property_key', hospitableKey)
        .single();
    });

    return result.success ? result.data : null;
  }

  /**
   * Create a new property
   */
  async createProperty(data: PropertyCreateData): Promise<Property> {
    return this.executeQuery('create property', async () => {
      return this.client
        .from('properties')
        .insert({
          title: data.title,
          description: data.description || null,
          location: data.location || null,
          price_per_night: data.pricePerNight || null,
          max_guests: data.maxGuests || null,
          amenities: data.amenities || {},
          images: data.images || [],
          airbnb_url: data.airbnbUrl || null,
          airbnb_listing_id: data.airbnbListingId || null,
          hospitable_calendar_url: data.hospitableCalendarUrl || null,
          hospitable_property_key: data.hospitablePropertyKey || null,
          is_active: data.isActive !== undefined ? data.isActive : true,
        })
        .select()
        .single();
    });
  }

  /**
   * Update an existing property
   */
  async updateProperty(id: string, data: PropertyUpdateData): Promise<Property> {
    return this.executeQuery('update property', async () => {
      const updateData: any = {};

      // Only include fields that are provided
      if (data.title !== undefined) updateData.title = data.title;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.location !== undefined) updateData.location = data.location;
      if (data.pricePerNight !== undefined) updateData.price_per_night = data.pricePerNight;
      if (data.maxGuests !== undefined) updateData.max_guests = data.maxGuests;
      if (data.amenities !== undefined) updateData.amenities = data.amenities;
      if (data.images !== undefined) updateData.images = data.images;
      if (data.airbnbUrl !== undefined) updateData.airbnb_url = data.airbnbUrl;
      if (data.airbnbListingId !== undefined) updateData.airbnb_listing_id = data.airbnbListingId;
      if (data.hospitableCalendarUrl !== undefined) updateData.hospitable_calendar_url = data.hospitableCalendarUrl;
      if (data.hospitablePropertyKey !== undefined) updateData.hospitable_property_key = data.hospitablePropertyKey;
      if (data.isActive !== undefined) updateData.is_active = data.isActive;

      return this.client
        .from('properties')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
    });
  }

  /**
   * Delete a property (soft delete by setting is_active to false)
   */
  async deleteProperty(id: string): Promise<Property> {
    return this.executeQuery('delete property', async () => {
      return this.client
        .from('properties')
        .update({ is_active: false })
        .eq('id', id)
        .select()
        .single();
    });
  }

  /**
   * Hard delete a property (permanent removal)
   */
  async hardDeleteProperty(id: string): Promise<void> {
    await this.executeQuery('hard delete property', async () => {
      return this.client
        .from('properties')
        .delete()
        .eq('id', id);
    });
  }

  /**
   * Get active properties count
   */
  async getActivePropertiesCount(): Promise<number> {
    const result = await this.executeQuerySafe('get active properties count', async () => {
      return this.client
        .from('properties')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true);
    });

    return result.success ? (result.data as any)?.count || 0 : 0;
  }

  /**
   * Search properties by title or description
   */
  async searchProperties(searchTerm: string, filters: PropertyFilters = {}): Promise<Property[]> {
    return this.executeQuery('search properties', async () => {
      let query = this.client
        .from('properties')
        .select('*')
        .or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);

      // Apply additional filters
      if (filters.isActive !== undefined) {
        query = query.eq('is_active', filters.isActive);
      }
      if (filters.maxGuests) {
        query = query.gte('max_guests', filters.maxGuests);
      }
      if (filters.minPrice) {
        query = query.gte('price_per_night', filters.minPrice);
      }
      if (filters.maxPrice) {
        query = query.lte('price_per_night', filters.maxPrice);
      }

      return query.order('created_at', { ascending: false });
    });
  }
}