/**
 * Enhanced Contact Service demonstrating UniversalErrorWrapper integration
 * 
 * This service shows how to use the UniversalErrorWrapper for comprehensive
 * error handling with automatic context detection and performance monitoring.
 */

import { DatabaseService, type ContactSubmission } from '../supabase';
import { 
  universalErrorWrapper,
  wrapAsync,
  wrapAsyncWithRetry,
  type OperationConfig
} from '../UniversalErrorWrapper';
import { 
  createValidationError, 
  createDatabaseError,
  BusinessOperation
} from '../errorHandling';
import { trackUserInteraction, UserInteractionType } from '../sessionTracking';

export interface ContactFilters {
  email?: string;
  submittedAfter?: string;
  submittedBefore?: string;
}

export interface ContactCreateData {
  name: string;
  email: string;
  message: string;
}

export class EnhancedContactService extends DatabaseService {
  
  /**
   * Get all contact submissions with enhanced error handling
   */
  async getContactSubmissions(filters: ContactFilters = {}): Promise<ContactSubmission[]> {
    return wrapAsync(
      'contact_submissions_fetch',
      async () => {
        // Record user journey step
        universalErrorWrapper.recordUserJourneyStep('contact_submissions_list');

        let query = this.client.from('contact_submissions').select('*');

        // Apply filters
        if (filters.email) {
          query = query.eq('email', filters.email);
        }
        if (filters.submittedAfter) {
          query = query.gte('created_at', filters.submittedAfter);
        }
        if (filters.submittedBefore) {
          query = query.lte('created_at', filters.submittedBefore);
        }

        const result = await query.order('created_at', { ascending: false });

        if (result.error) {
          throw createDatabaseError(
            `Failed to fetch contact submissions: ${result.error.message}`,
            'select',
            'contact_submissions'
          );
        }

        return result.data || [];
      },
      {
        entityType: 'contact_submission',
        entityId: 'list',
        customContext: {
          filters_applied: Object.keys(filters).length,
          has_email_filter: !!filters.email,
          has_date_filter: !!(filters.submittedAfter || filters.submittedBefore),
        },
        context: {
          business: {
            operation: BusinessOperation.DATA_FETCH,
            entityType: 'contact_submission',
            workflow: 'list_submissions',
          },
        },
      }
    );
  }

  /**
   * Get a single contact submission by ID with retry logic
   */
  async getContactSubmissionById(id: string): Promise<ContactSubmission | null> {
    return wrapAsyncWithRetry(
      'contact_submission_fetch_by_id',
      async () => {
        const result = await this.client
          .from('contact_submissions')
          .select('*')
          .eq('id', id)
          .single();

        if (result.error) {
          if (result.error.code === 'PGRST116') {
            // Not found - return null instead of throwing
            return null;
          }
          throw createDatabaseError(
            `Failed to fetch contact submission: ${result.error.message}`,
            'select',
            'contact_submissions'
          );
        }

        return result.data;
      },
      {
        maxRetries: 2,
        baseDelay: 500,
        retryCondition: (error) => {
          // Retry on network errors but not on validation or not found errors
          return error.message.includes('network') || 
                 error.message.includes('timeout') ||
                 error.message.includes('ECONNRESET');
        },
      },
      {
        entityType: 'contact_submission',
        entityId: id,
        customContext: { lookup_id: id },
        context: {
          business: {
            operation: BusinessOperation.DATA_FETCH,
            entityType: 'contact_submission',
            entityId: id,
            workflow: 'fetch_by_id',
          },
        },
      }
    );
  }

  /**
   * Create a new contact submission with comprehensive error handling
   */
  async createContactSubmission(data: ContactCreateData): Promise<ContactSubmission> {
    const config: OperationConfig = {
      operation: 'contact_submission_create',
      entityType: 'contact_submission',
      timeout: 10000, // 10 second timeout
      customContext: {
        email_domain: data.email.split('@')[1] || 'unknown',
        message_length: data.message.length,
        name_length: data.name.length,
      },
    };

    return universalErrorWrapper.wrapAsync(
      config,
      async () => {
        // Record user journey step
        universalErrorWrapper.recordUserJourneyStep('contact_form_submit');

        // Track user interaction
        trackUserInteraction(
          UserInteractionType.CONTACT_FORM,
          'contact_form_submit',
          { 
            name_length: data.name.length,
            email_domain: data.email.split('@')[1] || 'unknown',
            message_length: data.message.length,
          }
        );

        // Enhanced validation with specific error types
        this.validateContactData(data);

        // Check for spam patterns
        this.validateSpamPatterns(data.message);

        // Check for rate limiting
        await this.checkRateLimit(data.email);

        // Create the submission with database error handling
        const result = await this.client
          .from('contact_submissions')
          .insert({
            name: data.name.trim(),
            email: data.email.trim().toLowerCase(),
            message: data.message.trim(),
          })
          .select()
          .single();

        if (result.error) {
          throw createDatabaseError(
            `Failed to create contact submission: ${result.error.message}`,
            'insert',
            'contact_submissions'
          );
        }

        return result.data;
      },
      {
        business: {
          operation: BusinessOperation.CONTACT_FORM_SUBMIT,
          entityType: 'contact_submission',
          workflow: 'form_submission',
        },
        user: {
          email: data.email,
        },
      }
    );
  }

  /**
   * Delete a contact submission with enhanced error handling
   */
  async deleteContactSubmission(id: string): Promise<void> {
    return wrapAsync(
      'contact_submission_delete',
      async () => {
        const result = await this.client
          .from('contact_submissions')
          .delete()
          .eq('id', id);

        if (result.error) {
          throw createDatabaseError(
            `Failed to delete contact submission: ${result.error.message}`,
            'delete',
            'contact_submissions'
          );
        }
      },
      {
        entityType: 'contact_submission',
        entityId: id,
        context: {
          business: {
            operation: 'data_delete',
            entityType: 'contact_submission',
            entityId: id,
            workflow: 'admin_delete',
          },
        },
      }
    );
  }

  /**
   * Get contact submissions by email with caching consideration
   */
  async getContactSubmissionsByEmail(email: string): Promise<ContactSubmission[]> {
    return wrapAsync(
      'contact_submissions_by_email',
      async () => {
        return this.getContactSubmissions({ email: email.toLowerCase() });
      },
      {
        entityType: 'contact_submission',
        entityId: `email:${email}`,
        customContext: {
          email_domain: email.split('@')[1] || 'unknown',
          search_type: 'by_email',
        },
        context: {
          business: {
            operation: BusinessOperation.DATA_FETCH,
            entityType: 'contact_submission',
            workflow: 'search_by_email',
          },
        },
      }
    );
  }

  /**
   * Get recent contact submissions with performance monitoring
   */
  async getRecentContactSubmissions(days: number = 30): Promise<ContactSubmission[]> {
    return wrapAsync(
      'contact_submissions_recent',
      async () => {
        const dateThreshold = new Date();
        dateThreshold.setDate(dateThreshold.getDate() - days);
        const dateStr = dateThreshold.toISOString();

        return this.getContactSubmissions({
          submittedAfter: dateStr,
        });
      },
      {
        entityType: 'contact_submission',
        entityId: `recent:${days}days`,
        customContext: {
          days_back: days,
          date_threshold: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString(),
        },
        context: {
          business: {
            operation: BusinessOperation.DATA_FETCH,
            entityType: 'contact_submission',
            workflow: 'recent_submissions',
          },
        },
      }
    );
  }

  /**
   * Search contact submissions with enhanced error handling
   */
  async searchContactSubmissions(searchTerm: string): Promise<ContactSubmission[]> {
    return wrapAsync(
      'contact_submissions_search',
      async () => {
        // Validate search term
        if (!searchTerm.trim()) {
          throw createValidationError('Search term cannot be empty', 'searchTerm', searchTerm);
        }

        if (searchTerm.length < 2) {
          throw createValidationError('Search term must be at least 2 characters', 'searchTerm', searchTerm);
        }

        const result = await this.client
          .from('contact_submissions')
          .select('*')
          .or(`name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%,message.ilike.%${searchTerm}%`)
          .order('created_at', { ascending: false });

        if (result.error) {
          throw createDatabaseError(
            `Failed to search contact submissions: ${result.error.message}`,
            'select',
            'contact_submissions'
          );
        }

        return result.data || [];
      },
      {
        entityType: 'contact_submission',
        entityId: `search:${searchTerm}`,
        customContext: {
          search_term: searchTerm,
          search_length: searchTerm.length,
          search_type: 'full_text',
        },
        context: {
          business: {
            operation: BusinessOperation.DATA_FETCH,
            entityType: 'contact_submission',
            workflow: 'search_submissions',
          },
        },
      }
    );
  }

  /**
   * Get contact submission statistics with comprehensive error handling
   */
  async getContactStats(): Promise<{
    total: number;
    thisMonth: number;
    thisWeek: number;
    today: number;
    uniqueEmails: number;
  }> {
    return wrapAsync(
      'contact_stats_calculation',
      async () => {
        const result = await this.client
          .from('contact_submissions')
          .select('created_at, email');

        if (result.error) {
          throw createDatabaseError(
            `Failed to fetch contact submissions for stats: ${result.error.message}`,
            'select',
            'contact_submissions'
          );
        }

        const submissions = result.data || [];
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const thisWeek = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        const stats = {
          total: submissions.length,
          thisMonth: 0,
          thisWeek: 0,
          today: 0,
          uniqueEmails: 0,
        };

        const uniqueEmails = new Set<string>();

        submissions.forEach((submission) => {
          if (!submission.created_at) return;
          
          const submissionDate = new Date(submission.created_at);
          
          // Count by time period
          if (submissionDate >= today) {
            stats.today++;
          }
          if (submissionDate >= thisWeek) {
            stats.thisWeek++;
          }
          if (submissionDate >= thisMonth) {
            stats.thisMonth++;
          }

          // Track unique emails
          uniqueEmails.add(submission.email);
        });

        stats.uniqueEmails = uniqueEmails.size;

        return stats;
      },
      {
        entityType: 'contact_submission',
        entityId: 'stats',
        customContext: {
          calculation_type: 'time_based_stats',
          includes_unique_emails: true,
        },
        context: {
          business: {
            operation: BusinessOperation.DATA_FETCH,
            entityType: 'contact_submission',
            workflow: 'statistics_calculation',
          },
        },
      }
    );
  }

  /**
   * Check if email has submitted recently with rate limiting
   */
  async hasRecentSubmission(email: string): Promise<boolean> {
    return wrapAsync(
      'contact_rate_limit_check',
      async () => {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const dateStr = yesterday.toISOString();

        const submissions = await this.getContactSubmissions({
          email: email.toLowerCase(),
          submittedAfter: dateStr,
        });

        return submissions.length > 0;
      },
      {
        entityType: 'contact_submission',
        entityId: `rate_check:${email}`,
        customContext: {
          email_domain: email.split('@')[1] || 'unknown',
          check_type: 'rate_limit',
          time_window: '24_hours',
        },
        context: {
          business: {
            operation: BusinessOperation.DATA_FETCH,
            entityType: 'contact_submission',
            workflow: 'rate_limit_check',
          },
        },
      }
    );
  }

  /**
   * Private validation methods with enhanced error handling
   */
  private validateContactData(data: ContactCreateData): void {
    if (!data.name.trim()) {
      throw createValidationError('Name is required', 'name', data.name);
    }
    if (!data.email.trim()) {
      throw createValidationError('Email is required', 'email', data.email);
    }
    if (!data.message.trim()) {
      throw createValidationError('Message is required', 'message', data.message);
    }

    // Enhanced email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      throw createValidationError('Invalid email format', 'email', data.email);
    }

    // Check message length
    if (data.message.trim().length < 10) {
      throw createValidationError('Message must be at least 10 characters long', 'message', data.message);
    }

    // Check for reasonable limits
    if (data.name.length > 100) {
      throw createValidationError('Name is too long (max 100 characters)', 'name', data.name.length);
    }

    if (data.message.length > 5000) {
      throw createValidationError('Message is too long (max 5000 characters)', 'message', data.message.length);
    }
  }

  private validateSpamPatterns(message: string): void {
    const spamPatterns = [
      'http://', 'https://', 'www.', 'click here', 'buy now',
      'limited time', 'act now', 'free money', 'guaranteed',
      'no risk', 'call now', 'urgent', 'congratulations'
    ];
    
    const lowerMessage = message.toLowerCase();
    const detectedPatterns = spamPatterns.filter(pattern => 
      lowerMessage.includes(pattern)
    );

    if (detectedPatterns.length > 0) {
      throw createValidationError(
        'Message contains suspicious content', 
        'message', 
        { patterns: detectedPatterns, message_length: message.length }
      );
    }
  }

  private async checkRateLimit(email: string): Promise<void> {
    const hasRecent = await this.hasRecentSubmission(email);
    if (hasRecent) {
      throw createValidationError(
        'You have already submitted a message recently. Please wait 24 hours before submitting again.',
        'email',
        { email, rate_limit: '24_hours' }
      );
    }
  }
}