/**
 * Contact service for managing contact submission operations
 */

import { DatabaseService, type ContactSubmission } from '../supabase';
import { 
  BusinessErrorHandler, 
  BusinessOperation, 
  createValidationError, 
  createDatabaseError 
} from '../errorHandling';
import { trackUserInteraction, UserInteractionType } from '../sessionTracking';

export interface ContactFilters {
  email?: string;
  submittedAfter?: string;
  submittedBefore?: string;
}

export interface ContactCreateData {
  name: string;
  email: string;
  phone?: string;
  message: string;
  subject?: string;
  source?: string;
  metadata?: any;
}

export class ContactService extends DatabaseService {
  /**
   * Get all contact submissions with optional filtering
   */
  async getContactSubmissions(filters: ContactFilters = {}): Promise<ContactSubmission[]> {
    return BusinessErrorHandler.wrapBusinessOperation(
      BusinessOperation.DATA_FETCH,
      'contact_submission',
      async () => {
        return (async () => {
            let query = this.client.from('contact_submissions').select('*');

            // Apply filters
            if (filters.email) {
              query = query.eq('email', filters.email);
            }
            if (filters.submittedAfter) {
              query = query.gte('created_at', filters.submittedAfter);
            }
            if (filters.submittedBefore) {
              query = query.lte('created_at', filters.submittedBefore);
            }

            const result = await query.order('created_at', { ascending: false });

            if (result.error) {
              throw createDatabaseError(
                `Failed to fetch contact submissions: ${result.error.message}`,
                'select',
                'contact_submissions'
              );
            }

            return result.data || [];
          })();
      },
      undefined,
      { 
        filters_applied: Object.keys(filters).length,
        has_email_filter: !!filters.email,
        has_date_filter: !!(filters.submittedAfter || filters.submittedBefore),
      }
    );
  }

  /**
   * Get a single contact submission by ID
   */
  async getContactSubmissionById(id: string): Promise<ContactSubmission | null> {
    const result = await this.executeQuerySafe('get contact submission by id', async () => {
      return this.client
        .from('contact_submissions')
        .select('*')
        .eq('id', id)
        .single();
    });

    return result.success ? result.data : null;
  }

  /**
   * Create a new contact submission
   */
  async createContactSubmission(data: ContactCreateData): Promise<ContactSubmission> {
    return BusinessErrorHandler.wrapBusinessOperation(
      BusinessOperation.CONTACT_FORM_SUBMIT,
      'contact_submission',
      async () => {
        // Track user interaction
        trackUserInteraction(
          UserInteractionType.CONTACT_FORM,
          'contact_form_submit',
          { 
            name_length: data.name.length,
            email_domain: data.email.split('@')[1] || 'unknown',
            message_length: data.message.length,
          }
        );

        // Enhanced validation with specific error types
        if (!data.name.trim()) {
          throw createValidationError('Name is required', 'name', data.name);
        }
        if (!data.email.trim()) {
          throw createValidationError('Email is required', 'email', data.email);
        }
        if (!data.message.trim()) {
          throw createValidationError('Message is required', 'message', data.message);
        }

        // Enhanced email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
          throw createValidationError('Invalid email format', 'email', data.email);
        }

        // Check message length
        if (data.message.trim().length < 10) {
          throw createValidationError('Message must be at least 10 characters long', 'message', data.message);
        }

        // Check for spam patterns
        const spamPatterns = ['http://', 'https://', 'www.', 'click here', 'buy now'];
        const hasSpamPattern = spamPatterns.some(pattern => 
          data.message.toLowerCase().includes(pattern)
        );
        if (hasSpamPattern) {
          throw createValidationError('Message contains suspicious content', 'message', 'spam_detected');
        }

        // Monitor the database operation
        return (async () => {
            try {
              const result = await this.client
                .from('contact_submissions')
                .insert({
                  name: data.name.trim(),
                  email: data.email.trim().toLowerCase(),
                  message: data.message.trim(),
                })
                .select()
                .single();

              if (result.error) {
                throw createDatabaseError(
                  `Failed to create contact submission: ${result.error.message}`,
                  'insert',
                  'contact_submissions'
                );
              }

              return result.data;
            } catch (error) {
              if (error instanceof Error && error.name === 'DatabaseError') {
                throw error;
              }
              throw createDatabaseError(
                `Database operation failed: ${(error as Error).message}`,
                'insert',
                'contact_submissions'
              );
            }
          })();
      },
      undefined,
      { 
        email_domain: data.email.split('@')[1] || 'unknown',
        message_length: data.message.length,
        validation_passed: true,
      }
    );
  }

  /**
   * Delete a contact submission
   */
  async deleteContactSubmission(id: string): Promise<void> {
    await this.executeQuery('delete contact submission', async () => {
      return this.client
        .from('contact_submissions')
        .delete()
        .eq('id', id);
    });
  }

  /**
   * Get contact submissions by email
   */
  async getContactSubmissionsByEmail(email: string): Promise<ContactSubmission[]> {
    return this.getContactSubmissions({ email: email.toLowerCase() });
  }

  /**
   * Get recent contact submissions (last 30 days)
   */
  async getRecentContactSubmissions(days: number = 30): Promise<ContactSubmission[]> {
    const dateThreshold = new Date();
    dateThreshold.setDate(dateThreshold.getDate() - days);
    const dateStr = dateThreshold.toISOString();

    return this.getContactSubmissions({
      submittedAfter: dateStr,
    });
  }

  /**
   * Search contact submissions by name, email, or message
   */
  async searchContactSubmissions(searchTerm: string): Promise<ContactSubmission[]> {
    return this.executeQuery('search contact submissions', async () => {
      return this.client
        .from('contact_submissions')
        .select('*')
        .or(`name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%,message.ilike.%${searchTerm}%`)
        .order('created_at', { ascending: false });
    });
  }

  /**
   * Get contact submission statistics
   */
  async getContactStats(): Promise<{
    total: number;
    thisMonth: number;
    thisWeek: number;
    today: number;
    uniqueEmails: number;
  }> {
    const result = await this.executeQuerySafe('get contact stats', async () => {
      return this.client
        .from('contact_submissions')
        .select('created_at, email');
    });

    if (!result.success || !result.data) {
      return {
        total: 0,
        thisMonth: 0,
        thisWeek: 0,
        today: 0,
        uniqueEmails: 0,
      };
    }

    const submissions = result.data as ContactSubmission[];
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const stats = {
      total: submissions.length,
      thisMonth: 0,
      thisWeek: 0,
      today: 0,
      uniqueEmails: 0,
    };

    const uniqueEmails = new Set<string>();

    submissions.forEach((submission) => {
      if (!submission.created_at) return;
      
      const submissionDate = new Date(submission.created_at);
      
      // Count by time period
      if (submissionDate >= today) {
        stats.today++;
      }
      if (submissionDate >= thisWeek) {
        stats.thisWeek++;
      }
      if (submissionDate >= thisMonth) {
        stats.thisMonth++;
      }

      // Track unique emails
      uniqueEmails.add(submission.email);
    });

    stats.uniqueEmails = uniqueEmails.size;

    return stats;
  }

  /**
   * Check if email has submitted recently (within last 24 hours)
   */
  async hasRecentSubmission(email: string): Promise<boolean> {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const dateStr = yesterday.toISOString();

    const submissions = await this.getContactSubmissions({
      email: email.toLowerCase(),
      submittedAfter: dateStr,
    });

    return submissions.length > 0;
  }

  /**
   * Get submission count by email
   */
  async getSubmissionCountByEmail(email: string): Promise<number> {
    const submissions = await this.getContactSubmissionsByEmail(email);
    return submissions.length;
  }
}