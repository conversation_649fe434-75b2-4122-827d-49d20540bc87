/**
 * Booking service for managing booking data operations
 */

import { DatabaseService, type Booking, type Database } from '../supabase';

export interface BookingFilters {
  propertyId?: string;
  status?: Database['public']['Enums']['booking_status'];
  guestEmail?: string;
  checkInAfter?: string;
  checkInBefore?: string;
  checkOutAfter?: string;
  checkOutBefore?: string;
}

export interface BookingCreateData {
  propertyId: string;
  guestName: string;
  guestEmail: string;
  guestPhone?: string;
  checkIn: string;
  checkOut: string;
  totalGuests: number;
  totalPrice: number;
  status?: Database['public']['Enums']['booking_status'];
  airbnbSyncStatus?: string;
}

export interface BookingUpdateData extends Partial<Omit<BookingCreateData, 'propertyId'>> {}

export interface BookingWithProperty extends Booking {
  property?: {
    id: string;
    title: string;
    location: string | null;
    price_per_night: number | null;
  };
}

export class BookingService extends DatabaseService {
  /**
   * Get all bookings with optional filtering
   */
  async getBookings(filters: BookingFilters = {}): Promise<Booking[]> {
    const result = await this.executeQuerySafe('get bookings', async () => {
      let query = this.client.from('bookings').select('*');

      // Apply filters
      if (filters.propertyId) {
        query = query.eq('property_id', filters.propertyId);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.guestEmail) {
        query = query.eq('guest_email', filters.guestEmail);
      }
      if (filters.checkInAfter) {
        query = query.gte('check_in', filters.checkInAfter);
      }
      if (filters.checkInBefore) {
        query = query.lte('check_in', filters.checkInBefore);
      }
      if (filters.checkOutAfter) {
        query = query.gte('check_out', filters.checkOutAfter);
      }
      if (filters.checkOutBefore) {
        query = query.lte('check_out', filters.checkOutBefore);
      }

      return query.order('created_at', { ascending: false });
    });

    return result.success && result.data ? result.data : [];
  }

  /**
   * Get bookings with property information
   */
  async getBookingsWithProperty(filters: BookingFilters = {}): Promise<BookingWithProperty[]> {
    const result = await this.executeQuerySafe('get bookings with property', async () => {
      let query = this.client
        .from('bookings')
        .select(`
          *,
          property:properties(
            id,
            title,
            location,
            price_per_night
          )
        `);

      // Apply filters
      if (filters.propertyId) {
        query = query.eq('property_id', filters.propertyId);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.guestEmail) {
        query = query.eq('guest_email', filters.guestEmail);
      }
      if (filters.checkInAfter) {
        query = query.gte('check_in', filters.checkInAfter);
      }
      if (filters.checkInBefore) {
        query = query.lte('check_in', filters.checkInBefore);
      }
      if (filters.checkOutAfter) {
        query = query.gte('check_out', filters.checkOutAfter);
      }
      if (filters.checkOutBefore) {
        query = query.lte('check_out', filters.checkOutBefore);
      }

      return query.order('created_at', { ascending: false });
    });

    return result.success && result.data ? result.data : [];
  }

  /**
   * Get a single booking by ID
   */
  async getBookingById(id: string): Promise<Booking | null> {
    const result = await this.executeQuerySafe('get booking by id', async () => {
      return this.client
        .from('bookings')
        .select('*')
        .eq('id', id)
        .single();
    });

    return result.success ? result.data : null;
  }

  /**
   * Get a booking with property information by ID
   */
  async getBookingWithPropertyById(id: string): Promise<BookingWithProperty | null> {
    const result = await this.executeQuerySafe('get booking with property by id', async () => {
      return this.client
        .from('bookings')
        .select(`
          *,
          property:properties(
            id,
            title,
            location,
            price_per_night
          )
        `)
        .eq('id', id)
        .single();
    });

    return result.success ? result.data : null;
  }

  /**
   * Create a new booking
   */
  async createBooking(data: BookingCreateData): Promise<Booking> {
    // Validate dates
    const checkIn = new Date(data.checkIn);
    const checkOut = new Date(data.checkOut);
    
    if (checkOut <= checkIn) {
      throw new Error('Check-out date must be after check-in date');
    }

    if (checkIn < new Date()) {
      throw new Error('Check-in date cannot be in the past');
    }

    return this.executeQuery('create booking', async () => {
      return this.client
        .from('bookings')
        .insert({
          property_id: data.propertyId,
          guest_name: data.guestName,
          guest_email: data.guestEmail,
          guest_phone: data.guestPhone || null,
          check_in: data.checkIn,
          check_out: data.checkOut,
          total_guests: data.totalGuests,
          total_price: data.totalPrice,
          status: data.status || 'pending',
          airbnb_sync_status: data.airbnbSyncStatus || 'pending',
        })
        .select()
        .single();
    });
  }

  /**
   * Update an existing booking
   */
  async updateBooking(id: string, data: BookingUpdateData): Promise<Booking> {
    return this.executeQuery('update booking', async () => {
      const updateData: any = {};

      // Only include fields that are provided
      if (data.guestName !== undefined) updateData.guest_name = data.guestName;
      if (data.guestEmail !== undefined) updateData.guest_email = data.guestEmail;
      if (data.guestPhone !== undefined) updateData.guest_phone = data.guestPhone;
      if (data.checkIn !== undefined) updateData.check_in = data.checkIn;
      if (data.checkOut !== undefined) updateData.check_out = data.checkOut;
      if (data.totalGuests !== undefined) updateData.total_guests = data.totalGuests;
      if (data.totalPrice !== undefined) updateData.total_price = data.totalPrice;
      if (data.status !== undefined) updateData.status = data.status;
      if (data.airbnbSyncStatus !== undefined) updateData.airbnb_sync_status = data.airbnbSyncStatus;

      return this.client
        .from('bookings')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
    });
  }

  /**
   * Cancel a booking
   */
  async cancelBooking(id: string): Promise<Booking> {
    return this.updateBooking(id, { status: 'cancelled' });
  }

  /**
   * Confirm a booking
   */
  async confirmBooking(id: string): Promise<Booking> {
    return this.updateBooking(id, { status: 'confirmed' });
  }

  /**
   * Delete a booking
   */
  async deleteBooking(id: string): Promise<void> {
    await this.executeQuery('delete booking', async () => {
      return this.client
        .from('bookings')
        .delete()
        .eq('id', id);
    });
  }

  /**
   * Check for booking conflicts
   */
  async checkBookingConflicts(
    propertyId: string,
    checkIn: string,
    checkOut: string,
    excludeBookingId?: string
  ): Promise<Booking[]> {
    const result = await this.executeQuerySafe('check booking conflicts', async () => {
      let query = this.client
        .from('bookings')
        .select('*')
        .eq('property_id', propertyId)
        .neq('status', 'cancelled')
        .or(`check_in.lte.${checkOut},check_out.gte.${checkIn}`);

      if (excludeBookingId) {
        query = query.neq('id', excludeBookingId);
      }

      return query;
    });

    return result.success && result.data ? result.data : [];
  }

  /**
   * Get upcoming bookings for a property
   */
  async getUpcomingBookings(propertyId: string, days: number = 30): Promise<Booking[]> {
    const today = new Date().toISOString().split('T')[0];
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);
    const futureDateStr = futureDate.toISOString().split('T')[0];

    return this.getBookings({
      propertyId,
      checkInAfter: today,
      checkInBefore: futureDateStr,
    });
  }

  /**
   * Get bookings by guest email
   */
  async getBookingsByGuest(guestEmail: string): Promise<BookingWithProperty[]> {
    return this.getBookingsWithProperty({ guestEmail });
  }

  /**
   * Get booking statistics
   */
  async getBookingStats(): Promise<{
    total: number;
    pending: number;
    confirmed: number;
    cancelled: number;
    totalRevenue: number;
  }> {
    const result = await this.executeQuerySafe('get booking stats', async () => {
      return this.client
        .from('bookings')
        .select('status, total_price');
    });

    if (!result.success || !result.data) {
      return {
        total: 0,
        pending: 0,
        confirmed: 0,
        cancelled: 0,
        totalRevenue: 0,
      };
    }

    const bookings = result.data as Booking[];
    const stats = {
      total: bookings.length,
      pending: 0,
      confirmed: 0,
      cancelled: 0,
      totalRevenue: 0,
    };

    bookings.forEach((booking) => {
      switch (booking.status) {
        case 'pending':
          stats.pending++;
          break;
        case 'confirmed':
          stats.confirmed++;
          stats.totalRevenue += Number(booking.total_price);
          break;
        case 'cancelled':
          stats.cancelled++;
          break;
      }
    });

    return stats;
  }
}