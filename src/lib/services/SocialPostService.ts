/**
 * Social post service for managing social media content operations
 */

import { DatabaseService, type SocialPost, type Database } from '../supabase';

export interface SocialPostFilters {
  platform?: Database['public']['Enums']['platform'];
  postType?: Database['public']['Enums']['post_type'];
  publishedAfter?: string;
  publishedBefore?: string;
  isPublished?: boolean;
}

export interface SocialPostCreateData {
  title: string;
  content: string;
  mediaUrls?: string[];
  postType?: Database['public']['Enums']['post_type'];
  platform?: Database['public']['Enums']['platform'];
  publishedAt?: string;
}

export interface SocialPostUpdateData extends Partial<SocialPostCreateData> {}

export interface SocialPostWithBlogArticle extends SocialPost {
  blogArticle?: {
    id: string;
    title: string;
    slug: string;
    excerpt: string | null;
  }[];
}

export class SocialPostService extends DatabaseService {
  /**
   * Get all social posts with optional filtering
   */
  async getSocialPosts(filters: SocialPostFilters = {}): Promise<SocialPost[]> {
    const result = await this.executeQuerySafe('get social posts', async () => {
      let query = this.client.from('social_posts').select('*');

      // Apply filters
      if (filters.platform) {
        query = query.eq('platform', filters.platform);
      }
      if (filters.postType) {
        query = query.eq('post_type', filters.postType);
      }
      if (filters.publishedAfter) {
        query = query.gte('published_at', filters.publishedAfter);
      }
      if (filters.publishedBefore) {
        query = query.lte('published_at', filters.publishedBefore);
      }
      if (filters.isPublished !== undefined) {
        if (filters.isPublished) {
          query = query.not('published_at', 'is', null);
        } else {
          query = query.is('published_at', null);
        }
      }

      return query.order('created_at', { ascending: false });
    });

    return result.success && result.data ? result.data : [];
  }

  /**
   * Get social posts with linked blog articles
   */
  async getSocialPostsWithBlogArticles(filters: SocialPostFilters = {}): Promise<SocialPostWithBlogArticle[]> {
    const result = await this.executeQuerySafe('get social posts with blog articles', async () => {
      let query = this.client
        .from('social_posts')
        .select(`
          *,
          blogArticle:blog_articles(
            id,
            title,
            slug,
            excerpt
          )
        `);

      // Apply filters
      if (filters.platform) {
        query = query.eq('platform', filters.platform);
      }
      if (filters.postType) {
        query = query.eq('post_type', filters.postType);
      }
      if (filters.publishedAfter) {
        query = query.gte('published_at', filters.publishedAfter);
      }
      if (filters.publishedBefore) {
        query = query.lte('published_at', filters.publishedBefore);
      }
      if (filters.isPublished !== undefined) {
        if (filters.isPublished) {
          query = query.not('published_at', 'is', null);
        } else {
          query = query.is('published_at', null);
        }
      }

      return query.order('created_at', { ascending: false });
    });

    return result.success && result.data ? result.data : [];
  }

  /**
   * Get a single social post by ID
   */
  async getSocialPostById(id: string): Promise<SocialPost | null> {
    const result = await this.executeQuerySafe('get social post by id', async () => {
      return this.client
        .from('social_posts')
        .select('*')
        .eq('id', id)
        .single();
    });

    return result.success ? result.data : null;
  }

  /**
   * Get a social post with blog article by ID
   */
  async getSocialPostWithBlogArticleById(id: string): Promise<SocialPostWithBlogArticle | null> {
    const result = await this.executeQuerySafe('get social post with blog article by id', async () => {
      return this.client
        .from('social_posts')
        .select(`
          *,
          blogArticle:blog_articles(
            id,
            title,
            slug,
            excerpt
          )
        `)
        .eq('id', id)
        .single();
    });

    return result.success ? result.data : null;
  }

  /**
   * Create a new social post
   */
  async createSocialPost(data: SocialPostCreateData): Promise<SocialPost> {
    return this.executeQuery('create social post', async () => {
      return this.client
        .from('social_posts')
        .insert({
          title: data.title,
          content: data.content,
          media_urls: data.mediaUrls || [],
          post_type: data.postType || null,
          platform: data.platform || null,
          published_at: data.publishedAt || null,
        })
        .select()
        .single();
    });
  }

  /**
   * Update an existing social post
   */
  async updateSocialPost(id: string, data: SocialPostUpdateData): Promise<SocialPost> {
    return this.executeQuery('update social post', async () => {
      const updateData: any = {};

      // Only include fields that are provided
      if (data.title !== undefined) updateData.title = data.title;
      if (data.content !== undefined) updateData.content = data.content;
      if (data.mediaUrls !== undefined) updateData.media_urls = data.mediaUrls;
      if (data.postType !== undefined) updateData.post_type = data.postType;
      if (data.platform !== undefined) updateData.platform = data.platform;
      if (data.publishedAt !== undefined) updateData.published_at = data.publishedAt;

      return this.client
        .from('social_posts')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
    });
  }

  /**
   * Publish a social post
   */
  async publishSocialPost(id: string, publishedAt?: string): Promise<SocialPost> {
    const publishDate = publishedAt || new Date().toISOString();
    return this.updateSocialPost(id, { publishedAt: publishDate });
  }

  /**
   * Unpublish a social post
   */
  async unpublishSocialPost(id: string): Promise<SocialPost> {
    return this.updateSocialPost(id, { publishedAt: undefined });
  }

  /**
   * Delete a social post
   */
  async deleteSocialPost(id: string): Promise<void> {
    await this.executeQuery('delete social post', async () => {
      return this.client
        .from('social_posts')
        .delete()
        .eq('id', id);
    });
  }

  /**
   * Get published posts by platform
   */
  async getPublishedPostsByPlatform(platform: Database['public']['Enums']['platform']): Promise<SocialPost[]> {
    return this.getSocialPosts({
      platform,
      isPublished: true,
    });
  }

  /**
   * Get recent posts (last 30 days)
   */
  async getRecentPosts(days: number = 30): Promise<SocialPost[]> {
    const dateThreshold = new Date();
    dateThreshold.setDate(dateThreshold.getDate() - days);
    const dateStr = dateThreshold.toISOString();

    return this.getSocialPosts({
      publishedAfter: dateStr,
      isPublished: true,
    });
  }

  /**
   * Search social posts by title or content
   */
  async searchSocialPosts(searchTerm: string, filters: SocialPostFilters = {}): Promise<SocialPost[]> {
    return this.executeQuery('search social posts', async () => {
      let query = this.client
        .from('social_posts')
        .select('*')
        .or(`title.ilike.%${searchTerm}%,content.ilike.%${searchTerm}%`);

      // Apply additional filters
      if (filters.platform) {
        query = query.eq('platform', filters.platform);
      }
      if (filters.postType) {
        query = query.eq('post_type', filters.postType);
      }
      if (filters.isPublished !== undefined) {
        if (filters.isPublished) {
          query = query.not('published_at', 'is', null);
        } else {
          query = query.is('published_at', null);
        }
      }

      return query.order('created_at', { ascending: false });
    });
  }

  /**
   * Get social post statistics
   */
  async getSocialPostStats(): Promise<{
    total: number;
    published: number;
    draft: number;
    byPlatform: Record<string, number>;
    byType: Record<string, number>;
  }> {
    const result = await this.executeQuerySafe('get social post stats', async () => {
      return this.client
        .from('social_posts')
        .select('published_at, platform, post_type');
    });

    if (!result.success || !result.data) {
      return {
        total: 0,
        published: 0,
        draft: 0,
        byPlatform: {},
        byType: {},
      };
    }

    const posts = result.data as SocialPost[];
    const stats = {
      total: posts.length,
      published: 0,
      draft: 0,
      byPlatform: {} as Record<string, number>,
      byType: {} as Record<string, number>,
    };

    posts.forEach((post) => {
      // Count published vs draft
      if (post.published_at) {
        stats.published++;
      } else {
        stats.draft++;
      }

      // Count by platform
      if (post.platform) {
        stats.byPlatform[post.platform] = (stats.byPlatform[post.platform] || 0) + 1;
      }

      // Count by type
      if (post.post_type) {
        stats.byType[post.post_type] = (stats.byType[post.post_type] || 0) + 1;
      }
    });

    return stats;
  }
}