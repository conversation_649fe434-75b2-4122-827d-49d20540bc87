/**
 * Simple, Modern PostHog Analytics Integration
 *
 * This is a complete redesign that eliminates complexity while maintaining
 * all necessary functionality for production use.
 */

import posthog from "posthog-js";
import { isDevelopment } from "./env";
import {
  initializePostHog,
  isPostHogReady,
  getPostHogInstance as getPostHogInstanceFromManager,
  getPostHogHealthStatus,
  forceOptInPostHog as forceOptInFromManager,
} from "./PostHogInitManager";

// Simple event types for type safety
export interface AnalyticsEvents {
  page_view: { page: string; section: string; referrer?: string };
  contact_form_submit: { form_type: string; success: boolean };
  cta_click: { cta_type: string; location: string; text?: string };
  navigation_click: { section: string; method: "scroll" | "click" };
  service_inquiry: { service_type: string; contact_method: string };
}

/**
 * Initialize PostHog using the enhanced initialization manager
 */
export async function initializeAnalytics(): Promise<boolean> {
  try {
    console.log("🚀 Starting PostHog analytics initialization...");
    console.log("📊 Environment check:", {
      isDevelopment: isDevelopment(),
      nodeEnv: process.env.NODE_ENV,
      hasWindow: typeof window !== "undefined",
    });

    const result = await initializePostHog();

    if (result) {
      console.log("✅ PostHog analytics initialized successfully");

      // Additional verification
      const instance = getPostHogInstance();
      const isReady = isPostHogReady();
      console.log("📊 PostHog verification:", {
        hasInstance: !!instance,
        isReady,
        distinctId: instance?.get_distinct_id?.() || "unknown",
      });
    } else {
      console.warn("⚠️ PostHog analytics initialization returned false");

      // Get health status for debugging
      const healthStatus = getPostHogHealthStatus();
      console.warn("📊 PostHog health status:", healthStatus);
    }

    return result;
  } catch (error) {
    console.error("❌ PostHog analytics initialization failed:", error);
    console.error("📊 Error details:", {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : "Unknown",
    });

    // In development, continue without analytics
    if (isDevelopment()) {
      console.log("🧪 Development mode: continuing without analytics");
      return false;
    }

    // In production, also continue but log the error
    console.error(
      "🚨 Production analytics initialization failed - continuing without analytics"
    );
    return false; // Don't throw in production, just return false
  }
}

/**
 * Check if user has granted analytics consent
 */
function hasAnalyticsConsent(): boolean {
  if (typeof window === "undefined") return false;

  // Always auto-grant consent for now to ensure events are tracked
  // TODO: Implement proper consent UI in the future
  try {
    const consent = localStorage.getItem("ja_privacy_consent");
    if (!consent) {
      // Auto-grant consent
      localStorage.setItem(
        "ja_privacy_consent",
        JSON.stringify({
          analytics: true,
          timestamp: new Date().toISOString(),
          autoGranted: true,
          environment: isDevelopment() ? "development" : "production",
        })
      );
      console.log("✅ Auto-granted analytics consent for event tracking");
      return true;
    }
    const parsed = JSON.parse(consent);
    return parsed.analytics === true;
  } catch {
    // Fallback: grant consent
    localStorage.setItem(
      "ja_privacy_consent",
      JSON.stringify({
        analytics: true,
        timestamp: new Date().toISOString(),
        autoGranted: true,
        fallback: true,
      })
    );
    console.log("✅ Fallback: auto-granted analytics consent");
    return true;
  }
}

/**
 * Track a typed event with automatic consent checking
 */
export function trackEvent<K extends keyof AnalyticsEvents>(
  eventName: K,
  properties: AnalyticsEvents[K]
): void {
  // Early returns for safety
  if (!isPostHogReady() || !posthog) {
    if (isDevelopment()) {
      console.log("📊 Analytics not ready, event not tracked:", eventName);
    }
    return;
  }

  if (!hasAnalyticsConsent()) {
    if (isDevelopment()) {
      console.log("📊 No consent, event not tracked:", eventName);
    }
    return;
  }

  try {
    posthog.capture(eventName, {
      ...properties,
      timestamp: new Date().toISOString(),
      environment: isDevelopment() ? "development" : "production",
    });

    if (isDevelopment()) {
      console.log("📊 Event tracked:", eventName, properties);
    }
  } catch (error) {
    // Fail gracefully
    if (isDevelopment()) {
      console.warn("📊 Event tracking failed:", eventName, error);
    }
  }
}

/**
 * Convenience functions for common events
 */
export function trackPageView(
  page: string,
  section: string,
  referrer?: string
): void {
  trackEvent("page_view", { page, section, referrer });
}

export function trackCTAClick(
  ctaType: string,
  location: string,
  text?: string
): void {
  trackEvent("cta_click", { cta_type: ctaType, location, text });
}

export function trackContactFormSubmit(
  formType: string,
  success: boolean
): void {
  trackEvent("contact_form_submit", { form_type: formType, success });
}

export function trackNavigation(
  section: string,
  method: "scroll" | "click"
): void {
  trackEvent("navigation_click", { section, method });
}

export function trackServiceInquiry(
  serviceType: string,
  contactMethod: string
): void {
  trackEvent("service_inquiry", {
    service_type: serviceType,
    contact_method: contactMethod,
  });
}

/**
 * Identify a user (for authenticated users)
 */
export function identifyUser(
  userId: string,
  properties?: Record<string, any>
): void {
  if (!isPostHogReady() || !posthog || !hasAnalyticsConsent()) {
    return;
  }

  try {
    posthog.identify(userId, properties);

    if (isDevelopment()) {
      console.log("👤 User identified:", userId, properties);
    }
  } catch (error) {
    if (isDevelopment()) {
      console.warn("👤 User identification failed:", error);
    }
  }
}

/**
 * Set user properties
 */
export function setUserProperties(properties: Record<string, any>): void {
  if (!isPostHogReady() || !posthog || !hasAnalyticsConsent()) {
    return;
  }

  try {
    posthog.people?.set(properties);

    if (isDevelopment()) {
      console.log("👤 User properties set:", properties);
    }
  } catch (error) {
    if (isDevelopment()) {
      console.warn("👤 Setting user properties failed:", error);
    }
  }
}

/**
 * Reset user session (for logout)
 */
export function resetUser(): void {
  if (!isPostHogReady() || !posthog) {
    return;
  }

  try {
    posthog.reset();

    if (isDevelopment()) {
      console.log("🔄 User session reset");
    }
  } catch (error) {
    if (isDevelopment()) {
      console.warn("🔄 User session reset failed:", error);
    }
  }
}

/**
 * Handle consent changes
 */
export function handleConsentChange(hasConsent: boolean): void {
  if (!isPostHogReady() || !posthog) {
    return;
  }

  try {
    if (hasConsent) {
      posthog.opt_in_capturing();
      if (isDevelopment()) {
        console.log("✅ Analytics consent granted");
      }
    } else {
      posthog.opt_out_capturing();
      if (isDevelopment()) {
        console.log("🚫 Analytics consent withdrawn");
      }
    }
  } catch (error) {
    if (isDevelopment()) {
      console.warn("⚠️ Consent change failed:", error);
    }
  }
}

/**
 * Check if analytics is ready
 */
export function isAnalyticsReady(): boolean {
  return isPostHogReady() && hasAnalyticsConsent();
}

/**
 * Get PostHog instance (for advanced usage) - uses initialization manager
 */
export function getPostHogInstance() {
  return getPostHogInstanceFromManager();
}

/**
 * Development-only debug function
 */
export function debugAnalytics(): void {
  if (!isDevelopment()) return;

  console.log("🔍 Analytics Debug Info:");
  console.log("- Initialized:", isPostHogReady());
  console.log("- PostHog exists:", !!posthog);
  console.log("- PostHog loaded:", posthog?.__loaded);
  console.log("- Has consent:", hasAnalyticsConsent());
  console.log("- Distinct ID:", posthog?.get_distinct_id?.());
  console.log("- Session ID:", posthog?.get_session_id?.());
  console.log("- Has opted out:", posthog?.has_opted_out_capturing?.());
  console.log("- API Host:", posthog?.config?.api_host);
  console.log("- API Key:", posthog?.config?.token?.substring(0, 10) + "...");
}

/**
 * Send a test event (development only)
 */
export function sendTestEvent(): void {
  if (!isDevelopment()) return;

  console.log("🧪 Sending test event...");
  trackEvent("cta_click", {
    cta_type: "test_event",
    location: "debug_console",
    text: "Manual Test Event",
  });
}

/**
 * Test PostHog event sending with detailed logging
 */
export function testPostHogEventSending(): void {
  if (!isDevelopment()) return;

  console.log("🧪 Testing PostHog event sending...");

  const instance = getPostHogInstance();
  if (!instance) {
    console.error("❌ PostHog instance not available");
    return;
  }

  console.log("📊 PostHog instance details:", {
    hasCapture: typeof instance.capture === "function",
    distinctId: instance.get_distinct_id?.(),
    sessionId: instance.get_session_id?.(),
    hasOptedOut: instance.has_opted_out_capturing?.(),
    apiHost: instance.config?.api_host,
    apiKey: instance.config?.token?.substring(0, 10) + "...",
  });

  // Force opt-in if needed
  const hasOptedOut = instance.has_opted_out_capturing?.();
  if (hasOptedOut) {
    console.log("📊 PostHog is opted out, forcing opt-in...");
    const optInResult = forceOptInPostHog();
    console.log("📊 Opt-in result:", optInResult);
  }

  // Send test event
  const testEvent = {
    cta_type: "debug_test",
    location: "analytics_debugger",
    text: "Manual Debug Test",
  };

  console.log("📊 Sending test event:", testEvent);
  trackEvent("cta_click", testEvent);

  // Also try direct PostHog capture
  try {
    instance.capture("debug_direct_test", {
      ...testEvent,
      direct_capture: true,
      timestamp: new Date().toISOString(),
    });
    console.log("📊 Direct PostHog capture sent");
  } catch (error) {
    console.error("❌ Direct PostHog capture failed:", error);
  }
}

/**
 * Force opt-in PostHog if it's opted out
 */
export function forceOptInPostHog(): boolean {
  return forceOptInFromManager();
}

// Export PostHog instance for backward compatibility
export { posthog };
