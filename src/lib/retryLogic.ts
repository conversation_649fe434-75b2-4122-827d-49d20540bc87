/**
 * Retry logic utilities for handling network errors and transient failures
 */

import { NetworkError, BusinessErrorHandler, BusinessOperation } from './errorHandling'

export interface RetryOptions {
  maxAttempts?: number
  baseDelay?: number
  maxDelay?: number
  backoffFactor?: number
  retryCondition?: (error: Error) => boolean
  onRetry?: (attempt: number, error: Error) => void
}

export interface RetryResult<T> {
  success: boolean
  data?: T
  error?: Error
  attempts: number
  totalTime: number
}

// Default retry configuration
const DEFAULT_RETRY_OPTIONS: Required<RetryOptions> = {
  maxAttempts: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffFactor: 2,
  retryCondition: (error: Error) => {
    // Retry on network errors, timeouts, and 5xx server errors
    if (error instanceof NetworkError) {
      const statusCode = error.statusCode
      return !statusCode || statusCode >= 500 || statusCode === 408 || statusCode === 429
    }
    
    // Retry on fetch errors (network issues)
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return true
    }
    
    // Retry on timeout errors
    if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
      return true
    }
    
    return false
  },
  onRetry: () => {}, // No-op by default
}

/**
 * Exponential backoff delay calculation
 */
function calculateDelay(attempt: number, options: Required<RetryOptions>): number {
  const delay = options.baseDelay * Math.pow(options.backoffFactor, attempt - 1)
  return Math.min(delay, options.maxDelay)
}

/**
 * Sleep utility for delays
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * Generic retry wrapper for async operations
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
): Promise<RetryResult<T>> {
  const config = { ...DEFAULT_RETRY_OPTIONS, ...options }
  const startTime = Date.now()
  let lastError: Error | undefined
  
  for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
    try {
      const data = await operation()
      return {
        success: true,
        data,
        attempts: attempt,
        totalTime: Date.now() - startTime,
      }
    } catch (error) {
      lastError = error as Error
      
      // Check if we should retry this error
      if (!config.retryCondition(lastError)) {
        break
      }
      
      // Don't delay after the last attempt
      if (attempt < config.maxAttempts) {
        const delay = calculateDelay(attempt, config)
        config.onRetry(attempt, lastError)
        await sleep(delay)
      }
    }
  }
  
  return {
    success: false,
    error: lastError,
    attempts: config.maxAttempts,
    totalTime: Date.now() - startTime,
  }
}

/**
 * Retry wrapper specifically for fetch operations
 */
export async function withFetchRetry<T>(
  fetchOperation: () => Promise<Response>,
  parseResponse: (response: Response) => Promise<T>,
  options: RetryOptions = {}
): Promise<RetryResult<T>> {
  const operation = async (): Promise<T> => {
    const response = await fetchOperation()
    
    // Check for HTTP error status codes
    if (!response.ok) {
      throw new NetworkError(
        `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        response.url
      )
    }
    
    return await parseResponse(response)
  }
  
  return withRetry(operation, {
    ...options,
    retryCondition: (error: Error) => {
      // Custom retry logic for fetch operations
      if (error instanceof NetworkError) {
        const statusCode = error.statusCode
        // Retry on server errors, rate limiting, and timeouts
        return !statusCode || statusCode >= 500 || statusCode === 429 || statusCode === 408
      }
      
      // Use default retry condition for other errors
      return DEFAULT_RETRY_OPTIONS.retryCondition(error)
    },
  })
}

/**
 * Retry wrapper with business operation context
 */
export async function withBusinessRetry<T>(
  operation: () => Promise<T>,
  businessOperation: BusinessOperation,
  entityType: string,
  entityId?: string,
  options: RetryOptions = {}
): Promise<RetryResult<T>> {
  const result = await withRetry(operation, {
    ...options,
    onRetry: (attempt, error) => {
      // Log retry attempts as business breadcrumbs
      BusinessErrorHandler.handleGenericError(
        new Error(`Retry attempt ${attempt} for ${businessOperation}`),
        businessOperation,
        entityType,
        entityId,
        { 
          retryAttempt: attempt, 
          originalError: error.message,
          maxAttempts: options.maxAttempts || DEFAULT_RETRY_OPTIONS.maxAttempts,
        }
      )
      
      // Call custom onRetry if provided
      if (options.onRetry) {
        options.onRetry(attempt, error)
      }
    },
  })
  
  // Log final result
  if (!result.success && result.error) {
    BusinessErrorHandler.handleGenericError(
      result.error,
      businessOperation,
      entityType,
      entityId,
      {
        totalAttempts: result.attempts,
        totalTime: result.totalTime,
        finalFailure: true,
      }
    )
  }
  
  return result
}

/**
 * Circuit breaker pattern for preventing cascading failures
 */
export class CircuitBreaker {
  private failures = 0
  private lastFailureTime = 0
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED'
  
  constructor(
    private readonly failureThreshold: number = 5,
    private readonly recoveryTimeout: number = 60000 // 1 minute
  ) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime < this.recoveryTimeout) {
        throw new Error('Circuit breaker is OPEN - operation not allowed')
      }
      this.state = 'HALF_OPEN'
    }
    
    try {
      const result = await operation()
      this.onSuccess()
      return result
    } catch (error) {
      this.onFailure()
      throw error
    }
  }
  
  private onSuccess(): void {
    this.failures = 0
    this.state = 'CLOSED'
  }
  
  private onFailure(): void {
    this.failures++
    this.lastFailureTime = Date.now()
    
    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN'
    }
  }
  
  getState(): string {
    return this.state
  }
  
  getFailureCount(): number {
    return this.failures
  }
}

/**
 * Pre-configured retry options for common scenarios
 */
export const RETRY_PRESETS = {
  // Quick operations (API calls, form submissions)
  QUICK: {
    maxAttempts: 3,
    baseDelay: 500,
    maxDelay: 2000,
    backoffFactor: 1.5,
  } as RetryOptions,
  
  // Standard operations (data fetching, file uploads)
  STANDARD: {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 5000,
    backoffFactor: 2,
  } as RetryOptions,
  
  // Long operations (large file uploads, batch processing)
  LONG: {
    maxAttempts: 5,
    baseDelay: 2000,
    maxDelay: 30000,
    backoffFactor: 2,
  } as RetryOptions,
  
  // Critical operations (payment processing, booking confirmations)
  CRITICAL: {
    maxAttempts: 5,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 1.8,
  } as RetryOptions,
}

// Export commonly used instances
export const apiCircuitBreaker = new CircuitBreaker(3, 30000) // More aggressive for API calls
export const databaseCircuitBreaker = new CircuitBreaker(5, 60000) // More tolerant for database operations