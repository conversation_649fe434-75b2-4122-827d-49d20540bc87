/**
 * Performance monitoring utilities for Core Web Vitals and business metrics
 */

import { reportPerformanceMetric, reportWebVital, addBreadcrumb } from './sentry';
import type { PerformanceMetrics } from './sentry';

// Core Web Vitals thresholds (in milliseconds, except CLS)
const WEB_VITALS_THRESHOLDS = {
  FCP: { good: 1800, poor: 3000 }, // First Contentful Paint
  LCP: { good: 2500, poor: 4000 }, // Largest Contentful Paint
  FID: { good: 100, poor: 300 },   // First Input Delay
  CLS: { good: 0.1, poor: 0.25 },  // Cumulative Layout Shift
  TTFB: { good: 800, poor: 1800 }, // Time to First Byte
  INP: { good: 200, poor: 500 },   // Interaction to Next Paint
} as const;

// Business performance metrics
export interface BusinessPerformanceMetric {
  name: string;
  duration: number;
  success: boolean;
  entityType?: string;
  entityId?: string;
  metadata?: Record<string, any>;
}

// Performance monitoring class
export class PerformanceMonitor {
  private static measurements = new Map<string, number>();
  private static observers = new Map<string, PerformanceObserver>();

  // Initialize Core Web Vitals monitoring
  static initWebVitalsMonitoring(): void {
    if (typeof window === 'undefined') return;

    // Monitor First Contentful Paint (FCP)
    this.observePerformanceEntry('paint', (entries) => {
      entries.forEach((entry) => {
        if (entry.name === 'first-contentful-paint') {
          this.reportWebVitalMetric('FCP', entry.startTime, entry.entryType);
        }
      });
    });

    // Monitor Largest Contentful Paint (LCP)
    this.observePerformanceEntry('largest-contentful-paint', (entries) => {
      const lastEntry = entries[entries.length - 1];
      if (lastEntry) {
        this.reportWebVitalMetric('LCP', lastEntry.startTime, lastEntry.entryType);
      }
    });

    // Monitor First Input Delay (FID)
    this.observePerformanceEntry('first-input', (entries) => {
      entries.forEach((entry) => {
        const fid = (entry as any).processingStart - entry.startTime;
        this.reportWebVitalMetric('FID', fid, entry.entryType);
      });
    });

    // Monitor Cumulative Layout Shift (CLS)
    this.observePerformanceEntry('layout-shift', (entries) => {
      let clsValue = 0;
      entries.forEach((entry) => {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      });
      if (clsValue > 0) {
        this.reportWebVitalMetric('CLS', clsValue, 'layout-shift');
      }
    });

    // Monitor Navigation Timing for TTFB
    if ('navigation' in performance) {
      const navTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navTiming) {
        const ttfb = navTiming.responseStart - navTiming.requestStart;
        this.reportWebVitalMetric('TTFB', ttfb, 'navigation');
      }
    }

    // Monitor Long Tasks
    this.observePerformanceEntry('longtask', (entries) => {
      entries.forEach((entry) => {
        reportPerformanceMetric({
          name: 'long-task',
          value: entry.duration,
          unit: 'ms',
          tags: {
            type: 'performance-issue',
            threshold: '50ms',
          },
        });
      });
    });
  }

  // Generic performance entry observer
  private static observePerformanceEntry(
    entryType: string,
    callback: (entries: PerformanceEntry[]) => void
  ): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) return;

    try {
      const observer = new PerformanceObserver((list) => {
        callback(list.getEntries());
      });

      observer.observe({ entryTypes: [entryType] });
      this.observers.set(entryType, observer);
    } catch (error) {
      console.warn(`Failed to observe ${entryType}:`, error);
    }
  }

  // Report Web Vital metric with threshold analysis
  private static reportWebVitalMetric(name: keyof typeof WEB_VITALS_THRESHOLDS, value: number, entryType: string): void {
    const thresholds = WEB_VITALS_THRESHOLDS[name];
    const rating = value <= thresholds.good ? 'good' : value <= thresholds.poor ? 'needs-improvement' : 'poor';
    
    reportWebVital(name, value, `${name}-${Date.now()}`);
    
    addBreadcrumb(
      `Core Web Vital: ${name}`,
      'performance',
      rating === 'poor' ? 'warning' : 'info',
      {
        metric: name,
        value,
        rating,
        threshold_good: thresholds.good,
        threshold_poor: thresholds.poor,
        entry_type: entryType,
      }
    );
  }

  // Start timing a business operation
  static startTiming(operationName: string): void {
    this.measurements.set(operationName, performance.now());
    
    addBreadcrumb(
      `Started timing: ${operationName}`,
      'performance',
      'info',
      { operation: operationName, action: 'start' }
    );
  }

  // End timing and report business performance metric
  static endTiming(
    operationName: string,
    success: boolean = true,
    entityType?: string,
    entityId?: string,
    metadata?: Record<string, any>
  ): number {
    const startTime = this.measurements.get(operationName);
    if (!startTime) {
      console.warn(`No start time found for operation: ${operationName}`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.measurements.delete(operationName);

    const businessMetric: BusinessPerformanceMetric = {
      name: operationName,
      duration,
      success,
      entityType,
      entityId,
      metadata,
    };

    this.reportBusinessPerformanceMetric(businessMetric);
    return duration;
  }

  // Report business performance metric
  static reportBusinessPerformanceMetric(metric: BusinessPerformanceMetric): void {
    const performanceMetric: PerformanceMetrics = {
      name: `business.${metric.name}`,
      value: metric.duration,
      unit: 'ms',
      tags: {
        success: metric.success.toString(),
        entity_type: metric.entityType || 'unknown',
        entity_id: metric.entityId || 'unknown',
        ...metric.metadata,
      },
    };

    reportPerformanceMetric(performanceMetric);

    addBreadcrumb(
      `Business operation completed: ${metric.name}`,
      'performance',
      metric.success ? 'info' : 'warning',
      {
        operation: metric.name,
        duration: metric.duration,
        success: metric.success,
        entity_type: metric.entityType,
        entity_id: metric.entityId,
        ...metric.metadata,
      }
    );
  }

  // Monitor API call performance
  static async monitorAPICall<T>(
    apiName: string,
    apiCall: () => Promise<T>,
    endpoint?: string,
    method: string = 'GET'
  ): Promise<T> {
    const startTime = performance.now();
    let success = false;
    let statusCode: number | undefined;
    let error: Error | undefined;

    try {
      const result = await apiCall();
      success = true;
      statusCode = 200; // Assume success if no error
      return result;
    } catch (err) {
      error = err as Error;
      // Try to extract status code from error if available
      if (err && typeof err === 'object' && 'status' in err) {
        statusCode = (err as any).status;
      }
      throw err;
    } finally {
      const duration = performance.now() - startTime;

      reportPerformanceMetric({
        name: `api.${apiName}`,
        value: duration,
        unit: 'ms',
        tags: {
          endpoint: endpoint || 'unknown',
          method,
          success: success.toString(),
          status_code: statusCode?.toString() || 'unknown',
        },
      });

      addBreadcrumb(
        `API call: ${method} ${apiName}`,
        'api',
        success ? 'info' : 'error',
        {
          api_name: apiName,
          endpoint,
          method,
          duration,
          success,
          status_code: statusCode,
          error_message: error?.message,
        }
      );
    }
  }

  // Monitor component render performance
  static monitorComponentRender(componentName: string, renderFn: () => void): void {
    const startTime = performance.now();
    
    try {
      renderFn();
      const duration = performance.now() - startTime;

      if (duration > 16) { // More than one frame (60fps)
        reportPerformanceMetric({
          name: `component.render.${componentName}`,
          value: duration,
          unit: 'ms',
          tags: {
            component: componentName,
            type: 'render',
            slow: 'true',
          },
        });

        addBreadcrumb(
          `Slow component render: ${componentName}`,
          'performance',
          'warning',
          {
            component: componentName,
            duration,
            threshold: 16,
          }
        );
      }
    } catch (error) {
      const duration = performance.now() - startTime;
      
      addBreadcrumb(
        `Component render error: ${componentName}`,
        'performance',
        'error',
        {
          component: componentName,
          duration,
          error: (error as Error).message,
        }
      );
      
      throw error;
    }
  }

  // Monitor memory usage
  static monitorMemoryUsage(): void {
    if (typeof window === 'undefined' || !('memory' in performance)) return;

    const memory = (performance as any).memory;
    if (memory) {
      reportPerformanceMetric({
        name: 'memory.used',
        value: memory.usedJSHeapSize,
        unit: 'bytes',
        tags: {
          type: 'memory',
          total: memory.totalJSHeapSize.toString(),
          limit: memory.jsHeapSizeLimit.toString(),
        },
      });

      // Alert if memory usage is high
      const memoryUsagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
      if (memoryUsagePercent > 80) {
        addBreadcrumb(
          'High memory usage detected',
          'performance',
          'warning',
          {
            used: memory.usedJSHeapSize,
            total: memory.totalJSHeapSize,
            limit: memory.jsHeapSizeLimit,
            usage_percent: memoryUsagePercent,
          }
        );
      }
    }
  }

  // Clean up observers
  static cleanup(): void {
    this.observers.forEach((observer) => {
      observer.disconnect();
    });
    this.observers.clear();
    this.measurements.clear();
  }
}

// Utility functions for easy performance monitoring
export const startTiming = (operationName: string): void => {
  PerformanceMonitor.startTiming(operationName);
};

export const endTiming = (
  operationName: string,
  success: boolean = true,
  entityType?: string,
  entityId?: string,
  metadata?: Record<string, any>
): number => {
  return PerformanceMonitor.endTiming(operationName, success, entityType, entityId, metadata);
};

export const monitorAPICall = <T>(
  apiName: string,
  apiCall: () => Promise<T>,
  endpoint?: string,
  method: string = 'GET'
): Promise<T> => {
  return PerformanceMonitor.monitorAPICall(apiName, apiCall, endpoint, method);
};

export const initPerformanceMonitoring = (): void => {
  PerformanceMonitor.initWebVitalsMonitoring();
  
  // Monitor memory usage every 30 seconds
  if (typeof window !== 'undefined') {
    setInterval(() => {
      PerformanceMonitor.monitorMemoryUsage();
    }, 30000);
  }
};

// Export the performance monitor class
export default PerformanceMonitor;