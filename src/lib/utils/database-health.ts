/**
 * Database health check utilities
 */

import { checkDatabaseConnection, DatabaseUtils, type ConnectionStatusExtended } from '../supabase';
import { isDevelopment } from '../env';

export interface HealthCheckResult {
  isHealthy: boolean;
  connection: ConnectionStatusExtended;
  tables: {
    name: string;
    exists: boolean;
    count: number;
  }[];
  timestamp: Date;
  responseTime: number;
}

/**
 * Performs a comprehensive database health check
 */
export async function performDatabaseHealthCheck(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    // Check basic connection
    const connectionStatus = await checkDatabaseConnection();
    const connection: ConnectionStatusExtended = {
      isConnected: connectionStatus === 'connected',
      error: connectionStatus === 'error' ? 'Database connection error' : undefined,
      timestamp: new Date(),
    };
    
    if (!connection.isConnected) {
      return {
        isHealthy: false,
        connection,
        tables: [],
        timestamp: new Date(),
        responseTime: Date.now() - startTime,
      };
    }

    // Check required tables
    const requiredTables = [
      'properties',
      'bookings',
      'social_posts',
      'blog_articles',
      'availability_calendar',
      'contact_submissions',
    ];

    const tableChecks = await Promise.all(
      requiredTables.map(async (tableName) => {
        try {
          const exists = await DatabaseUtils.tableExists(tableName);
          const count = exists ? await DatabaseUtils.getTableCount(tableName) : 0;
          
          return {
            name: tableName,
            exists,
            count,
          };
        } catch (error) {
          if (isDevelopment()) {
            console.warn(`Error checking table ${tableName}:`, error);
          }
          return {
            name: tableName,
            exists: false,
            count: 0,
          };
        }
      })
    );

    const allTablesExist = tableChecks.every(table => table.exists);
    const responseTime = Date.now() - startTime;

    const result: HealthCheckResult = {
      isHealthy: connection.isConnected && allTablesExist,
      connection,
      tables: tableChecks,
      timestamp: new Date(),
      responseTime,
    };

    if (isDevelopment()) {
      console.log('🏥 Database health check completed:', {
        healthy: result.isHealthy,
        responseTime: `${responseTime}ms`,
        tablesFound: tableChecks.filter(t => t.exists).length,
        totalTables: requiredTables.length,
      });
    }

    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    if (isDevelopment()) {
      console.error('❌ Database health check failed:', error);
    }

    return {
      isHealthy: false,
      connection: {
        isConnected: false,
        error: errorMessage,
        timestamp: new Date(),
      },
      tables: [],
      timestamp: new Date(),
      responseTime: Date.now() - startTime,
    };
  }
}

/**
 * Quick database connectivity check
 */
export async function quickHealthCheck(): Promise<boolean> {
  try {
    const connection = await checkDatabaseConnection();
    return connection === 'connected';
  } catch {
    return false;
  }
}

/**
 * Get database statistics summary
 */
export async function getDatabaseStats(): Promise<{
  totalRecords: number;
  tableStats: Record<string, number>;
  lastChecked: Date;
}> {
  const tables = [
    'properties',
    'bookings',
    'social_posts',
    'blog_articles',
    'availability_calendar',
    'contact_submissions',
  ];

  const tableStats: Record<string, number> = {};
  let totalRecords = 0;

  await Promise.all(
    tables.map(async (tableName) => {
      try {
        const count = await DatabaseUtils.getTableCount(tableName);
        tableStats[tableName] = count;
        totalRecords += count;
      } catch (error) {
        if (isDevelopment()) {
          console.warn(`Error getting count for ${tableName}:`, error);
        }
        tableStats[tableName] = 0;
      }
    })
  );

  return {
    totalRecords,
    tableStats,
    lastChecked: new Date(),
  };
}

/**
 * Validates database schema integrity
 */
export async function validateDatabaseSchema(): Promise<{
  isValid: boolean;
  missingTables: string[];
  errors: string[];
}> {
  const requiredTables = [
    'properties',
    'bookings',
    'social_posts',
    'blog_articles',
    'availability_calendar',
    'contact_submissions',
  ];

  const missingTables: string[] = [];
  const errors: string[] = [];

  try {
    for (const tableName of requiredTables) {
      const exists = await DatabaseUtils.tableExists(tableName);
      if (!exists) {
        missingTables.push(tableName);
      }
    }

    if (missingTables.length > 0) {
      errors.push(`Missing required tables: ${missingTables.join(', ')}`);
    }

    return {
      isValid: missingTables.length === 0 && errors.length === 0,
      missingTables,
      errors,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    errors.push(`Schema validation failed: ${errorMessage}`);
    
    return {
      isValid: false,
      missingTables,
      errors,
    };
  }
}