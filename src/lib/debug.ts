/**
 * Development debug utilities
 * Makes analytics debug functions available globally in development mode
 */

import { isDevelopment } from './env';
import { 
  debugAnalytics, 
  sendTestEvent, 
  testPostHogEventSending, 
  forceOptInPostHog,
  getPostHogInstance,
  isAnalyticsReady
} from './analytics';
import { testPostHogDirect, checkPostHogConnectivity, flushPostHogEvents } from './test-posthog-direct';
import { initSimplePostHog, sendSimpleTestEvent, getSimplePostHogStatus } from './simple-posthog';

/**
 * Initialize debug utilities in development mode
 */
export function initializeDebugUtils(): void {
  if (!isDevelopment() || typeof window === 'undefined') {
    return;
  }

  // Add debug functions to window object
  (window as any).jaDebug = {
    // Analytics debug functions
    debugAnalytics,
    sendTestEvent,
    testPostHogEventSending,
    forceOptInPostHog,
    getPostHogInstance,
    isAnalyticsReady,
    testPostHogDirect,
    checkPostHogConnectivity,
    flushPostHogEvents,
    initSimplePostHog,
    sendSimpleTestEvent,
    getSimplePostHogStatus,
    
    // Utility functions
    checkPostHogStatus: () => {
      const instance = getPostHogInstance();
      return {
        ready: isAnalyticsReady(),
        instance: !!instance,
        distinctId: instance?.get_distinct_id?.(),
        sessionId: instance?.get_session_id?.(),
        hasOptedOut: instance?.has_opted_out_capturing?.(),
        apiHost: instance?.config?.api_host,
        apiKey: instance?.config?.token?.substring(0, 10) + '...'
      };
    },
    
    // Quick test function
    quickTest: () => {
      console.log('🧪 Running quick PostHog test...');
      testPostHogEventSending();
      return 'Check console for results';
    }
  };

  console.log('🛠️ Debug utilities loaded. Use window.jaDebug to access debug functions.');
  console.log('Available functions:', Object.keys((window as any).jaDebug));
}