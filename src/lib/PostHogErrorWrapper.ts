/**
 * Comprehensive PostHog error handling wrapper
 * Provides graceful degradation and enhanced error monitoring for PostHog operations
 */

import { wrapExternalService } from './AsyncOperationWrapper';
import { createValidationError } from './errorHandling';
import { universalErrorWrapper } from './UniversalErrorWrapper';
import { isDevelopment } from './env';

export interface PostHogOperationOptions {
  timeout?: number;
  retries?: number;
  gracefulDegradation?: boolean;
  logErrors?: boolean;
}

export interface PostHogHealthStatus {
  isHealthy: boolean;
  lastCheck: number;
  consecutiveFailures: number;
  lastError?: string;
}

/**
 * Enhanced PostHog wrapper with comprehensive error handling and graceful degradation
 */
export class PostHogErrorWrapper {
  private static healthStatus: PostHogHealthStatus = {
    isHealthy: true,
    lastCheck: 0,
    consecutiveFailures: 0,
  };

  private static readonly MAX_CONSECUTIVE_FAILURES = 5;
  private static readonly HEALTH_CHECK_INTERVAL = 60000; // 1 minute
  private static readonly CIRCUIT_BREAKER_TIMEOUT = 300000; // 5 minutes

  /**
   * Wrap PostHog capture operation with enhanced error handling
   */
  static async wrapCapture(
    eventName: string,
    properties: Record<string, any>,
    posthogInstance: any,
    options: PostHogOperationOptions = {}
  ): Promise<boolean> {
    const {
      timeout = 5000,
      retries = 2,
      gracefulDegradation = true,
      logErrors = true,
    } = options;

    // Check circuit breaker
    if (!this.isServiceHealthy()) {
      if (logErrors && isDevelopment()) {
        console.warn('PostHog service is unhealthy, skipping event capture');
      }
      return false;
    }

    // Validate inputs
    if (!eventName || typeof eventName !== 'string') {
      if (gracefulDegradation) {
        if (logErrors) {
          console.warn('Invalid event name for PostHog capture, skipping');
        }
        return false;
      }
      throw createValidationError('Event name is required and must be a string', 'eventName', eventName);
    }

    if (!properties || typeof properties !== 'object') {
      if (gracefulDegradation) {
        if (logErrors) {
          console.warn('Invalid properties for PostHog capture, using empty object');
        }
        properties = {};
      } else {
        throw createValidationError('Properties must be an object', 'properties', properties);
      }
    }

    try {
      await wrapExternalService(
        'posthog',
        'capture_event',
        async () => {
          return new Promise<void>((resolve, reject) => {
            try {
              // Add timeout to PostHog operation
              const timeoutId = setTimeout(() => {
                reject(new Error(`PostHog capture timeout after ${timeout}ms`));
              }, timeout);

              // Capture event
              posthogInstance.capture(eventName, {
                ...properties,
                timestamp: new Date().toISOString(),
                _wrapper_version: '1.0.0',
              });

              clearTimeout(timeoutId);
              resolve();
            } catch (error) {
              reject(error);
            }
          });
        },
        {
          timeout,
          retryConfig: {
            maxRetries: retries,
            baseDelay: 1000,
            maxDelay: 10000,
            exponentialBackoff: true,
            retryCondition: (error: Error) => {
              const message = error.message.toLowerCase();
              return message.includes('timeout') ||
                     message.includes('network') ||
                     message.includes('connection') ||
                     message.includes('rate limit');
            }
          },
          context: {
            page: {
              url: typeof window !== 'undefined' ? window.location.href : '',
              section: 'analytics',
              component: 'posthog'
            },
            browser: {
              name: typeof window !== 'undefined' ? navigator.userAgent : 'server',
              version: 'unknown',
              userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server'
            },
            technical: {
              requestId: `posthog_capture_${Date.now()}`,
              apiEndpoint: 'capture',
            },
            business: {
              operation: 'analytics_capture',
              entityType: 'analytics_event',
              workflow: 'user_tracking'
            }
          }
        }
      );

      // Record successful operation
      this.recordSuccess();
      universalErrorWrapper.recordUserJourneyStep('posthog_capture_success');
      
      return true;

    } catch (error) {
      // Record failure
      this.recordFailure(error as Error);
      universalErrorWrapper.recordUserJourneyStep('posthog_capture_failed');

      if (gracefulDegradation) {
        if (logErrors) {
          console.error('PostHog capture failed, continuing gracefully:', error);
        }
        return false;
      }

      throw error;
    }
  }

  /**
   * Wrap PostHog identify operation with enhanced error handling
   */
  static async wrapIdentify(
    userId: string,
    properties: Record<string, any> = {},
    posthogInstance: any,
    options: PostHogOperationOptions = {}
  ): Promise<boolean> {
    const {
      timeout = 5000,
      retries = 2,
      gracefulDegradation = true,
      logErrors = true,
    } = options;

    // Check circuit breaker
    if (!this.isServiceHealthy()) {
      if (logErrors && isDevelopment()) {
        console.warn('PostHog service is unhealthy, skipping user identification');
      }
      return false;
    }

    // Validate inputs
    if (!userId || typeof userId !== 'string') {
      if (gracefulDegradation) {
        if (logErrors) {
          console.warn('Invalid user ID for PostHog identify, skipping');
        }
        return false;
      }
      throw createValidationError('User ID is required and must be a string', 'userId', userId);
    }

    try {
      await wrapExternalService(
        'posthog',
        'identify_user',
        async () => {
          return new Promise<void>((resolve, reject) => {
            try {
              // Add timeout to PostHog operation
              const timeoutId = setTimeout(() => {
                reject(new Error(`PostHog identify timeout after ${timeout}ms`));
              }, timeout);

              // Identify user
              posthogInstance.identify(userId, {
                ...properties,
                _identified_at: new Date().toISOString(),
                _wrapper_version: '1.0.0',
              });

              clearTimeout(timeoutId);
              resolve();
            } catch (error) {
              reject(error);
            }
          });
        },
        {
          timeout,
          retryConfig: {
            maxRetries: retries,
            baseDelay: 1000,
            maxDelay: 10000,
            exponentialBackoff: true,
            retryCondition: (error: Error) => {
              const message = error.message.toLowerCase();
              return message.includes('timeout') ||
                     message.includes('network') ||
                     message.includes('connection');
            }
          },
          context: {
            page: {
              url: typeof window !== 'undefined' ? window.location.href : '',
              section: 'analytics',
              component: 'posthog'
            },
            browser: {
              name: typeof window !== 'undefined' ? navigator.userAgent : 'server',
              version: 'unknown',
              userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server'
            },
            user: {
              id: userId,
            },
            technical: {
              requestId: `posthog_identify_${Date.now()}`,
              apiEndpoint: 'identify',
            },
            business: {
              operation: 'user_identification',
              entityType: 'user',
              entityId: userId,
              workflow: 'user_tracking'
            }
          }
        }
      );

      // Record successful operation
      this.recordSuccess();
      universalErrorWrapper.recordUserJourneyStep('posthog_identify_success');
      
      return true;

    } catch (error) {
      // Record failure
      this.recordFailure(error as Error);
      universalErrorWrapper.recordUserJourneyStep('posthog_identify_failed');

      if (gracefulDegradation) {
        if (logErrors) {
          console.error('PostHog identify failed, continuing gracefully:', error);
        }
        return false;
      }

      throw error;
    }
  }

  /**
   * Wrap PostHog people.set operation with enhanced error handling
   */
  static async wrapPeopleSet(
    properties: Record<string, any>,
    posthogInstance: any,
    options: PostHogOperationOptions = {}
  ): Promise<boolean> {
    const {
      timeout = 5000,
      retries = 2,
      gracefulDegradation = true,
      logErrors = true,
    } = options;

    // Check circuit breaker
    if (!this.isServiceHealthy()) {
      if (logErrors && isDevelopment()) {
        console.warn('PostHog service is unhealthy, skipping people.set');
      }
      return false;
    }

    // Validate inputs
    if (!properties || typeof properties !== 'object' || Object.keys(properties).length === 0) {
      if (gracefulDegradation) {
        if (logErrors) {
          console.warn('Invalid or empty properties for PostHog people.set, skipping');
        }
        return false;
      }
      throw createValidationError('Properties must be a non-empty object', 'properties', properties);
    }

    try {
      await wrapExternalService(
        'posthog',
        'people_set',
        async () => {
          return new Promise<void>((resolve, reject) => {
            try {
              // Add timeout to PostHog operation
              const timeoutId = setTimeout(() => {
                reject(new Error(`PostHog people.set timeout after ${timeout}ms`));
              }, timeout);

              // Set user properties
              posthogInstance.people.set({
                ...properties,
                _updated_at: new Date().toISOString(),
                _wrapper_version: '1.0.0',
              });

              clearTimeout(timeoutId);
              resolve();
            } catch (error) {
              reject(error);
            }
          });
        },
        {
          timeout,
          retryConfig: {
            maxRetries: retries,
            baseDelay: 1000,
            maxDelay: 10000,
            exponentialBackoff: true,
            retryCondition: (error: Error) => {
              const message = error.message.toLowerCase();
              return message.includes('timeout') ||
                     message.includes('network') ||
                     message.includes('connection');
            }
          },
          context: {
            page: {
              url: typeof window !== 'undefined' ? window.location.href : '',
              section: 'analytics',
              component: 'posthog'
            },
            browser: {
              name: typeof window !== 'undefined' ? navigator.userAgent : 'server',
              version: 'unknown',
              userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server'
            },
            technical: {
              requestId: `posthog_people_set_${Date.now()}`,
              apiEndpoint: 'people.set',
            },
            business: {
              operation: 'user_properties_update',
              entityType: 'user_properties',
              workflow: 'user_tracking'
            }
          }
        }
      );

      // Record successful operation
      this.recordSuccess();
      universalErrorWrapper.recordUserJourneyStep('posthog_people_set_success');
      
      return true;

    } catch (error) {
      // Record failure
      this.recordFailure(error as Error);
      universalErrorWrapper.recordUserJourneyStep('posthog_people_set_failed');

      if (gracefulDegradation) {
        if (logErrors) {
          console.error('PostHog people.set failed, continuing gracefully:', error);
        }
        return false;
      }

      throw error;
    }
  }

  /**
   * Wrap PostHog reset operation with enhanced error handling
   */
  static async wrapReset(
    posthogInstance: any,
    options: PostHogOperationOptions = {}
  ): Promise<boolean> {
    const {
      timeout = 3000,
      retries = 1,
      gracefulDegradation = true,
      logErrors = true,
    } = options;

    try {
      await wrapExternalService(
        'posthog',
        'reset_user',
        async () => {
          return new Promise<void>((resolve, reject) => {
            try {
              // Add timeout to PostHog operation
              const timeoutId = setTimeout(() => {
                reject(new Error(`PostHog reset timeout after ${timeout}ms`));
              }, timeout);

              // Reset user session
              posthogInstance.reset();

              clearTimeout(timeoutId);
              resolve();
            } catch (error) {
              reject(error);
            }
          });
        },
        {
          timeout,
          retryConfig: {
            maxRetries: retries,
            baseDelay: 1000,
            maxDelay: 10000,
            exponentialBackoff: true,
            retryCondition: (error: Error) => {
              const message = error.message.toLowerCase();
              return message.includes('timeout') ||
                     message.includes('network');
            }
          },
          context: {
            page: {
              url: typeof window !== 'undefined' ? window.location.href : '',
              section: 'analytics',
              component: 'posthog'
            },
            browser: {
              name: typeof window !== 'undefined' ? navigator.userAgent : 'server',
              version: 'unknown',
              userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server'
            },
            technical: {
              requestId: `posthog_reset_${Date.now()}`,
              apiEndpoint: 'reset',
            },
            business: {
              operation: 'user_session_reset',
              entityType: 'user_session',
              workflow: 'user_tracking'
            }
          }
        }
      );

      // Record successful operation
      this.recordSuccess();
      universalErrorWrapper.recordUserJourneyStep('posthog_reset_success');
      
      return true;

    } catch (error) {
      // Record failure
      this.recordFailure(error as Error);
      universalErrorWrapper.recordUserJourneyStep('posthog_reset_failed');

      if (gracefulDegradation) {
        if (logErrors) {
          console.error('PostHog reset failed, continuing gracefully:', error);
        }
        return false;
      }

      throw error;
    }
  }

  /**
   * Wrap PostHog opt-out operation with enhanced error handling
   */
  static async wrapOptOut(
    posthogInstance: any,
    options: PostHogOperationOptions = {}
  ): Promise<boolean> {
    const {
      timeout = 3000,
      retries = 1,
      gracefulDegradation = true,
      logErrors = true,
    } = options;

    try {
      await wrapExternalService(
        'posthog',
        'opt_out',
        async () => {
          return new Promise<void>((resolve, reject) => {
            try {
              // Add timeout to PostHog operation
              const timeoutId = setTimeout(() => {
                reject(new Error(`PostHog opt-out timeout after ${timeout}ms`));
              }, timeout);

              // Opt out of tracking
              posthogInstance.opt_out_capturing();

              clearTimeout(timeoutId);
              resolve();
            } catch (error) {
              reject(error);
            }
          });
        },
        {
          timeout,
          retryConfig: {
            maxRetries: retries,
            baseDelay: 1000,
            maxDelay: 10000,
            exponentialBackoff: true,
            retryCondition: () => false, // Don't retry opt-out operations
          },
          context: {
            page: {
              url: typeof window !== 'undefined' ? window.location.href : '',
              section: 'analytics',
              component: 'posthog'
            },
            browser: {
              name: typeof window !== 'undefined' ? navigator.userAgent : 'server',
              version: 'unknown',
              userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server'
            },
            technical: {
              requestId: `posthog_opt_out_${Date.now()}`,
              apiEndpoint: 'opt_out_capturing',
            },
            business: {
              operation: 'user_opt_out',
              entityType: 'user_consent',
              workflow: 'privacy_management'
            }
          }
        }
      );

      // Record successful operation
      this.recordSuccess();
      universalErrorWrapper.recordUserJourneyStep('posthog_opt_out_success');
      
      return true;

    } catch (error) {
      // Record failure
      this.recordFailure(error as Error);
      universalErrorWrapper.recordUserJourneyStep('posthog_opt_out_failed');

      if (gracefulDegradation) {
        if (logErrors) {
          console.error('PostHog opt-out failed, continuing gracefully:', error);
        }
        return false;
      }

      throw error;
    }
  }

  /**
   * Wrap PostHog opt-in operation with enhanced error handling
   */
  static async wrapOptIn(
    posthogInstance: any,
    options: PostHogOperationOptions = {}
  ): Promise<boolean> {
    const {
      timeout = 3000,
      retries = 1,
      gracefulDegradation = true,
      logErrors = true,
    } = options;

    try {
      await wrapExternalService(
        'posthog',
        'opt_in',
        async () => {
          return new Promise<void>((resolve, reject) => {
            try {
              // Add timeout to PostHog operation
              const timeoutId = setTimeout(() => {
                reject(new Error(`PostHog opt-in timeout after ${timeout}ms`));
              }, timeout);

              // Opt in to tracking
              posthogInstance.opt_in_capturing();

              clearTimeout(timeoutId);
              resolve();
            } catch (error) {
              reject(error);
            }
          });
        },
        {
          timeout,
          retryConfig: {
            maxRetries: retries,
            baseDelay: 1000,
            maxDelay: 10000,
            exponentialBackoff: true,
            retryCondition: () => false, // Don't retry opt-in operations
          },
          context: {
            page: {
              url: typeof window !== 'undefined' ? window.location.href : '',
              section: 'analytics',
              component: 'posthog'
            },
            browser: {
              name: typeof window !== 'undefined' ? navigator.userAgent : 'server',
              version: 'unknown',
              userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server'
            },
            technical: {
              requestId: `posthog_opt_in_${Date.now()}`,
              apiEndpoint: 'opt_in_capturing',
            },
            business: {
              operation: 'user_opt_in',
              entityType: 'user_consent',
              workflow: 'privacy_management'
            }
          }
        }
      );

      // Record successful operation
      this.recordSuccess();
      universalErrorWrapper.recordUserJourneyStep('posthog_opt_in_success');
      
      return true;

    } catch (error) {
      // Record failure
      this.recordFailure(error as Error);
      universalErrorWrapper.recordUserJourneyStep('posthog_opt_in_failed');

      if (gracefulDegradation) {
        if (logErrors) {
          console.error('PostHog opt-in failed, continuing gracefully:', error);
        }
        return false;
      }

      throw error;
    }
  }

  /**
   * Check if PostHog service is healthy
   */
  static isServiceHealthy(): boolean {
    const now = Date.now();
    
    // If we haven't checked recently, assume healthy
    if (now - this.healthStatus.lastCheck > this.HEALTH_CHECK_INTERVAL) {
      return true;
    }

    // If we have too many consecutive failures, consider unhealthy
    if (this.healthStatus.consecutiveFailures >= this.MAX_CONSECUTIVE_FAILURES) {
      // Check if enough time has passed to reset the circuit breaker
      if (now - this.healthStatus.lastCheck > this.CIRCUIT_BREAKER_TIMEOUT) {
        this.resetHealthStatus();
        return true;
      }
      return false;
    }

    return this.healthStatus.isHealthy;
  }

  /**
   * Record successful PostHog operation
   */
  private static recordSuccess(): void {
    this.healthStatus = {
      isHealthy: true,
      lastCheck: Date.now(),
      consecutiveFailures: 0,
    };
  }

  /**
   * Record failed PostHog operation
   */
  private static recordFailure(error: Error): void {
    this.healthStatus = {
      isHealthy: false,
      lastCheck: Date.now(),
      consecutiveFailures: this.healthStatus.consecutiveFailures + 1,
      lastError: error.message,
    };

    // Log circuit breaker activation
    if (this.healthStatus.consecutiveFailures === this.MAX_CONSECUTIVE_FAILURES) {
      console.warn('PostHog circuit breaker activated due to consecutive failures');
      universalErrorWrapper.recordUserJourneyStep('posthog_circuit_breaker_activated');
    }
  }

  /**
   * Reset health status (for testing or manual recovery)
   */
  static resetHealthStatus(): void {
    this.healthStatus = {
      isHealthy: true,
      lastCheck: 0,
      consecutiveFailures: 0,
    };
  }

  /**
   * Get current health status
   */
  static getHealthStatus(): PostHogHealthStatus {
    return { ...this.healthStatus };
  }

  /**
   * Perform health check by attempting a simple operation
   */
  static async performHealthCheck(posthogInstance: any): Promise<boolean> {
    try {
      // Attempt a simple capture operation with minimal data
      const success = await this.wrapCapture(
        '_health_check',
        { timestamp: Date.now() },
        posthogInstance,
        {
          timeout: 3000,
          retries: 1,
          gracefulDegradation: true,
          logErrors: false,
        }
      );

      if (success) {
        this.recordSuccess();
        universalErrorWrapper.recordUserJourneyStep('posthog_health_check_success');
      } else {
        this.recordFailure(new Error('Health check failed'));
        universalErrorWrapper.recordUserJourneyStep('posthog_health_check_failed');
      }

      return success;
    } catch (error) {
      this.recordFailure(error as Error);
      universalErrorWrapper.recordUserJourneyStep('posthog_health_check_error');
      return false;
    }
  }
}

// Export convenience functions
export const wrapPostHogCapture = PostHogErrorWrapper.wrapCapture.bind(PostHogErrorWrapper);
export const wrapPostHogIdentify = PostHogErrorWrapper.wrapIdentify.bind(PostHogErrorWrapper);
export const wrapPostHogPeopleSet = PostHogErrorWrapper.wrapPeopleSet.bind(PostHogErrorWrapper);
export const wrapPostHogReset = PostHogErrorWrapper.wrapReset.bind(PostHogErrorWrapper);
export const wrapPostHogOptOut = PostHogErrorWrapper.wrapOptOut.bind(PostHogErrorWrapper);
export const wrapPostHogOptIn = PostHogErrorWrapper.wrapOptIn.bind(PostHogErrorWrapper);