/**
 * Error handling middleware for Next.js applications
 */

import { NextRequest, NextResponse } from 'next/server'
import * as Sentry from '@sentry/nextjs'
import { 
  NetworkError, 
  ValidationError, 
  DatabaseError, 
  BusinessLogicError,
  BusinessErrorHandler,
  BusinessOperation,
  ErrorCategory,
  ErrorSeverity 
} from '../errorHandling'

// Error response interface
interface ErrorResponse {
  error: {
    message: string
    code: string
    category: string
    severity: string
    timestamp: string
    requestId: string
  }
  details?: any
}

// Request context for error tracking
interface RequestContext {
  requestId: string
  method: string
  url: string
  userAgent?: string
  ip?: string
  userId?: string
}

/**
 * Generate unique request ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Extract request context for error reporting
 */
function extractRequestContext(request: NextRequest): RequestContext {
  return {
    requestId: generateRequestId(),
    method: request.method,
    url: request.url,
    userAgent: request.headers.get('user-agent') || undefined,
    ip: request.headers.get('x-forwarded-for') || 
        request.headers.get('x-real-ip') || 
        'unknown',
    // Extract user ID from auth headers if available
    userId: request.headers.get('x-user-id') || undefined,
  }
}

/**
 * Create standardized error response
 */
function createErrorResponse(
  error: Error,
  context: RequestContext,
  includeDetails = false
): NextResponse<ErrorResponse> {
  let statusCode = 500
  let category = ErrorCategory.BUSINESS
  let severity = ErrorSeverity.HIGH
  let code = 'INTERNAL_ERROR'

  // Determine response based on error type
  if (error instanceof ValidationError) {
    statusCode = 400
    category = ErrorCategory.VALIDATION
    severity = ErrorSeverity.MEDIUM
    code = error.code
  } else if (error instanceof NetworkError) {
    statusCode = error.statusCode || 502
    category = ErrorCategory.NETWORK
    severity = error.statusCode && error.statusCode >= 500 ? ErrorSeverity.HIGH : ErrorSeverity.MEDIUM
    code = error.code
  } else if (error instanceof DatabaseError) {
    statusCode = 503
    category = ErrorCategory.DATABASE
    severity = ErrorSeverity.HIGH
    code = error.code
  } else if (error instanceof BusinessLogicError) {
    statusCode = 422
    category = ErrorCategory.BUSINESS
    severity = ErrorSeverity.HIGH
    code = error.code
  }

  const errorResponse: ErrorResponse = {
    error: {
      message: error.message,
      code,
      category,
      severity,
      timestamp: new Date().toISOString(),
      requestId: context.requestId,
    },
  }

  // Include error details in development
  if (includeDetails && process.env.NODE_ENV === 'development') {
    errorResponse.details = {
      stack: error.stack,
      name: error.name,
      context,
    }
  }

  return NextResponse.json(errorResponse, { status: statusCode })
}

/**
 * Main error handling middleware
 */
export function errorMiddleware(
  handler: (request: NextRequest, context: RequestContext) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const context = extractRequestContext(request)
    
    try {
      // Add request context to Sentry scope
      Sentry.withScope((scope) => {
        scope.setTag('requestId', context.requestId)
        scope.setContext('request', {
          method: context.method,
          url: context.url,
          userAgent: context.userAgent,
          ip: context.ip,
        })
        
        if (context.userId) {
          scope.setUser({ id: context.userId })
        }
      })

      return await handler(request, context)
    } catch (error) {
      const err = error as Error
      
      // Report error with full context
      Sentry.withScope((scope) => {
        scope.setTag('requestId', context.requestId)
        scope.setTag('errorMiddleware', true)
        scope.setContext('request', {
          requestId: context.requestId,
          method: context.method,
          url: context.url,
          userAgent: context.userAgent,
          ip: context.ip
        })
        
        if (err instanceof BusinessLogicError) {
          scope.setTag('businessOperation', err.operation)
          scope.setContext('businessError', {
            operation: err.operation,
            entityId: err.entityId,
            entityType: err.entityType,
          })
        }
        
        Sentry.captureException(err)
      })

      // Log error for server-side debugging
      console.error(`[${context.requestId}] ${err.name}: ${err.message}`, {
        context,
        stack: err.stack,
      })

      return createErrorResponse(err, context, true)
    }
  }
}

/**
 * API route error wrapper
 */
export function withErrorHandling<T = any>(
  handler: (request: NextRequest, context: RequestContext) => Promise<NextResponse<T>>
) {
  return errorMiddleware(handler)
}

/**
 * Business operation wrapper for API routes
 */
export function withBusinessOperation<T = any>(
  operation: BusinessOperation,
  entityType: string,
  handler: (request: NextRequest, context: RequestContext, entityId?: string) => Promise<NextResponse<T>>
) {
  return errorMiddleware(async (request: NextRequest, context: RequestContext) => {
    // Extract entity ID from URL or request body if available
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/').filter(Boolean)
    const entityId = pathSegments[pathSegments.length - 1] || undefined

    try {
      return await BusinessErrorHandler.wrapBusinessOperation(
        operation,
        entityType,
        () => handler(request, context, entityId),
        entityId,
        { requestId: context.requestId }
      )
    } catch (error) {
      // Error is already handled by BusinessErrorHandler, just re-throw
      throw error
    }
  })
}

/**
 * Validation middleware for request data
 */
export function withValidation<T>(
  validator: (data: any) => T | Promise<T>,
  handler: (request: NextRequest, context: RequestContext, validatedData: T) => Promise<NextResponse>
) {
  return errorMiddleware(async (request: NextRequest, context: RequestContext) => {
    try {
      let data: any

      // Parse request data based on content type
      const contentType = request.headers.get('content-type') || ''
      
      if (contentType.includes('application/json')) {
        data = await request.json()
      } else if (contentType.includes('application/x-www-form-urlencoded')) {
        const formData = await request.formData()
        data = Object.fromEntries(formData.entries())
      } else if (request.method === 'GET') {
        data = Object.fromEntries(new URL(request.url).searchParams.entries())
      } else {
        data = {}
      }

      // Validate the data
      const validatedData = await validator(data)
      
      return await handler(request, context, validatedData)
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error
      }
      
      // Wrap validation errors
      throw new ValidationError(
        `Validation failed: ${(error as Error).message}`,
        'request_data',
        undefined
      )
    }
  })
}

/**
 * Rate limiting middleware
 */
export function withRateLimit(
  maxRequests: number,
  windowMs: number,
  keyGenerator?: (request: NextRequest) => string
) {
  const requests = new Map<string, { count: number; resetTime: number }>()
  
  return (handler: (request: NextRequest, context: RequestContext) => Promise<NextResponse>) => {
    return errorMiddleware(async (request: NextRequest, context: RequestContext) => {
      const key = keyGenerator ? keyGenerator(request) : (context.ip || 'unknown')
      const now = Date.now()
      const windowStart = now - windowMs
      
      // Clean up old entries
      for (const [k, v] of requests.entries()) {
        if (v.resetTime < windowStart) {
          requests.delete(k)
        }
      }
      
      // Check current request count
      const current = requests.get(key) || { count: 0, resetTime: now + windowMs }
      
      if (current.count >= maxRequests && current.resetTime > now) {
        throw new NetworkError(
          'Too many requests',
          429,
          request.url
        )
      }
      
      // Update request count
      current.count++
      requests.set(key, current)
      
      return await handler(request, context)
    })
  }
}

// Export commonly used configurations
export const API_ERROR_MIDDLEWARE = errorMiddleware
export const CONTACT_FORM_MIDDLEWARE = (handler: any) => 
  withBusinessOperation(BusinessOperation.CONTACT_FORM_SUBMIT, 'contact_form', handler)
export const BOOKING_MIDDLEWARE = (handler: any) => 
  withBusinessOperation(BusinessOperation.BOOKING_CREATE, 'booking', handler)
export const PROPERTY_MIDDLEWARE = (handler: any) => 
  withBusinessOperation(BusinessOperation.PROPERTY_SEARCH, 'property', handler)