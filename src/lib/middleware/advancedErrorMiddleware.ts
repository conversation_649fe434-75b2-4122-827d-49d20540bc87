/**
 * Advanced error handling middleware with comprehensive Sentry integration
 * Extends the base error middleware with additional patterns and features
 */

import { NextRequest, NextResponse } from 'next/server';
import * as Sentry from '@sentry/nextjs';
import { TryCatchEnhancer } from '../TryCatchEnhancer';
import { 
  createValidationError,
  createNetworkError
} from '../errorHandling';

// Advanced middleware configuration
interface AdvancedMiddlewareConfig {
  enablePerformanceMonitoring?: boolean;
  enableSecurityHeaders?: boolean;
  enableCORS?: boolean;
  enableRequestLogging?: boolean;
  enableResponseCompression?: boolean;
  maxRequestSize?: number;
  timeoutMs?: number;
  allowedOrigins?: string[];
  securityHeaders?: Record<string, string>;
}

// Request context with enhanced tracking
interface EnhancedRequestContext {
  requestId: string;
  method: string;
  url: string;
  userAgent?: string;
  ip?: string;
  userId?: string;
  sessionId?: string;
  startTime: number;
  fingerprint?: string;
  geoLocation?: {
    country?: string;
    region?: string;
    city?: string;
  };
}

/**
 * Generate enhanced request fingerprint for tracking
 */
function generateRequestFingerprint(request: NextRequest): string {
  return TryCatchEnhancer.wrapSync(
    () => {
      const components = [
        request.headers.get('user-agent') || 'unknown',
        request.headers.get('accept-language') || 'unknown',
        request.headers.get('x-forwarded-for') || 'unknown',
        request.method,
        new URL(request.url).pathname
      ];
      
      // Simple hash function for fingerprinting
      let hash = 0;
      const str = components.join('|');
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
      }
      
      return Math.abs(hash).toString(36);
    },
    {
      operation: 'generate_request_fingerprint',
      category: 'validation',
      severity: 'low',
      businessContext: {
        entityType: 'request_fingerprint',
        workflow: 'security_tracking'
      },
      fallbackValue: 'unknown'
    }
  ) || 'unknown';
}

/**
 * Extract enhanced request context with security and performance tracking
 */
function extractEnhancedRequestContext(request: NextRequest): EnhancedRequestContext {
  return TryCatchEnhancer.wrapSync(
    () => {
      return {
        requestId: `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        method: request.method,
        url: request.url,
        userAgent: request.headers.get('user-agent') || undefined,
        ip: request.headers.get('x-forwarded-for') || 
            request.headers.get('x-real-ip') || 
            'unknown',
        userId: request.headers.get('x-user-id') || undefined,
        sessionId: request.headers.get('x-session-id') || undefined,
        startTime: performance.now(),
        fingerprint: generateRequestFingerprint(request),
        geoLocation: {
          country: request.headers.get('cf-ipcountry') || undefined,
          region: request.headers.get('cf-region') || undefined,
          city: request.headers.get('cf-ipcity') || undefined,
        }
      };
    },
    {
      operation: 'extract_enhanced_request_context',
      category: 'validation',
      severity: 'low',
      businessContext: {
        entityType: 'request_context',
        workflow: 'request_processing'
      },
      fallbackValue: {
        requestId: 'unknown',
        method: request.method,
        url: request.url,
        startTime: performance.now()
      } as EnhancedRequestContext
    }
  ) as EnhancedRequestContext;
}

/**
 * Advanced security headers middleware
 */
function addSecurityHeaders(
  response: NextResponse, 
  config: AdvancedMiddlewareConfig
): NextResponse {
  return TryCatchEnhancer.wrapSync(
    () => {
      const defaultHeaders = {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"
      };
      
      const headers = { ...defaultHeaders, ...config.securityHeaders };
      
      Object.entries(headers).forEach(([key, value]) => {
        response.headers.set(key, value);
      });
      
      return response;
    },
    {
      operation: 'add_security_headers',
      category: 'validation',
      severity: 'medium',
      businessContext: {
        entityType: 'security_headers',
        workflow: 'response_processing'
      },
      fallbackValue: response
    }
  ) || response;
}

/**
 * CORS middleware with enhanced configuration
 */
function handleCORS(
  request: NextRequest, 
  response: NextResponse, 
  config: AdvancedMiddlewareConfig
): NextResponse {
  return TryCatchEnhancer.wrapSync(
    () => {
      const origin = request.headers.get('origin');
      const allowedOrigins = config.allowedOrigins || ['*'];
      
      if (allowedOrigins.includes('*') || (origin && allowedOrigins.includes(origin))) {
        response.headers.set('Access-Control-Allow-Origin', origin || '*');
        response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
        response.headers.set('Access-Control-Max-Age', '86400');
      }
      
      return response;
    },
    {
      operation: 'handle_cors',
      category: 'validation',
      severity: 'medium',
      businessContext: {
        entityType: 'cors_headers',
        workflow: 'response_processing'
      },
      context: {
        page: {
          url: '',
          section: 'middleware',
          component: 'cors'
        },
        browser: {
          name: 'server',
          version: 'unknown',
          userAgent: 'server'
        },
        technical: {
          requestId: `cors_${Date.now()}`,
        }
      },
      fallbackValue: response
    }
  ) || response;
}

/**
 * Request size validation middleware
 */
async function validateRequestSize(
  request: NextRequest, 
  maxSize: number
): Promise<void> {
  await TryCatchEnhancer.wrapAsync(
    async () => {
      const contentLength = request.headers.get('content-length');
      
      if (contentLength) {
        const size = parseInt(contentLength, 10);
        if (size > maxSize) {
          throw createValidationError(
            `Request size ${size} bytes exceeds maximum allowed size ${maxSize} bytes`,
            'request_size',
            { size, maxSize }
          );
        }
      }
      
      // For requests without content-length, we'll need to read the body
      // This is a simplified check - in production you'd want streaming validation
      if (request.body) {
        const body = await request.text();
        if (body.length > maxSize) {
          throw createValidationError(
            `Request body size ${body.length} bytes exceeds maximum allowed size ${maxSize} bytes`,
            'request_body_size',
            { size: body.length, maxSize }
          );
        }
      }
    },
    {
      operation: 'validate_request_size',
      category: 'validation',
      severity: 'medium',
      businessContext: {
        entityType: 'request_validation',
        workflow: 'security_validation'
      },
      context: {
        page: {
          url: '',
          section: 'middleware',
          component: 'size_validation'
        },
        browser: {
          name: 'server',
          version: 'unknown',
          userAgent: 'server'
        },
        technical: {
          requestId: `size_validation_${Date.now()}`,
        }
      }
    }
  );
}

/**
 * Performance monitoring middleware
 */
function createPerformanceMonitor(context: EnhancedRequestContext) {
  return {
    end: () => {
      return TryCatchEnhancer.wrapSync(
        () => {
          const duration = performance.now() - context.startTime;
          
          // Log slow requests
          if (duration > 1000) {
            Sentry.addBreadcrumb({
              message: `Slow request detected: ${context.method} ${context.url}`,
              category: 'performance',
              level: 'warning',
              data: {
                duration,
                requestId: context.requestId,
                method: context.method,
                url: context.url
              }
            });
          }
          
          // Track performance metrics
          Sentry.setMeasurement('request_duration', duration, 'millisecond');
          
          return duration;
        },
        {
          operation: 'performance_monitor_end',
          category: 'performance',
          severity: 'low',
          businessContext: {
            entityType: 'performance_metrics',
            workflow: 'monitoring'
          },
          context: {
            page: {
              url: '',
              section: 'middleware',
              component: 'performance'
            },
            browser: {
              name: 'server',
              version: 'unknown',
              userAgent: 'server'
            },
            technical: {
              requestId: context.requestId,
            }
          },
          fallbackValue: 0
        }
      ) || 0;
    }
  };
}

/**
 * Advanced error middleware with comprehensive features
 */
export function advancedErrorMiddleware(
  handler: (request: NextRequest, context: EnhancedRequestContext) => Promise<NextResponse>,
  config: AdvancedMiddlewareConfig = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const context = extractEnhancedRequestContext(request);
    let performanceMonitor: ReturnType<typeof createPerformanceMonitor> | null = null;
    
    const result = await TryCatchEnhancer.wrapAsync(
      async () => {
        // Initialize performance monitoring
        if (config.enablePerformanceMonitoring) {
          performanceMonitor = createPerformanceMonitor(context);
        }
        
        // Validate request size
        if (config.maxRequestSize) {
          await validateRequestSize(request, config.maxRequestSize);
        }
        
        // Set up Sentry context
        Sentry.withScope((scope) => {
          scope.setTag('requestId', context.requestId);
          scope.setTag('fingerprint', context.fingerprint);
          scope.setContext('request', {
            method: context.method,
            url: context.url,
            userAgent: context.userAgent,
            ip: context.ip,
            fingerprint: context.fingerprint
          });
          
          if (context.userId) {
            scope.setUser({ id: context.userId });
          }
          
          if (context.geoLocation) {
            scope.setContext('geo', context.geoLocation);
          }
        });
        
        // Handle OPTIONS requests for CORS
        if (request.method === 'OPTIONS' && config.enableCORS) {
          let response = new NextResponse(null, { status: 200 });
          response = handleCORS(request, response, config);
          return response;
        }
        
        // Execute the main handler
        let response = await handler(request, context);
        
        // Apply response enhancements
        if (config.enableSecurityHeaders) {
          response = addSecurityHeaders(response, config);
        }
        
        if (config.enableCORS) {
          response = handleCORS(request, response, config);
        }
        
        // Add performance headers
        if (performanceMonitor) {
          const duration = performanceMonitor.end();
          response.headers.set('X-Response-Time', `${duration.toFixed(2)}ms`);
        }
        
        // Add request tracking headers
        response.headers.set('X-Request-ID', context.requestId);
        
        return response;
      },
      {
        operation: 'advanced_error_middleware',
        category: 'validation',
        severity: 'high',
        businessContext: {
          entityType: 'middleware',
          workflow: 'request_processing'
        },
        context: {
          technical: {
            requestId: context.requestId,
            apiEndpoint: context.url,
            statusCode: 500
          },
          page: {
            url: context.url,
            section: 'middleware'
          },
          browser: {
            name: 'unknown',
            version: 'unknown',
            userAgent: context.userAgent || 'unknown'
          }
        },
        onError: (error) => {
          // Log error with full context
          console.error(`[${context.requestId}] Advanced middleware error:`, {
            error: error.message,
            context,
            stack: error.stack
          });
          
          // End performance monitoring on error
          if (performanceMonitor) {
            performanceMonitor.end();
          }
        }
      }
    );
    
    return result as NextResponse;
  };
}

/**
 * Timeout middleware wrapper
 */
export function withTimeout(
  timeoutMs: number,
  handler: (request: NextRequest, context: EnhancedRequestContext) => Promise<NextResponse>
) {
  return (request: NextRequest, context: EnhancedRequestContext): Promise<NextResponse> => {
    return TryCatchEnhancer.wrapAsync(
      async () => {
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(createNetworkError(
              `Request timeout after ${timeoutMs}ms`,
              408,
              request.url
            ));
          }, timeoutMs);
        });
        
        return await Promise.race([
          handler(request, context),
          timeoutPromise
        ]);
      },
      {
        operation: 'request_timeout_wrapper',
        category: 'network',
        severity: 'high',
        businessContext: {
          entityType: 'request_timeout',
          workflow: 'request_processing'
        },
        context: {
          technical: {
            requestId: context.requestId,
            apiEndpoint: request.url,
            responseTime: timeoutMs
          },
          page: {
            url: request.url,
            section: 'timeout'
          },
          browser: {
            name: 'unknown',
            version: 'unknown',
            userAgent: 'unknown'
          }
        }
      }
    ) as Promise<NextResponse>;
  };
}

/**
 * Request logging middleware
 */
export function withRequestLogging(
  handler: (request: NextRequest, context: EnhancedRequestContext) => Promise<NextResponse>
) {
  return async (request: NextRequest, context: EnhancedRequestContext): Promise<NextResponse> => {
    const result = await TryCatchEnhancer.wrapAsync(
      async () => {
        // Log request start
        console.log(`[${context.requestId}] ${context.method} ${context.url} - Started`, {
          userAgent: context.userAgent,
          ip: context.ip,
          fingerprint: context.fingerprint
        });
        
        const response = await handler(request, context);
        
        // Log request completion
        const duration = performance.now() - context.startTime;
        console.log(`[${context.requestId}] ${context.method} ${context.url} - Completed`, {
          status: response.status,
          duration: `${duration.toFixed(2)}ms`
        });
        
        return response;
      },
      {
        operation: 'request_logging_middleware',
        category: 'validation',
        severity: 'low',
        businessContext: {
          entityType: 'request_logging',
          workflow: 'monitoring'
        },
        context: {
          technical: {
            requestId: context.requestId,
            apiEndpoint: context.url
          },
          page: {
            url: context.url,
            section: 'logging'
          },
          browser: {
            name: 'server',
            version: 'unknown',
            userAgent: context.userAgent || 'server'
          }
        }
      }
    );
    
    return result as NextResponse;
  };
}

// Export convenience functions for common configurations
export const createSecureAPIMiddleware = (config: Partial<AdvancedMiddlewareConfig> = {}) =>
  advancedErrorMiddleware(async () => new NextResponse(), {
    enableSecurityHeaders: true,
    enableCORS: true,
    enablePerformanceMonitoring: true,
    enableRequestLogging: true,
    maxRequestSize: 10 * 1024 * 1024, // 10MB
    timeoutMs: 30000, // 30 seconds
    ...config
  });

export const createPublicAPIMiddleware = (config: Partial<AdvancedMiddlewareConfig> = {}) =>
  advancedErrorMiddleware(async () => new NextResponse(), {
    enableCORS: true,
    enablePerformanceMonitoring: true,
    allowedOrigins: ['*'],
    maxRequestSize: 1 * 1024 * 1024, // 1MB
    timeoutMs: 15000, // 15 seconds
    ...config
  });