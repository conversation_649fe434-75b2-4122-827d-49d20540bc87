/**
 * PostHog analytics configuration and utilities
 * Enhanced with comprehensive async error handling and thread-safe initialization
 */

import posthog from 'posthog-js';
import { getEnvironmentConfig, isDevelopment } from './env';
import {
  PostHogErrorWrapper,
  wrapPostHogCapture,
  wrapPostHogIdentify,
  wrapPostHogPeopleSet,
  wrapPostHogReset
} from './PostHogErrorWrapper';
import {
  postHogInitManager,
  initializePostHog,
  isPostHogInitialized,
  isPostHogInitializing,
  isPostHogReady as isPostHogReadyFromManager,
  getPostHogHealthStatus as getPostHogHealthStatusFromManager,
  resetPostHog,
  getPostHogInstance as getPostHogInstanceFromManager
} from './PostHogInitManager';

export interface PostHogConfig {
  apiKey: string;
  apiHost: string;
  options: {
    capture_pageview: boolean;
    capture_pageleave: boolean;
    disable_session_recording?: boolean;
    disable_surveys?: boolean;
    disable_compression?: boolean;
  };
}

export interface AnalyticsEvents {
  page_view: { 
    page: string; 
    section: string;
    referrer?: string;
  };
  contact_form_submit: { 
    form_type: string;
    success: boolean;
  };
  cta_click: { 
    cta_type: string; 
    location: string;
    text?: string;
  };
  navigation_click: {
    section: string;
    method: 'scroll' | 'click';
  };
  service_inquiry: {
    service_type: string;
    contact_method: string;
  };
  funnel_event: {
    funnel_stage: string;
    [key: string]: any;
  };
  scroll_depth: {
    depth_percentage: number;
    section: string;
  };
  time_on_section: {
    section: string;
    time_spent_seconds: number;
  };
}



/**
 * Initializes PostHog analytics using the new thread-safe initialization manager
 */
export function initPostHog(): Promise<void> {
  return initializePostHog().then(success => {
    if (!success && isDevelopment()) {
      console.log('PostHog initialization completed with disabled state');
    }
  }).catch(error => {
    console.error('PostHog initialization failed:', error);
    // Don't throw in production to prevent app crashes
    if (isDevelopment()) {
      throw error;
    }
  });
}

// Track last consent check to avoid spamming console
let lastConsentCheck = 0;

/**
 * Checks if analytics tracking is allowed based on user consent
 */
function hasAnalyticsConsent(): boolean {
  if (typeof window === 'undefined') return false;
  
  // In development, allow tracking without consent for testing
  if (isDevelopment()) {
    // Only log once every 10 seconds to avoid spam
    const now = Date.now();
    if (now - lastConsentCheck > 10000) {
      console.log('🧪 Development mode: allowing analytics without consent');
      lastConsentCheck = now;
    }
    return true;
  }
  
  try {
    const consentData = localStorage.getItem('ja_privacy_consent');
    if (!consentData) return false;
    
    const consent = JSON.parse(consentData);
    return consent.analytics === true;
  } catch (error) {
    console.error('Error checking analytics consent:', error);
    return false;
  }
}

// Event throttling to prevent rate limiting
const eventThrottle = new Map<string, number>();
const THROTTLE_DURATION = 1000; // 1 second

/**
 * Tracks a custom event with typed parameters (privacy-aware)
 * Enhanced with comprehensive error handling and graceful degradation
 */
export function trackEvent<K extends keyof AnalyticsEvents>(
  eventName: K,
  properties: AnalyticsEvents[K]
): void {
  // Early returns for consent and initialization checks
  if (!hasAnalyticsConsent()) {
    if (isDevelopment()) {
      console.log('📊 Event not tracked - no analytics consent:', eventName);
    }
    return;
  }

  if (!postHogInitManager.isReady()) {
    if (isDevelopment()) {
      console.warn('📊 PostHog not ready. Event not tracked:', eventName, {
        managerState: getPostHogHealthStatusFromManager(),
        isInitialized: postHogInitManager.isInitialized(),
        isInitializing: postHogInitManager.isInitializing()
      });
    }
    return;
  }

  // Throttle identical events to prevent rate limiting
  const eventKey = `${eventName}_${JSON.stringify(properties)}`;
  const now = Date.now();
  const lastSent = eventThrottle.get(eventKey) || 0;
  
  if (now - lastSent < THROTTLE_DURATION) {
    if (isDevelopment()) {
      console.log('📊 Event throttled:', eventName);
    }
    return;
  }

  // Get PostHog instance from manager
  const posthogInstance = getPostHogInstanceFromManager();
  if (!posthogInstance) {
    if (isDevelopment()) {
      console.warn('📊 PostHog instance not available for event tracking:', eventName, {
        managerReady: postHogInitManager.isReady(),
        managerState: getPostHogHealthStatusFromManager()
      });
    }
    return;
  }

  if (isDevelopment()) {
    console.log('📊 Attempting to track event:', eventName, properties);
  }

  // Use enhanced PostHog wrapper with comprehensive error handling
  wrapPostHogCapture(
    eventName,
    {
      ...properties,
      timestamp: new Date().toISOString(),
      environment: getEnvironmentConfig().app.environment,
    },
    posthogInstance,
    {
      timeout: 5000,
      retries: 2,
      gracefulDegradation: true,
      logErrors: isDevelopment(),
    }
  ).then((success) => {
    if (success) {
      eventThrottle.set(eventKey, now);
      
      if (isDevelopment()) {
        console.log('📊 Event tracked:', eventName, properties);
      }
    } else {
      // Clear throttle entry on failure so we can retry later
      eventThrottle.delete(eventKey);
    }
  }).catch((error) => {
    // This should rarely happen due to graceful degradation
    console.error('Failed to track event:', eventName, error);
    eventThrottle.delete(eventKey);
  });
}

/**
 * Tracks page view with section information
 */
export function trackPageView(page: string, section: string, referrer?: string): void {
  trackEvent('page_view', { page, section, referrer });
}

/**
 * Tracks CTA button clicks
 */
export function trackCTAClick(ctaType: string, location: string, text?: string): void {
  trackEvent('cta_click', { cta_type: ctaType, location, text });
}

/**
 * Tracks contact form submissions
 */
export function trackContactFormSubmit(formType: string, success: boolean): void {
  trackEvent('contact_form_submit', { form_type: formType, success });
}

/**
 * Tracks navigation interactions
 */
export function trackNavigation(section: string, method: 'scroll' | 'click'): void {
  trackEvent('navigation_click', { section, method });
}

/**
 * Tracks service inquiries
 */
export function trackServiceInquiry(serviceType: string, contactMethod: string): void {
  trackEvent('service_inquiry', { service_type: serviceType, contact_method: contactMethod });
}

/**
 * Tracks conversion funnel events
 */
export function trackFunnelEvent(funnelStage: string, eventData: Record<string, any>): void {
  trackEvent('funnel_event' as keyof AnalyticsEvents, {
    funnel_stage: funnelStage,
    ...eventData,
  } as any);
}

/**
 * Tracks scroll depth for engagement analysis
 */
export function trackScrollDepth(depth: number, section: string): void {
  trackEvent('scroll_depth' as keyof AnalyticsEvents, {
    depth_percentage: depth,
    section,
  } as any);
}

/**
 * Tracks time spent on sections
 */
export function trackTimeOnSection(section: string, timeSpent: number): void {
  trackEvent('time_on_section' as keyof AnalyticsEvents, {
    section,
    time_spent_seconds: timeSpent,
  } as any);
}

/**
 * Sets user properties for analytics (privacy-aware)
 * Enhanced with comprehensive error handling and graceful degradation
 */
export function setUserProperties(properties: Record<string, any>): void {
  if (!hasAnalyticsConsent()) {
    if (isDevelopment()) {
      console.log('👤 User properties not set (no consent)');
    }
    return;
  }

  if (!isPostHogReady()) {
    if (isDevelopment()) {
      console.warn('PostHog not ready. User properties not set');
    }
    return;
  }

  // Get PostHog instance from manager
  const posthogInstance = getPostHogInstanceFromManager();
  if (!posthogInstance) {
    if (isDevelopment()) {
      console.warn('PostHog instance not available for setting user properties');
    }
    return;
  }

  // Use enhanced PostHog wrapper with comprehensive error handling
  wrapPostHogPeopleSet(
    properties,
    posthogInstance,
    {
      timeout: 5000,
      retries: 2,
      gracefulDegradation: true,
      logErrors: isDevelopment(),
    }
  ).then((success) => {
    if (success && isDevelopment()) {
      console.log('👤 User properties set:', properties);
    }
  }).catch((error) => {
    // This should rarely happen due to graceful degradation
    console.error('Failed to set user properties:', error);
  });
}

/**
 * Identifies a user (for authenticated users, privacy-aware)
 * Enhanced with comprehensive error handling and graceful degradation
 */
export function identifyUser(userId: string, properties?: Record<string, any>): void {
  if (!hasAnalyticsConsent()) {
    if (isDevelopment()) {
      console.log('🔍 User not identified (no consent)');
    }
    return;
  }

  if (!isPostHogReady()) {
    if (isDevelopment()) {
      console.warn('PostHog not ready. User not identified');
    }
    return;
  }

  // Get PostHog instance from manager
  const posthogInstance = getPostHogInstanceFromManager();
  if (!posthogInstance) {
    if (isDevelopment()) {
      console.warn('PostHog instance not available for user identification');
    }
    return;
  }

  // Use enhanced PostHog wrapper with comprehensive error handling
  wrapPostHogIdentify(
    userId,
    properties || {},
    posthogInstance,
    {
      timeout: 5000,
      retries: 2,
      gracefulDegradation: true,
      logErrors: isDevelopment(),
    }
  ).then((success) => {
    if (success && isDevelopment()) {
      console.log('🔍 User identified:', userId, properties);
    }
  }).catch((error) => {
    // This should rarely happen due to graceful degradation
    console.error('Failed to identify user:', error);
  });
}

/**
 * Resets user session (for logout)
 * Enhanced with comprehensive error handling and graceful degradation
 */
export function resetUser(): void {
  if (!isPostHogReady()) {
    return;
  }

  // Get PostHog instance from manager
  const posthogInstance = getPostHogInstanceFromManager();
  if (!posthogInstance) {
    return;
  }

  // Use enhanced PostHog wrapper with comprehensive error handling
  wrapPostHogReset(
    posthogInstance,
    {
      timeout: 3000,
      retries: 1,
      gracefulDegradation: true,
      logErrors: isDevelopment(),
    }
  ).then((success) => {
    if (success && isDevelopment()) {
      console.log('🔄 User session reset');
    }
  }).catch((error) => {
    // This should rarely happen due to graceful degradation
    console.error('Failed to reset user session:', error);
  });
}

/**
 * Checks if PostHog is initialized and ready (uses new manager)
 */
export function isPostHogReady(): boolean {
  return isPostHogReadyFromManager();
}

/**
 * Gets PostHog instance for advanced usage (uses new manager)
 */
export function getPostHogInstance() {
  return getPostHogInstanceFromManager();
}

/**
 * Handles consent withdrawal by opting out of tracking
 * Enhanced with comprehensive error handling and graceful degradation
 */
export function withdrawConsent(): void {
  // Opt out of capturing first
  const posthogInstance = getPostHogInstanceFromManager();
  if (posthogInstance && typeof posthogInstance.opt_out_capturing === 'function') {
    posthogInstance.opt_out_capturing();
  }

  // Use the initialization manager to disable PostHog
  postHogInitManager.disable();

  if (isDevelopment()) {
    console.log('🚫 Analytics consent withdrawn and capturing disabled');
  }
}

/**
 * Handles consent granting by opting back into tracking
 * Enhanced with comprehensive error handling and graceful degradation
 */
export function grantConsent(): void {
  // Use the initialization manager to enable PostHog
  postHogInitManager.enable().then((success) => {
    if (success) {
      // Opt the user back into capturing
      const posthogInstance = getPostHogInstanceFromManager();
      if (posthogInstance && typeof posthogInstance.opt_in_capturing === 'function') {
        posthogInstance.opt_in_capturing();
        if (isDevelopment()) {
          console.log('✅ Analytics consent granted and capturing enabled');
        }
      }
    }
  }).catch((error) => {
    console.error('Failed to grant consent:', error);
  });
}

/**
 * Check PostHog service health
 * Enhanced with comprehensive health monitoring
 */
export function checkPostHogHealth(): Promise<boolean> {
  const posthogInstance = getPostHogInstanceFromManager();
  if (!posthogInstance) {
    return Promise.resolve(false);
  }

  return PostHogErrorWrapper.performHealthCheck(posthogInstance);
}

/**
 * Get PostHog service health status
 */
export function getPostHogHealthStatus() {
  return getPostHogHealthStatusFromManager();
}

/**
 * Reset PostHog health status (for recovery or testing)
 */
export function resetPostHogHealth(): void {
  postHogInitManager.reset();
  PostHogErrorWrapper.resetHealthStatus();
}

/**
 * Check if PostHog service is currently healthy
 */
export function isPostHogHealthy(): boolean {
  const healthStatus = getPostHogHealthStatusFromManager();
  return healthStatus.healthy && PostHogErrorWrapper.isServiceHealthy();
}

/**
 * Enhanced PostHog ready check with health status
 */
export function isPostHogReadyAndHealthy(): boolean {
  return postHogInitManager.isReady() && PostHogErrorWrapper.isServiceHealthy();
}

/**
 * Resets initialization state (for testing purposes)
 * @internal
 */
export function _resetInitializationState(): void {
  postHogInitManager.reset();
  PostHogErrorWrapper.resetHealthStatus();
}

// Export PostHog instance for direct access if needed (legacy compatibility)
export { posthog };
export default posthog;

// Export new initialization manager functions
export { 
  postHogInitManager,
  initializePostHog,
  isPostHogInitialized,
  isPostHogInitializing,
  resetPostHog
};