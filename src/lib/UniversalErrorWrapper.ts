/**
 * Universal Error Wrapper Infrastructure
 * 
 * Provides comprehensive error handling capabilities with automatic context detection,
 * performance monitoring integration, and proper Sentry error reporting.
 */

import * as Sentry from "@sentry/nextjs";
import { 
  reportError, 
  addBusinessBreadcrumb,
  type ErrorContext 
} from './sentry';
import { 
  BusinessErrorHandler,
  ValidationError,
  NetworkError,
  DatabaseError,
  BusinessLogicError,
  BusinessOperation
} from './errorHandling';

// Enhanced error context with performance and system state
export interface EnhancedErrorContext extends ErrorContext {
  performance?: {
    operationDuration?: number;
    memoryUsage?: number;
    apiResponseTime?: number;
    renderTime?: number;
  };
  
  userJourney?: {
    currentStep: string;
    previousSteps: string[];
    sessionDuration: number;
    interactionCount: number;
  };
  
  systemState?: {
    databaseConnectionStatus: boolean;
    externalServicesStatus: Record<string, boolean>;
    featureFlags: Record<string, boolean>;
  };
}

// Operation configuration for error handling
export interface OperationConfig {
  operation: string;
  entityType?: string;
  entityId?: string;
  timeout?: number;
  retryCount?: number;
  retryDelay?: number;
  skipPerformanceMonitoring?: boolean;
  customContext?: Record<string, any>;
}

// Performance metrics for operations
export interface OperationMetrics {
  startTime: number;
  endTime?: number;
  duration?: number;
  memoryBefore?: number;
  memoryAfter?: number;
  memoryDelta?: number;
}

// Retry configuration
export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  exponentialBackoff: boolean;
  retryCondition?: (error: Error) => boolean;
}

/**
 * Universal Error Wrapper class providing comprehensive error handling
 */
export class UniversalErrorWrapper {
  private static instance: UniversalErrorWrapper;
  private operationMetrics: Map<string, OperationMetrics> = new Map();
  private userJourneySteps: string[] = [];
  private interactionCount: number = 0;
  private sessionStartTime: number = Date.now();

  private constructor() {
    // Initialize performance monitoring
    this.initializePerformanceMonitoring();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): UniversalErrorWrapper {
    if (!UniversalErrorWrapper.instance) {
      UniversalErrorWrapper.instance = new UniversalErrorWrapper();
    }
    return UniversalErrorWrapper.instance;
  }

  /**
   * Initialize performance monitoring
   */
  private initializePerformanceMonitoring(): void {
    if (typeof window !== 'undefined') {
      // Monitor memory usage if available
      if ('memory' in performance) {
        setInterval(() => {
          const memInfo = (performance as any).memory;
          if (memInfo) {
            Sentry.setMeasurement('memory.used', memInfo.usedJSHeapSize, 'byte');
            Sentry.setMeasurement('memory.total', memInfo.totalJSHeapSize, 'byte');
            Sentry.setMeasurement('memory.limit', memInfo.jsHeapSizeLimit, 'byte');
          }
        }, 30000); // Every 30 seconds
      }
    }
  }

  /**
   * Get automatic context detection
   */
  private getAutomaticContext(): EnhancedErrorContext {
    const browserInfo = this.getBrowserInfo();
    const pageInfo = this.getPageInfo();
    const performanceInfo = this.getPerformanceInfo();
    const userJourneyInfo = this.getUserJourneyInfo();
    const systemStateInfo = this.getSystemStateInfo();

    return {
      page: pageInfo,
      browser: browserInfo,
      performance: performanceInfo,
      userJourney: userJourneyInfo,
      systemState: systemStateInfo,
    };
  }

  /**
   * Get browser information
   */
  private getBrowserInfo() {
    if (typeof window === 'undefined') {
      return { name: 'server', version: 'unknown', userAgent: 'server' };
    }
    
    const userAgent = navigator.userAgent;
    let browserName = 'unknown';
    let browserVersion = 'unknown';

    if (userAgent.includes('Chrome')) {
      browserName = 'Chrome';
      const match = userAgent.match(/Chrome\/(\d+)/);
      browserVersion = match ? match[1] : 'unknown';
    } else if (userAgent.includes('Firefox')) {
      browserName = 'Firefox';
      const match = userAgent.match(/Firefox\/(\d+)/);
      browserVersion = match ? match[1] : 'unknown';
    } else if (userAgent.includes('Safari')) {
      browserName = 'Safari';
      const match = userAgent.match(/Version\/(\d+)/);
      browserVersion = match ? match[1] : 'unknown';
    }

    return { name: browserName, version: browserVersion, userAgent };
  }

  /**
   * Get page information
   */
  private getPageInfo() {
    if (typeof window === 'undefined') {
      return { url: 'server', section: 'server' };
    }
    
    return {
      url: window.location.href,
      section: window.location.pathname,
      component: document.title || 'unknown',
    };
  }

  /**
   * Get performance information
   */
  private getPerformanceInfo() {
    if (typeof window === 'undefined') {
      return {};
    }

    const performanceInfo: any = {};

    // Memory information
    if ('memory' in performance) {
      const memInfo = (performance as any).memory;
      if (memInfo) {
        performanceInfo.memoryUsage = memInfo.usedJSHeapSize;
      }
    }

    // Navigation timing
    if ('navigation' in performance) {
      const navTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navTiming) {
        performanceInfo.renderTime = navTiming.loadEventEnd - navTiming.fetchStart;
      }
    }

    return performanceInfo;
  }

  /**
   * Get user journey information
   */
  private getUserJourneyInfo() {
    return {
      currentStep: this.userJourneySteps[this.userJourneySteps.length - 1] || 'unknown',
      previousSteps: this.userJourneySteps.slice(-5), // Last 5 steps
      sessionDuration: Date.now() - this.sessionStartTime,
      interactionCount: this.interactionCount,
    };
  }

  /**
   * Get system state information
   */
  private getSystemStateInfo() {
    const systemState: any = {
      databaseConnectionStatus: true, // Default to true, would be enhanced with actual checks
      externalServicesStatus: {
        supabase: true,
        posthog: true,
        sentry: true,
      },
      featureFlags: {},
    };

    // Check if we're in browser environment
    if (typeof window !== 'undefined') {
      // Add browser-specific system state
      systemState.browserState = {
        online: navigator.onLine,
        cookieEnabled: navigator.cookieEnabled,
        language: navigator.language,
        userAgentData: (navigator as any).userAgentData || null,
      };

      // Add connection information if available
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        systemState.networkState = {
          effectiveType: connection?.effectiveType,
          downlink: connection?.downlink,
          rtt: connection?.rtt,
        };
      }
    }

    return systemState;
  }

  /**
   * Record user journey step
   */
  public recordUserJourneyStep(step: string): void {
    this.userJourneySteps.push(step);
    this.interactionCount++;
    
    // Keep only last 20 steps to prevent memory issues
    if (this.userJourneySteps.length > 20) {
      this.userJourneySteps = this.userJourneySteps.slice(-20);
    }
  }

  /**
   * Start operation metrics tracking
   */
  private startOperationMetrics(operationId: string): void {
    const metrics: OperationMetrics = {
      startTime: performance.now(),
    };

    if (typeof window !== 'undefined' && 'memory' in performance) {
      const memInfo = (performance as any).memory;
      if (memInfo) {
        metrics.memoryBefore = memInfo.usedJSHeapSize;
      }
    }

    this.operationMetrics.set(operationId, metrics);
  }

  /**
   * End operation metrics tracking
   */
  private endOperationMetrics(operationId: string): OperationMetrics | null {
    const metrics = this.operationMetrics.get(operationId);
    if (!metrics) return null;

    metrics.endTime = performance.now();
    metrics.duration = metrics.endTime - metrics.startTime;

    if (typeof window !== 'undefined' && 'memory' in performance) {
      const memInfo = (performance as any).memory;
      if (memInfo && metrics.memoryBefore !== undefined) {
        metrics.memoryAfter = memInfo.usedJSHeapSize;
        if (metrics.memoryAfter !== undefined) {
          metrics.memoryDelta = metrics.memoryAfter - metrics.memoryBefore;
        }
      }
    }

    this.operationMetrics.delete(operationId);
    return metrics;
  }

  /**
   * Implement exponential backoff retry logic
   */
  private async executeWithRetry<T>(
    fn: () => Promise<T>,
    retryConfig: RetryConfig,
    operationName: string
  ): Promise<T> {
    let lastError: Error;
    let delay = retryConfig.baseDelay;

    for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;

        // Check if we should retry this error
        if (retryConfig.retryCondition && !retryConfig.retryCondition(lastError)) {
          throw lastError;
        }

        // Don't retry on the last attempt
        if (attempt === retryConfig.maxRetries) {
          break;
        }

        // Add breadcrumb for retry attempt
        addBusinessBreadcrumb(
          operationName,
          'retry',
          undefined,
          'warning',
          {
            attempt: attempt + 1,
            maxRetries: retryConfig.maxRetries,
            delay,
            error: lastError.message,
          }
        );

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay));

        // Calculate next delay
        if (retryConfig.exponentialBackoff) {
          delay = Math.min(delay * 2, retryConfig.maxDelay);
        }
      }
    }

    throw lastError!;
  }

  /**
   * Wrap async operations with comprehensive error handling
   */
  public async wrapAsync<T>(
    config: OperationConfig,
    asyncFn: () => Promise<T>,
    additionalContext?: Partial<EnhancedErrorContext>
  ): Promise<T> {
    const operationId = `${config.operation}_${Date.now()}_${Math.random()}`;
    
    // Start performance monitoring
    if (!config.skipPerformanceMonitoring) {
      this.startOperationMetrics(operationId);
    }

    // Record user journey step
    this.recordUserJourneyStep(config.operation);

    return Sentry.startSpan(
      {
        op: `async.${config.entityType || 'operation'}`,
        name: config.operation,
        attributes: {
          operationId,
          entityType: config.entityType,
          entityId: config.entityId,
          ...config.customContext,
        },
      },
      async (span) => {
        try {
          // Add operation breadcrumb
          addBusinessBreadcrumb(
            config.operation,
            config.entityType || 'unknown',
            config.entityId,
            undefined,
            { status: 'started', operationId }
          );

          let result: T;

          // Execute with timeout if specified
          if (config.timeout) {
            const timeoutPromise = new Promise<never>((_, reject) => {
              setTimeout(() => reject(new Error(`Operation timeout: ${config.operation}`)), config.timeout);
            });

            result = await Promise.race([asyncFn(), timeoutPromise]);
          } else {
            result = await asyncFn();
          }

          // End performance monitoring
          if (!config.skipPerformanceMonitoring) {
            const metrics = this.endOperationMetrics(operationId);
            if (metrics) {
              span?.setAttribute('performance.duration', metrics.duration || 0);
              if (metrics.memoryDelta) {
                span?.setAttribute('performance.memoryDelta', metrics.memoryDelta);
              }
            }
          }

          // Add success breadcrumb
          addBusinessBreadcrumb(
            config.operation,
            config.entityType || 'unknown',
            config.entityId,
            'success',
            { status: 'completed', operationId }
          );

          span?.setAttribute('operation.status', 'success');
          return result;

        } catch (error) {
          // End performance monitoring on error
          if (!config.skipPerformanceMonitoring) {
            const metrics = this.endOperationMetrics(operationId);
            if (metrics) {
              span?.setAttribute('performance.duration', metrics.duration || 0);
            }
          }

          span?.setAttribute('operation.status', 'error');
          span?.recordException(error as Error);

          // Get enhanced context
          const enhancedContext: EnhancedErrorContext = {
            ...this.getMergedContext(additionalContext),
            business: {
              operation: config.operation,
              entityType: config.entityType,
              entityId: config.entityId,
              workflow: 'async_operation',
              ...this.getMergedContext(additionalContext).business,
            },
          };

          // Handle different error types
          if (error instanceof ValidationError) {
            BusinessErrorHandler.handleValidationError(
              error,
              config.operation as BusinessOperation,
              { operationId, ...config.customContext }
            );
          } else if (error instanceof NetworkError) {
            BusinessErrorHandler.handleNetworkError(
              error,
              config.operation as BusinessOperation,
              { operationId, ...config.customContext }
            );
          } else if (error instanceof DatabaseError) {
            BusinessErrorHandler.handleDatabaseError(
              error,
              config.operation as BusinessOperation,
              { operationId, ...config.customContext }
            );
          } else if (error instanceof BusinessLogicError) {
            BusinessErrorHandler.handleBusinessLogicError(
              error,
              { operationId, ...config.customContext }
            );
          } else {
            // Generic error handling with enhanced context
            reportError(error as Error, enhancedContext);
          }

          // Add error breadcrumb
          addBusinessBreadcrumb(
            config.operation,
            config.entityType || 'unknown',
            config.entityId,
            'error',
            { 
              status: 'failed', 
              operationId,
              error: (error as Error).message,
              errorType: (error as Error).name,
            }
          );

          throw error;
        }
      }
    );
  }

  /**
   * Wrap async operations with retry logic
   */
  public async wrapAsyncWithRetry<T>(
    config: OperationConfig,
    asyncFn: () => Promise<T>,
    retryConfig: RetryConfig,
    additionalContext?: Partial<EnhancedErrorContext>
  ): Promise<T> {
    return this.wrapAsync(
      config,
      () => this.executeWithRetry(asyncFn, retryConfig, config.operation),
      additionalContext
    );
  }

  /**
   * Wrap synchronous operations with comprehensive error handling
   */
  public wrapSync<T>(
    config: OperationConfig,
    syncFn: () => T,
    additionalContext?: Partial<EnhancedErrorContext>
  ): T {
    const operationId = `${config.operation}_${Date.now()}_${Math.random()}`;
    
    // Start performance monitoring
    if (!config.skipPerformanceMonitoring) {
      this.startOperationMetrics(operationId);
    }

    // Record user journey step
    this.recordUserJourneyStep(config.operation);

    return Sentry.startSpan(
      {
        op: `sync.${config.entityType || 'operation'}`,
        name: config.operation,
        attributes: {
          operationId,
          entityType: config.entityType,
          entityId: config.entityId,
          ...config.customContext,
        },
      },
      (span) => {
        try {
          // Add operation breadcrumb
          addBusinessBreadcrumb(
            config.operation,
            config.entityType || 'unknown',
            config.entityId,
            undefined,
            { status: 'started', operationId }
          );

          const result = syncFn();

          // End performance monitoring
          if (!config.skipPerformanceMonitoring) {
            const metrics = this.endOperationMetrics(operationId);
            if (metrics) {
              span?.setAttribute('performance.duration', metrics.duration || 0);
              if (metrics.memoryDelta) {
                span?.setAttribute('performance.memoryDelta', metrics.memoryDelta);
              }
            }
          }

          // Add success breadcrumb
          addBusinessBreadcrumb(
            config.operation,
            config.entityType || 'unknown',
            config.entityId,
            'success',
            { status: 'completed', operationId }
          );

          span?.setAttribute('operation.status', 'success');
          return result;

        } catch (error) {
          // End performance monitoring on error
          if (!config.skipPerformanceMonitoring) {
            const metrics = this.endOperationMetrics(operationId);
            if (metrics) {
              span?.setAttribute('performance.duration', metrics.duration || 0);
            }
          }

          span?.setAttribute('operation.status', 'error');
          span?.recordException(error as Error);

          // Get enhanced context
          const enhancedContext: EnhancedErrorContext = {
            ...this.getMergedContext(additionalContext),
            business: {
              operation: config.operation,
              entityType: config.entityType,
              entityId: config.entityId,
              workflow: 'sync_operation',
              ...this.getMergedContext(additionalContext).business,
            },
          };

          // Handle different error types
          if (error instanceof ValidationError) {
            BusinessErrorHandler.handleValidationError(
              error,
              config.operation as BusinessOperation,
              { operationId, ...config.customContext }
            );
          } else if (error instanceof NetworkError) {
            BusinessErrorHandler.handleNetworkError(
              error,
              config.operation as BusinessOperation,
              { operationId, ...config.customContext }
            );
          } else if (error instanceof DatabaseError) {
            BusinessErrorHandler.handleDatabaseError(
              error,
              config.operation as BusinessOperation,
              { operationId, ...config.customContext }
            );
          } else if (error instanceof BusinessLogicError) {
            BusinessErrorHandler.handleBusinessLogicError(
              error,
              { operationId, ...config.customContext }
            );
          } else {
            // Generic error handling with enhanced context
            reportError(error as Error, enhancedContext);
          }

          // Add error breadcrumb
          addBusinessBreadcrumb(
            config.operation,
            config.entityType || 'unknown',
            config.entityId,
            'error',
            { 
              status: 'failed', 
              operationId,
              error: (error as Error).message,
              errorType: (error as Error).name,
            }
          );

          throw error;
        }
      }
    );
  }

  /**
   * Create a default retry configuration
   */
  public static createDefaultRetryConfig(overrides?: Partial<RetryConfig>): RetryConfig {
    return {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      exponentialBackoff: true,
      retryCondition: (error: Error) => {
        // Retry on network errors and temporary failures
        return error instanceof NetworkError || 
               error.message.includes('timeout') ||
               error.message.includes('ECONNRESET') ||
               error.message.includes('ENOTFOUND');
      },
      ...overrides,
    };
  }

  /**
   * Create operation configuration with defaults
   */
  public static createOperationConfig(
    operation: string,
    overrides?: Partial<OperationConfig>
  ): OperationConfig {
    return {
      operation,
      timeout: 30000, // 30 seconds default
      retryCount: 0,
      retryDelay: 1000,
      skipPerformanceMonitoring: false,
      ...overrides,
    };
  }

  /**
   * Clear user journey history (useful for testing or privacy)
   */
  public clearUserJourney(): void {
    this.userJourneySteps = [];
    this.interactionCount = 0;
  }

  /**
   * Get current user journey state
   */
  public getUserJourneyState() {
    return {
      currentStep: this.userJourneySteps[this.userJourneySteps.length - 1] || 'unknown',
      previousSteps: [...this.userJourneySteps],
      sessionDuration: Date.now() - this.sessionStartTime,
      interactionCount: this.interactionCount,
    };
  }

  /**
   * Set custom context that will be included in all subsequent operations
   */
  private globalContext: Partial<EnhancedErrorContext> = {};

  public setGlobalContext(context: Partial<EnhancedErrorContext>): void {
    this.globalContext = { ...this.globalContext, ...context };
  }

  /**
   * Clear global context
   */
  public clearGlobalContext(): void {
    this.globalContext = {};
  }

  /**
   * Get merged context (automatic + global + additional)
   */
  private getMergedContext(additionalContext?: Partial<EnhancedErrorContext>): EnhancedErrorContext {
    const automaticContext = this.getAutomaticContext();
    return {
      ...automaticContext,
      ...this.globalContext,
      ...additionalContext,
      // Ensure business context is properly merged
      business: {
        ...automaticContext.business,
        ...this.globalContext.business,
        ...additionalContext?.business,
      },
    };
  }
}

// Export singleton instance for convenience
export const universalErrorWrapper = UniversalErrorWrapper.getInstance();

// Export utility functions for common patterns
export const wrapAsync = <T>(
  operation: string,
  asyncFn: () => Promise<T>,
  options?: Partial<OperationConfig & { context?: Partial<EnhancedErrorContext> }>
): Promise<T> => {
  const config = UniversalErrorWrapper.createOperationConfig(operation, options);
  return universalErrorWrapper.wrapAsync(config, asyncFn, options?.context);
};

export const wrapSync = <T>(
  operation: string,
  syncFn: () => T,
  options?: Partial<OperationConfig & { context?: Partial<EnhancedErrorContext> }>
): T => {
  const config = UniversalErrorWrapper.createOperationConfig(operation, options);
  return universalErrorWrapper.wrapSync(config, syncFn, options?.context);
};

export const wrapAsyncWithRetry = <T>(
  operation: string,
  asyncFn: () => Promise<T>,
  retryConfig?: Partial<RetryConfig>,
  options?: Partial<OperationConfig & { context?: Partial<EnhancedErrorContext> }>
): Promise<T> => {
  const config = UniversalErrorWrapper.createOperationConfig(operation, options);
  const retry = UniversalErrorWrapper.createDefaultRetryConfig(retryConfig);
  return universalErrorWrapper.wrapAsyncWithRetry(config, asyncFn, retry, options?.context);
};

// Additional utility functions for common scenarios

/**
 * Wrap database operations with appropriate error handling
 */
export const wrapDatabaseOperation = <T>(
  operation: string,
  asyncFn: () => Promise<T>,
  table?: string,
  options?: Partial<OperationConfig & { context?: Partial<EnhancedErrorContext> }>
): Promise<T> => {
  return wrapAsync(operation, asyncFn, {
    entityType: 'database',
    entityId: table,
    timeout: 10000, // 10 seconds for database operations
    ...options,
    context: {
      technical: { apiEndpoint: `database/${table}` },
      ...options?.context,
    },
  });
};

/**
 * Wrap API calls with retry logic and appropriate timeouts
 */
export const wrapAPICall = <T>(
  endpoint: string,
  asyncFn: () => Promise<T>,
  options?: Partial<OperationConfig & { context?: Partial<EnhancedErrorContext> }>
): Promise<T> => {
  return wrapAsyncWithRetry(
    `api_call_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`,
    asyncFn,
    {
      maxRetries: 3,
      baseDelay: 1000,
      retryCondition: (error) => 
        error instanceof NetworkError && 
        error.statusCode !== undefined && 
        error.statusCode >= 500
    },
    {
      entityType: 'api',
      entityId: endpoint,
      timeout: 15000, // 15 seconds for API calls
      ...options,
      context: {
        technical: { apiEndpoint: endpoint },
        ...options?.context,
      },
    }
  );
};

/**
 * Wrap form validation with appropriate error handling
 */
export const wrapFormValidation = <T>(
  formName: string,
  syncFn: () => T,
  options?: Partial<OperationConfig & { context?: Partial<EnhancedErrorContext> }>
): T => {
  return wrapSync(`validate_${formName}`, syncFn, {
    entityType: 'form',
    entityId: formName,
    skipPerformanceMonitoring: true, // Validation is usually fast
    ...options,
    context: {
      business: { workflow: 'form_validation' },
      ...options?.context,
    },
  });
};

/**
 * Wrap component operations with appropriate error handling
 */
export const wrapComponentOperation = <T>(
  componentName: string,
  operation: string,
  syncFn: () => T,
  options?: Partial<OperationConfig & { context?: Partial<EnhancedErrorContext> }>
): T => {
  return wrapSync(`${componentName}_${operation}`, syncFn, {
    entityType: 'component',
    entityId: componentName,
    ...options,
    context: {
      page: { 
        url: typeof window !== 'undefined' ? window.location.href : 'server',
        section: typeof window !== 'undefined' ? window.location.pathname : 'server',
        component: componentName 
      },
      business: { workflow: 'component_interaction' },
      ...options?.context,
    },
  });
};