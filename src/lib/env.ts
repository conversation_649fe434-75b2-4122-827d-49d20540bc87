/**
 * Secure environment configuration utilities with validation
 * Implements security best practices for environment variable handling
 */

import type { EnvironmentConfig, CustomProcessEnv, SecureEnvironmentConfig } from '../types/env';

// List of sensitive environment variables that should never be exposed to client
const SENSITIVE_SERVER_ONLY_VARS = [
  'SENTRY_AUTH_TOKEN',
  'SUPABASE_SERVICE_ROLE_KEY',
  'DATABASE_URL',
  'NEXTAUTH_SECRET',
  'WEBHOOK_SECRET',
  'API_SECRET_KEY',
  'PRIVATE_KEY',
  'JWT_SECRET',
] as const;

// List of required environment variables for production
const REQUIRED_PRODUCTION_VARS = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'NEXT_PUBLIC_SENTRY_DSN',
  'NEXT_PUBLIC_POSTHOG_API_KEY',
] as const;

// Environment variable patterns that should be validated
const ENV_VAR_PATTERNS = {
  SUPABASE_URL: /^https:\/\/[a-z0-9]+\.supabase\.co$/,
  SENTRY_DSN: /^https:\/\/[a-f0-9]+@[a-z0-9-]+\.ingest\.(us\.)?sentry\.io\/[0-9]+$/,
  POSTHOG_API_KEY: /^phc_[a-zA-Z0-9]+$/,
  URL: /^https?:\/\/.+/,
} as const;

/**
 * Validates that a required environment variable is present and secure
 */
function requireEnvVar(name: keyof CustomProcessEnv, value: string | undefined): string {
  if (!value || value.trim() === '') {
    const errorMessage = `Missing required environment variable: ${name}. ` +
      `Please check your environment configuration and ensure ${name} is set.`;
    
    // Log security-relevant missing variables
    if (isProduction()) {
      console.error(`🔒 SECURITY: ${errorMessage}`);
    }
    
    throw new Error(errorMessage);
  }

  const trimmedValue = value.trim();
  
  // Validate format for known patterns
  if (name.includes('SUPABASE_URL') && !ENV_VAR_PATTERNS.SUPABASE_URL.test(trimmedValue)) {
    throw new Error(`Invalid format for ${name}: Must be a valid Supabase URL`);
  }
  
  if (name.includes('SENTRY_DSN') && !ENV_VAR_PATTERNS.SENTRY_DSN.test(trimmedValue)) {
    throw new Error(`Invalid format for ${name}: Must be a valid Sentry DSN`);
  }
  
  if (name.includes('POSTHOG_API_KEY') && !ENV_VAR_PATTERNS.POSTHOG_API_KEY.test(trimmedValue)) {
    throw new Error(`Invalid format for ${name}: Must be a valid PostHog API key`);
  }

  // Check for placeholder values that shouldn't be in production
  if (isProduction() && isPlaceholderValue(trimmedValue)) {
    throw new Error(`Production environment contains placeholder value for ${name}`);
  }

  return trimmedValue;
}

/**
 * Checks if a value appears to be a placeholder
 */
function isPlaceholderValue(value: string): boolean {
  const placeholderPatterns = [
    /your_.*_here/i,
    /replace.*with.*actual/i,
    /todo.*replace/i,
    /example/i,
    /test.*key/i,
    /dummy/i,
    /placeholder/i,
  ];
  
  return placeholderPatterns.some(pattern => pattern.test(value));
}

/**
 * Gets an optional environment variable with a default value
 */
function getEnvVar(name: keyof CustomProcessEnv, defaultValue: string): string {
  const value = process.env[name];
  const result = value && value.trim() !== '' ? value.trim() : defaultValue;
  
  // Validate format if it's not the default value
  if (result !== defaultValue) {
    if (name.includes('URL') && !ENV_VAR_PATTERNS.URL.test(result)) {
      console.warn(`⚠️  Warning: ${name} may have invalid URL format: ${result}`);
    }
  }
  
  return result;
}

/**
 * Validates that sensitive environment variables are not exposed to client
 */
function validateServerOnlyVars(): void {
  if (typeof window !== 'undefined') {
    // We're on the client side - check for exposed sensitive variables
    SENSITIVE_SERVER_ONLY_VARS.forEach(varName => {
      if (process.env[varName]) {
        console.error(`🚨 SECURITY VIOLATION: Sensitive variable ${varName} is exposed to client!`);
        throw new Error(`Security violation: ${varName} must not be exposed to client`);
      }
    });
  }
}

/**
 * Validates production environment requirements
 */
function validateProductionRequirements(): void {
  if (!isProduction()) return;

  const missingVars: string[] = [];
  
  REQUIRED_PRODUCTION_VARS.forEach(varName => {
    const value = process.env[varName];
    if (!value || value.trim() === '' || isPlaceholderValue(value)) {
      missingVars.push(varName);
    }
  });

  if (missingVars.length > 0) {
    const errorMessage = `🔒 PRODUCTION SECURITY: Missing or invalid required environment variables: ${missingVars.join(', ')}`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }
}

/**
 * Validates environment configuration and returns typed config object
 * Implements comprehensive security checks
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  try {
    // Perform security validations first
    validateServerOnlyVars();
    validateProductionRequirements();

    const config: EnvironmentConfig = {
      supabase: {
        url: requireEnvVar('NEXT_PUBLIC_SUPABASE_URL', process.env.NEXT_PUBLIC_SUPABASE_URL),
        anonKey: requireEnvVar('NEXT_PUBLIC_SUPABASE_ANON_KEY', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY),
      },
      sentry: {
        dsn: requireEnvVar('NEXT_PUBLIC_SENTRY_DSN', process.env.NEXT_PUBLIC_SENTRY_DSN),
        environment: (process.env.NEXT_PUBLIC_SENTRY_ENVIRONMENT as 'development' | 'staging' | 'production') || 'development',
      },
      posthog: {
        apiKey: requireEnvVar('NEXT_PUBLIC_POSTHOG_API_KEY', process.env.NEXT_PUBLIC_POSTHOG_API_KEY),
        apiHost: getEnvVar('NEXT_PUBLIC_POSTHOG_API_HOST', 'https://us.i.posthog.com'),
      },
      app: {
        environment: process.env.NODE_ENV || 'development',
        version: process.env.npm_package_version || '0.0.0',
      },
    };

    // Log successful validation in production
    if (isProduction()) {
      console.log('✅ Environment configuration validated successfully with security checks');
    }

    return config;
  } catch (error) {
    console.error('❌ Environment configuration error:', error);
    
    // In production, exit the process for security violations
    if (isProduction() && error instanceof Error && error.message.includes('SECURITY')) {
      console.error('🚨 Exiting due to security violation in production environment');
      process.exit(1);
    }
    
    throw error;
  }
}

/**
 * Gets secure server-side configuration (server-only variables)
 * This function should only be called on the server side
 */
export function getSecureServerConfig(): SecureEnvironmentConfig {
  if (typeof window !== 'undefined') {
    throw new Error('🚨 SECURITY: getSecureServerConfig() must only be called on server side');
  }

  return {
    sentry: {
      authToken: process.env.SENTRY_AUTH_TOKEN,
      org: process.env.SENTRY_ORG,
      project: process.env.SENTRY_PROJECT,
    },
    supabase: {
      serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
    },
    security: {
      corsOrigin: process.env.CORS_ORIGIN,
      trustedHosts: process.env.TRUSTED_HOSTS?.split(',').map(host => host.trim()) || [],
      cspReportUri: process.env.CSP_REPORT_URI,
    },
    database: {
      url: process.env.DATABASE_URL,
      connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000'),
      maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
    },
  };
}

/**
 * Checks if we're in development mode
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development';
}

/**
 * Checks if we're in production mode
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}

/**
 * Checks if we're in test mode
 */
export function isTest(): boolean {
  return process.env.NODE_ENV === 'test';
}

/**
 * Gets the current environment name
 */
export function getEnvironment(): string {
  return process.env.NODE_ENV || 'development';
}

/**
 * Validates environment configuration on startup with comprehensive security checks
 * Call this early in your application to catch configuration issues
 */
export function validateEnvironment(): void {
  try {
    // Validate client-side configuration
    getEnvironmentConfig();
    
    // Validate server-side configuration if on server
    if (typeof window === 'undefined') {
      getSecureServerConfig();
    }
    
    // Additional runtime checks
    performRuntimeSecurityChecks();
    
    console.log('✅ Environment configuration validated successfully with security checks');
  } catch (error) {
    console.error('❌ Environment configuration validation failed:', error);
    
    // In production, always exit on validation failure
    if (isProduction()) {
      console.error('🚨 Exiting due to environment validation failure in production');
      process.exit(1);
    }
    
    // In development, only exit for security violations
    if (error instanceof Error && error.message.includes('SECURITY')) {
      console.error('🚨 Exiting due to security violation');
      process.exit(1);
    }
  }
}

/**
 * Performs additional runtime security checks
 */
function performRuntimeSecurityChecks(): void {
  // Check for common security misconfigurations
  if (isProduction()) {
    // Ensure NODE_ENV is properly set
    if (process.env.NODE_ENV !== 'production') {
      throw new Error('🔒 SECURITY: NODE_ENV must be set to "production" in production environment');
    }
    
    // Ensure debug flags are disabled
    if (process.env.DEBUG === 'true' || process.env.NEXT_PUBLIC_DEBUG === 'true') {
      console.warn('⚠️  Warning: Debug flags are enabled in production');
    }
    
    // Check for development-only features
    if (process.env.FEATURE_ANALYTICS_DEBUG === 'true' || process.env.FEATURE_SENTRY_DEBUG === 'true') {
      console.warn('⚠️  Warning: Debug features are enabled in production');
    }
  }
}

/**
 * Sanitizes environment variables for logging (removes sensitive data)
 */
export function sanitizeEnvForLogging(): Record<string, string> {
  const sanitized: Record<string, string> = {};
  
  Object.entries(process.env).forEach(([key, value]) => {
    if (!value) return;
    
    // Skip sensitive variables entirely
    if (SENSITIVE_SERVER_ONLY_VARS.some(sensitiveVar => key.includes(sensitiveVar))) {
      sanitized[key] = '[REDACTED]';
      return;
    }
    
    // Partially redact API keys and tokens
    if (key.includes('KEY') || key.includes('TOKEN') || key.includes('SECRET')) {
      sanitized[key] = value.length > 8 
        ? `${value.substring(0, 4)}...${value.substring(value.length - 4)}`
        : '[REDACTED]';
      return;
    }
    
    // Show non-sensitive variables
    sanitized[key] = value;
  });
  
  return sanitized;
}

/**
 * Validates a specific environment variable at runtime
 */
export function validateEnvVar(name: string, value: string | undefined, required = false): boolean {
  try {
    if (required) {
      requireEnvVar(name as keyof CustomProcessEnv, value);
    }
    return true;
  } catch {
    return false;
  }
}