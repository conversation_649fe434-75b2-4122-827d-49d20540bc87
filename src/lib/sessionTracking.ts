/**
 * User session tracking utilities for enhanced error context and analytics
 */

import { setUserSessionContext, addUserInteractionBreadcrumb, type UserSessionContext } from './sentry';
import { addBreadcrumb } from './sentry';

// Session storage keys
const SESSION_STORAGE_KEYS = {
  SESSION_ID: 'ja_session_id',
  SESSION_START: 'ja_session_start',
  PAGE_VIEWS: 'ja_page_views',
  USER_INTERACTIONS: 'ja_user_interactions',
} as const;

// User interaction types
export enum UserInteractionType {
  CLICK = 'click',
  FORM_SUBMIT = 'form_submit',
  FORM_FOCUS = 'form_focus',
  SCROLL = 'scroll',
  NAVIGATION = 'navigation',
  CTA_CLICK = 'cta_click',
  CONTACT_FORM = 'contact_form',
  BOOKING_INTERACTION = 'booking_interaction',
}

// Session data interface
export interface SessionData {
  sessionId: string;
  startTime: number;
  pageViews: PageView[];
  userInteractions: UserInteraction[];
  userAgent: string;
  referrer?: string;
  userId?: string;
  userEmail?: string;
  userRole?: string;
}

// Page view tracking
export interface PageView {
  url: string;
  title: string;
  timestamp: number;
  referrer?: string;
  section: string;
  timeOnPage?: number;
}

// User interaction tracking
export interface UserInteraction {
  type: UserInteractionType;
  element: string;
  page: string;
  timestamp: number;
  data?: Record<string, any>;
}

// Session manager class
export class SessionManager {
  private static instance: SessionManager;
  private sessionData: SessionData;
  private currentPageView: PageView | null = null;
  private interactionCount = 0;

  private constructor() {
    this.sessionData = this.initializeSession();
    this.setupEventListeners();
    this.updateSentryContext();
  }

  // Singleton pattern
  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  // Initialize or restore session
  private initializeSession(): SessionData {
    if (typeof window === 'undefined') {
      return this.createNewSession();
    }

    const existingSessionId = sessionStorage.getItem(SESSION_STORAGE_KEYS.SESSION_ID);
    const sessionStart = sessionStorage.getItem(SESSION_STORAGE_KEYS.SESSION_START);
    
    // Check if session is still valid (less than 30 minutes old)
    const sessionTimeout = 30 * 60 * 1000; // 30 minutes
    const now = Date.now();
    
    if (existingSessionId && sessionStart && (now - parseInt(sessionStart)) < sessionTimeout) {
      return this.restoreSession(existingSessionId, parseInt(sessionStart));
    } else {
      return this.createNewSession();
    }
  }

  // Create a new session
  private createNewSession(): SessionData {
    const sessionId = this.generateSessionId();
    const startTime = Date.now();
    
    const sessionData: SessionData = {
      sessionId,
      startTime,
      pageViews: [],
      userInteractions: [],
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server',
      referrer: typeof document !== 'undefined' ? document.referrer : undefined,
    };

    if (typeof window !== 'undefined') {
      sessionStorage.setItem(SESSION_STORAGE_KEYS.SESSION_ID, sessionId);
      sessionStorage.setItem(SESSION_STORAGE_KEYS.SESSION_START, startTime.toString());
    }

    addBreadcrumb(
      'New session started',
      'session',
      'info',
      {
        session_id: sessionId,
        start_time: startTime,
        user_agent: sessionData.userAgent,
        referrer: sessionData.referrer,
      }
    );

    return sessionData;
  }

  // Restore existing session
  private restoreSession(sessionId: string, startTime: number): SessionData {
    const pageViews = this.loadFromStorage<PageView[]>(SESSION_STORAGE_KEYS.PAGE_VIEWS, []);
    const userInteractions = this.loadFromStorage<UserInteraction[]>(SESSION_STORAGE_KEYS.USER_INTERACTIONS, []);

    const sessionData: SessionData = {
      sessionId,
      startTime,
      pageViews,
      userInteractions,
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server',
      referrer: typeof document !== 'undefined' ? document.referrer : undefined,
    };

    addBreadcrumb(
      'Session restored',
      'session',
      'info',
      {
        session_id: sessionId,
        start_time: startTime,
        page_views_count: pageViews.length,
        interactions_count: userInteractions.length,
      }
    );

    return sessionData;
  }

  // Generate unique session ID
  private generateSessionId(): string {
    return `ja_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Load data from session storage
  private loadFromStorage<T>(key: string, defaultValue: T): T {
    if (typeof window === 'undefined') return defaultValue;
    
    try {
      const stored = sessionStorage.getItem(key);
      return stored ? JSON.parse(stored) : defaultValue;
    } catch {
      return defaultValue;
    }
  }

  // Save data to session storage
  private saveToStorage<T>(key: string, data: T): void {
    if (typeof window === 'undefined') return;
    
    try {
      sessionStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save to session storage:', error);
    }
  }

  // Set up event listeners for automatic tracking
  private setupEventListeners(): void {
    if (typeof window === 'undefined') return;

    // Track page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden' && this.currentPageView) {
        this.endCurrentPageView();
      }
    });

    // Track beforeunload for page view duration
    window.addEventListener('beforeunload', () => {
      if (this.currentPageView) {
        this.endCurrentPageView();
      }
    });

    // Track clicks on important elements
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (target) {
        // Safely get className as string
        let className = '';
        if (target.className) {
          className = typeof target.className === 'string' 
            ? target.className 
            : String(target.className);
        }
        
        this.trackUserInteraction(
          UserInteractionType.CLICK,
          this.getElementDescription(target),
          { 
            tag: target.tagName.toLowerCase(),
            className,
            id: target.id,
            text: target.textContent?.slice(0, 100),
          }
        );
      }
    });

    // Track form submissions
    document.addEventListener('submit', (event) => {
      const form = event.target as HTMLFormElement;
      if (form) {
        this.trackUserInteraction(
          UserInteractionType.FORM_SUBMIT,
          this.getElementDescription(form),
          {
            action: form.action,
            method: form.method,
            formId: form.id,
          }
        );
      }
    });
  }

  // Get a description of an HTML element
  private getElementDescription(element: HTMLElement): string {
    const tag = element.tagName.toLowerCase();
    const id = element.id ? `#${element.id}` : '';
    
    // Handle className safely - it could be a string or DOMTokenList
    let className = '';
    if (element.className) {
      if (typeof element.className === 'string') {
        className = element.className ? `.${element.className.split(' ').join('.')}` : '';
      } else {
        // Handle DOMTokenList (SVG elements, etc.)
        const classStr = String(element.className);
        className = classStr ? `.${classStr.split(' ').join('.')}` : '';
      }
    }
    
    const text = element.textContent?.slice(0, 30) || '';
    
    return `${tag}${id}${className}${text ? ` "${text}"` : ''}`;
  }

  // Update Sentry context with current session
  private updateSentryContext(): void {
    const sessionContext: UserSessionContext = {
      sessionId: this.sessionData.sessionId,
      userAgent: this.sessionData.userAgent,
      timestamp: this.sessionData.startTime,
      page: typeof window !== 'undefined' ? window.location.pathname : 'server',
      referrer: this.sessionData.referrer,
      userId: this.sessionData.userId,
      email: this.sessionData.userEmail,
      role: this.sessionData.userRole,
    };

    setUserSessionContext(sessionContext);
  }

  // Track page view
  trackPageView(url?: string, title?: string, section?: string): void {
    // End current page view if exists
    if (this.currentPageView) {
      this.endCurrentPageView();
    }

    const pageView: PageView = {
      url: url || (typeof window !== 'undefined' ? window.location.href : 'server'),
      title: title || (typeof document !== 'undefined' ? document.title : 'Unknown'),
      timestamp: Date.now(),
      referrer: typeof document !== 'undefined' ? document.referrer : undefined,
      section: section || (typeof window !== 'undefined' ? window.location.pathname : 'server'),
    };

    this.sessionData.pageViews.push(pageView);
    this.currentPageView = pageView;
    
    this.saveToStorage(SESSION_STORAGE_KEYS.PAGE_VIEWS, this.sessionData.pageViews);
    this.updateSentryContext();

    addBreadcrumb(
      `Page view: ${pageView.title}`,
      'navigation',
      'info',
      {
        url: pageView.url,
        section: pageView.section,
        referrer: pageView.referrer,
      }
    );
  }

  // End current page view and calculate time on page
  private endCurrentPageView(): void {
    if (!this.currentPageView) return;

    const timeOnPage = Date.now() - this.currentPageView.timestamp;
    this.currentPageView.timeOnPage = timeOnPage;
    
    // Update the page view in the array
    const lastIndex = this.sessionData.pageViews.length - 1;
    if (lastIndex >= 0) {
      this.sessionData.pageViews[lastIndex] = this.currentPageView;
      this.saveToStorage(SESSION_STORAGE_KEYS.PAGE_VIEWS, this.sessionData.pageViews);
    }

    addBreadcrumb(
      `Page view ended: ${this.currentPageView.title}`,
      'navigation',
      'info',
      {
        url: this.currentPageView.url,
        time_on_page: timeOnPage,
      }
    );

    this.currentPageView = null;
  }

  // Track user interaction
  trackUserInteraction(
    type: UserInteractionType,
    element: string,
    data?: Record<string, any>
  ): void {
    const interaction: UserInteraction = {
      type,
      element,
      page: typeof window !== 'undefined' ? window.location.pathname : 'server',
      timestamp: Date.now(),
      data,
    };

    this.sessionData.userInteractions.push(interaction);
    this.interactionCount++;
    
    // Keep only last 100 interactions to prevent storage bloat
    if (this.sessionData.userInteractions.length > 100) {
      this.sessionData.userInteractions = this.sessionData.userInteractions.slice(-100);
    }
    
    this.saveToStorage(SESSION_STORAGE_KEYS.USER_INTERACTIONS, this.sessionData.userInteractions);

    // Add to Sentry breadcrumbs
    addUserInteractionBreadcrumb(type, element, interaction.page, data);
  }

  // Set user information
  setUser(userId?: string, email?: string, role?: string): void {
    this.sessionData.userId = userId;
    this.sessionData.userEmail = email;
    this.sessionData.userRole = role;
    
    this.updateSentryContext();

    addBreadcrumb(
      'User information updated',
      'user',
      'info',
      {
        user_id: userId,
        email: email,
        role: role,
      }
    );
  }

  // Get session data
  getSessionData(): SessionData {
    return { ...this.sessionData };
  }

  // Get session summary for debugging
  getSessionSummary(): {
    sessionId: string;
    duration: number;
    pageViews: number;
    interactions: number;
    currentPage: string;
  } {
    return {
      sessionId: this.sessionData.sessionId,
      duration: Date.now() - this.sessionData.startTime,
      pageViews: this.sessionData.pageViews.length,
      interactions: this.sessionData.userInteractions.length,
      currentPage: typeof window !== 'undefined' ? window.location.pathname : 'server',
    };
  }

  // Clear session data
  clearSession(): void {
    if (typeof window !== 'undefined') {
      Object.values(SESSION_STORAGE_KEYS).forEach(key => {
        sessionStorage.removeItem(key);
      });
    }
    
    this.sessionData = this.createNewSession();
    this.currentPageView = null;
    this.interactionCount = 0;
    this.updateSentryContext();
  }
}

// Utility functions for easy access
export const getSessionManager = (): SessionManager => {
  return SessionManager.getInstance();
};

export const trackPageView = (url?: string, title?: string, section?: string): void => {
  getSessionManager().trackPageView(url, title, section);
  
  // Also track in analytics
  if (typeof window !== 'undefined') {
    import('./analytics').then(({ trackPageView: analyticsTrackPageView }) => {
      analyticsTrackPageView(
        url || window.location.pathname,
        section || window.location.pathname.replace('/', '') || 'home',
        document.referrer || undefined
      );
    });
  }
};

export const trackUserInteraction = (
  type: UserInteractionType,
  element: string,
  data?: Record<string, any>
): void => {
  getSessionManager().trackUserInteraction(type, element, data);
};

export const setUser = (userId?: string, email?: string, role?: string): void => {
  getSessionManager().setUser(userId, email, role);
};

export const getSessionSummary = () => {
  return getSessionManager().getSessionSummary();
};

// Initialize session tracking
export const initSessionTracking = (): void => {
  if (typeof window !== 'undefined') {
    getSessionManager();
  }
};

export default SessionManager;