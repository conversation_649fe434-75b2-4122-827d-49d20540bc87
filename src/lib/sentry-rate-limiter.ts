/**
 * Sentry Rate Limiter Utility
 * Helps prevent hitting Sentry's rate limits by implementing client-side throttling
 */

interface RateLimitConfig {
  maxEventsPerMinute: number;
  maxTransactionsPerMinute: number;
  maxErrorsPerMinute: number;
}

class SentryRateLimiter {
  private eventCounts: Map<string, number[]> = new Map();
  private config: RateLimitConfig;

  constructor(config?: Partial<RateLimitConfig>) {
    this.config = {
      maxEventsPerMinute: process.env.NODE_ENV === 'production' ? 100 : 50,
      maxTransactionsPerMinute: process.env.NODE_ENV === 'production' ? 50 : 25,
      maxErrorsPerMinute: process.env.NODE_ENV === 'production' ? 20 : 10,
      ...config,
    };
  }

  private cleanOldEntries(eventType: string): void {
    const now = Date.now();
    const oneMinuteAgo = now - 60000; // 1 minute in milliseconds
    
    const timestamps = this.eventCounts.get(eventType) || [];
    const recentTimestamps = timestamps.filter(timestamp => timestamp > oneMinuteAgo);
    
    this.eventCounts.set(eventType, recentTimestamps);
  }

  private addEvent(eventType: string): void {
    this.cleanOldEntries(eventType);
    
    const timestamps = this.eventCounts.get(eventType) || [];
    timestamps.push(Date.now());
    this.eventCounts.set(eventType, timestamps);
  }

  private getEventCount(eventType: string): number {
    this.cleanOldEntries(eventType);
    return this.eventCounts.get(eventType)?.length || 0;
  }

  shouldAllowEvent(eventType: 'error' | 'transaction' | 'other' = 'other'): boolean {
    const currentCount = this.getEventCount(eventType);
    
    let maxAllowed: number;
    switch (eventType) {
      case 'error':
        maxAllowed = this.config.maxErrorsPerMinute;
        break;
      case 'transaction':
        maxAllowed = this.config.maxTransactionsPerMinute;
        break;
      default:
        maxAllowed = this.config.maxEventsPerMinute;
    }

    if (currentCount >= maxAllowed) {
      console.warn(`Sentry rate limit reached for ${eventType} events (${currentCount}/${maxAllowed})`);
      return false;
    }

    this.addEvent(eventType);
    return true;
  }

  getStats(): Record<string, number> {
    return {
      errors: this.getEventCount('error'),
      transactions: this.getEventCount('transaction'),
      other: this.getEventCount('other'),
    };
  }

  reset(): void {
    this.eventCounts.clear();
  }
}

// Create a singleton instance
export const sentryRateLimiter = new SentryRateLimiter();

// Helper functions for use in Sentry configuration
export const shouldAllowError = () => sentryRateLimiter.shouldAllowEvent('error');
export const shouldAllowTransaction = () => sentryRateLimiter.shouldAllowEvent('transaction');
export const shouldAllowOtherEvent = () => sentryRateLimiter.shouldAllowEvent('other');

// Development helper to monitor rate limiting
if (process.env.NODE_ENV === 'development') {
  // Log stats every 30 seconds in development
  setInterval(() => {
    const stats = sentryRateLimiter.getStats();
    if (stats.errors > 0 || stats.transactions > 0 || stats.other > 0) {
      console.log('Sentry Rate Limiter Stats (last minute):', stats);
    }
  }, 30000);
}