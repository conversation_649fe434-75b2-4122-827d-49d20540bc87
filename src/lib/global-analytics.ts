/**
 * Global Analytics System with PostHog Bypass
 * 
 * This module creates a robust analytics system that works even when PostHog
 * is blocked by ad blockers. It exposes analytics functions globally and
 * ensures events are tracked reliably.
 */

import { isDevelopment } from './env';

// Global analytics interface
export interface GlobalAnalytics {
  initializeAnalytics: () => Promise<boolean>;
  trackEvent: (event: string, properties?: Record<string, any>) => void;
  identify: (userId: string, properties?: Record<string, any>) => void;
  reset: () => void;
  isReady: () => boolean;
  getStatus: () => AnalyticsStatus;
  sendTestEvent: () => void;
}

export interface AnalyticsStatus {
  initialized: boolean;
  mode: 'normal' | 'bypass' | 'disabled';
  ready: boolean;
  lastError?: string;
  eventsSent: number;
  distinctId?: string;
}

// PostHog configuration
const POSTHOG_CONFIG = {
  apiKey: process.env.NEXT_PUBLIC_POSTHOG_API_KEY || '',
  apiHost: process.env.NEXT_PUBLIC_POSTHOG_API_HOST || 'https://us.i.posthog.com',
  debug: isDevelopment()
};

// Global state
let analyticsState: AnalyticsStatus = {
  initialized: false,
  mode: 'disabled',
  ready: false,
  eventsSent: 0
};

let posthogInstance: any = null;
let bypassInstance: PostHogBypass | null = null;

/**
 * PostHog Bypass Implementation
 */
class PostHogBypass {
  private config: typeof POSTHOG_CONFIG;
  private distinctId: string;
  private sessionId: string;
  private eventQueue: Array<{ event: string; properties: Record<string, any>; timestamp: number }> = [];
  private isProcessing = false;

  constructor(config: typeof POSTHOG_CONFIG) {
    this.config = config;
    this.distinctId = this.generateDistinctId();
    this.sessionId = this.generateSessionId();
    this.loadStoredState();
  }

  async init(): Promise<boolean> {
    try {
      console.log('🚀 Initializing PostHog bypass mode...');
      
      // Test connectivity
      const isConnected = await this.testConnectivity();
      if (!isConnected) {
        console.warn('⚠️ PostHog API not accessible, events will be queued');
      }

      // Start processing queue
      this.startQueueProcessor();

      console.log('✅ PostHog bypass initialized successfully', {
        distinctId: this.distinctId,
        sessionId: this.sessionId,
        connected: isConnected
      });

      return true;
    } catch (error) {
      console.error('❌ PostHog bypass initialization failed:', error);
      return false;
    }
  }

  capture(event: string, properties: Record<string, any> = {}): void {
    const eventData = {
      event,
      properties: {
        ...properties,
        distinct_id: this.distinctId,
        $session_id: this.sessionId,
        $lib: 'posthog-bypass',
        $lib_version: '1.0.0',
        $current_url: typeof window !== 'undefined' ? window.location.href : '',
        $host: typeof window !== 'undefined' ? window.location.hostname : '',
        $pathname: typeof window !== 'undefined' ? window.location.pathname : '',
        $browser: typeof window !== 'undefined' ? navigator.userAgent : '',
        $timestamp: new Date().toISOString(),
        $time: Date.now() / 1000,
      },
      timestamp: Date.now()
    };

    this.eventQueue.push(eventData);
    analyticsState.eventsSent++;

    if (this.config.debug) {
      console.log('📊 Event queued for bypass:', eventData);
    }

    // Process queue if not already processing
    if (!this.isProcessing) {
      this.processQueue();
    }
  }

  identify(userId: string, properties: Record<string, any> = {}): void {
    this.distinctId = userId;
    this.saveStoredState();
    this.capture('$identify', { $anon_distinct_id: this.distinctId, ...properties });
  }

  reset(): void {
    this.distinctId = this.generateDistinctId();
    this.sessionId = this.generateSessionId();
    this.eventQueue = [];
    this.clearStoredState();
  }

  get_distinct_id(): string {
    return this.distinctId;
  }

  get_session_id(): string {
    return this.sessionId;
  }

  private async testConnectivity(): Promise<boolean> {
    try {
      await fetch(`${this.config.apiHost}/decide/`, {
        method: 'HEAD',
        mode: 'no-cors'
      });
      return true;
    } catch {
      return false;
    }
  }

  private startQueueProcessor(): void {
    // Process queue every 5 seconds
    setInterval(() => {
      if (this.eventQueue.length > 0 && !this.isProcessing) {
        this.processQueue();
      }
    }, 5000);
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.eventQueue.length === 0) return;

    this.isProcessing = true;
    const eventsToProcess = [...this.eventQueue];
    this.eventQueue = [];

    try {
      await this.sendEventBatch(eventsToProcess);
      if (this.config.debug) {
        console.log(`📊 Successfully sent ${eventsToProcess.length} events via bypass`);
      }
    } catch (error) {
      // Re-queue events if sending failed
      this.eventQueue.unshift(...eventsToProcess);
      if (this.config.debug) {
        console.warn('⚠️ Failed to send events, re-queued:', error);
      }
    } finally {
      this.isProcessing = false;
    }
  }

  private async sendEventBatch(events: Array<{ event: string; properties: Record<string, any>; timestamp: number }>): Promise<void> {
    const payload = {
      api_key: this.config.apiKey,
      batch: events.map(({ event, properties }) => ({
        event,
        properties,
        timestamp: new Date().toISOString()
      }))
    };

    // Try multiple endpoints to bypass ad blockers
    const endpoints = [
      `${this.config.apiHost}/batch/`,
      `${this.config.apiHost}/capture/`,
      // Alternative endpoint
      this.config.apiHost.replace('us.i.posthog.com', 'app.posthog.com') + '/batch/',
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'PostHog-Bypass/1.0.0',
          },
          body: JSON.stringify(payload),
          credentials: 'omit',
          mode: 'cors',
        });

        if (response.ok) {
          return; // Success
        }
      } catch (error) {
        continue; // Try next endpoint
      }
    }

    throw new Error('All endpoints failed');
  }

  private generateDistinctId(): string {
    return `bypass_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  }

  private loadStoredState(): void {
    if (typeof window === 'undefined') return;

    try {
      const stored = localStorage.getItem('posthog_bypass_state');
      if (stored) {
        const state = JSON.parse(stored);
        this.distinctId = state.distinctId || this.distinctId;
        this.sessionId = state.sessionId || this.sessionId;
      }
    } catch (error) {
      console.warn('⚠️ Failed to load stored state:', error);
    }
  }

  private saveStoredState(): void {
    if (typeof window === 'undefined') return;

    try {
      const state = {
        distinctId: this.distinctId,
        sessionId: this.sessionId,
        timestamp: Date.now()
      };
      localStorage.setItem('posthog_bypass_state', JSON.stringify(state));
    } catch (error) {
      console.warn('⚠️ Failed to save state:', error);
    }
  }

  private clearStoredState(): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.removeItem('posthog_bypass_state');
    } catch (error) {
      console.warn('⚠️ Failed to clear stored state:', error);
    }
  }
}

/**
 * Initialize analytics system
 */
async function initializeAnalytics(): Promise<boolean> {
  if (typeof window === 'undefined') return false;

  try {
    console.log('🚀 Initializing global analytics system...');

    // Validate configuration
    if (!POSTHOG_CONFIG.apiKey || POSTHOG_CONFIG.apiKey === 'placeholder-api-key') {
      console.warn('⚠️ PostHog API key not configured, analytics disabled');
      analyticsState.mode = 'disabled';
      return false;
    }

    // Try to load PostHog normally first
    try {
      const { default: posthog } = await import('posthog-js');
      
      // Initialize PostHog
      posthog.init(POSTHOG_CONFIG.apiKey, {
        api_host: POSTHOG_CONFIG.apiHost,
        debug: POSTHOG_CONFIG.debug,
        opt_out_capturing_by_default: false,
        capture_pageview: false,
        request_batching: false,
        loaded: (instance) => {
          posthogInstance = instance;
          analyticsState.mode = 'normal';
          analyticsState.initialized = true;
          analyticsState.ready = true;
          analyticsState.distinctId = instance.get_distinct_id();
          
          console.log('✅ PostHog normal mode initialized');
        }
      });

      // Wait a bit for initialization
      await new Promise(resolve => setTimeout(resolve, 2000));

      if (posthogInstance && analyticsState.ready) {
        return true;
      }
    } catch (error) {
      console.warn('⚠️ Normal PostHog initialization failed, using bypass mode:', error);
    }

    // Fall back to bypass mode
    console.log('🚧 Initializing PostHog bypass mode...');
    bypassInstance = new PostHogBypass(POSTHOG_CONFIG);
    const bypassSuccess = await bypassInstance.init();

    if (bypassSuccess) {
      analyticsState.mode = 'bypass';
      analyticsState.initialized = true;
      analyticsState.ready = true;
      analyticsState.distinctId = bypassInstance.get_distinct_id();
      
      console.log('✅ PostHog bypass mode initialized');
      return true;
    }

    throw new Error('Both normal and bypass initialization failed');

  } catch (error) {
    console.error('❌ Analytics initialization failed:', error);
    analyticsState.lastError = error instanceof Error ? error.message : String(error);
    analyticsState.mode = 'disabled';
    return false;
  }
}

/**
 * Track an event
 */
function trackEvent(event: string, properties: Record<string, any> = {}): void {
  if (!analyticsState.ready) {
    console.warn('📊 Analytics not ready, event not tracked:', event);
    return;
  }

  try {
    const eventProps = {
      ...properties,
      timestamp: new Date().toISOString(),
      environment: isDevelopment() ? 'development' : 'production',
    };

    if (analyticsState.mode === 'normal' && posthogInstance) {
      posthogInstance.capture(event, eventProps);
    } else if (analyticsState.mode === 'bypass' && bypassInstance) {
      bypassInstance.capture(event, eventProps);
    }

    analyticsState.eventsSent++;

    if (POSTHOG_CONFIG.debug) {
      console.log(`📊 Event tracked (${analyticsState.mode} mode):`, event, eventProps);
    }
  } catch (error) {
    console.warn('📊 Event tracking failed:', event, error);
  }
}

/**
 * Identify a user
 */
function identify(userId: string, properties: Record<string, any> = {}): void {
  if (!analyticsState.ready) return;

  try {
    if (analyticsState.mode === 'normal' && posthogInstance) {
      posthogInstance.identify(userId, properties);
    } else if (analyticsState.mode === 'bypass' && bypassInstance) {
      bypassInstance.identify(userId, properties);
    }

    analyticsState.distinctId = userId;

    if (POSTHOG_CONFIG.debug) {
      console.log(`👤 User identified (${analyticsState.mode} mode):`, userId, properties);
    }
  } catch (error) {
    console.warn('👤 User identification failed:', error);
  }
}

/**
 * Reset analytics session
 */
function reset(): void {
  if (!analyticsState.ready) return;

  try {
    if (analyticsState.mode === 'normal' && posthogInstance) {
      posthogInstance.reset();
    } else if (analyticsState.mode === 'bypass' && bypassInstance) {
      bypassInstance.reset();
    }

    analyticsState.distinctId = undefined;

    if (POSTHOG_CONFIG.debug) {
      console.log(`🔄 Analytics session reset (${analyticsState.mode} mode)`);
    }
  } catch (error) {
    console.warn('🔄 Session reset failed:', error);
  }
}

/**
 * Check if analytics is ready
 */
function isReady(): boolean {
  return analyticsState.ready;
}

/**
 * Get analytics status
 */
function getStatus(): AnalyticsStatus {
  return { ...analyticsState };
}

/**
 * Send a test event
 */
function sendTestEvent(): void {
  const testId = Math.random().toString(36).substring(7);
  trackEvent('test_event', {
    test_id: testId,
    test_type: 'manual_test',
    mode: analyticsState.mode,
    timestamp: new Date().toISOString(),
    source: 'global_analytics_test'
  });
  
  console.log(`🧪 Test event sent with ID: ${testId} (${analyticsState.mode} mode)`);
}

/**
 * Create global analytics object
 */
const globalAnalytics: GlobalAnalytics = {
  initializeAnalytics,
  trackEvent,
  identify,
  reset,
  isReady,
  getStatus,
  sendTestEvent
};

/**
 * Expose analytics functions globally
 */
export function exposeGlobalAnalytics(): void {
  if (typeof window === 'undefined') return;

  // Expose individual functions
  (window as any).initializeAnalytics = initializeAnalytics;
  (window as any).trackEvent = trackEvent;
  (window as any).identify = identify;
  (window as any).resetAnalytics = reset;
  (window as any).isAnalyticsReady = isReady;
  (window as any).getAnalyticsStatus = getStatus;
  (window as any).sendTestEvent = sendTestEvent;

  // Expose the full analytics object
  (window as any).analytics = globalAnalytics;

  console.log('🌐 Global analytics functions exposed to window object');
}

/**
 * Initialize the global analytics system
 * This function should be called from a client-side component
 */
export function initializeGlobalAnalytics(): void {
  if (typeof window === 'undefined') return;

  // Expose functions immediately
  exposeGlobalAnalytics();
  
  // Initialize analytics after a short delay
  setTimeout(async () => {
    try {
      const success = await initializeAnalytics();
      if (success) {
        console.log('✅ Global analytics system ready');
        
        // Send initialization event
        trackEvent('analytics_initialized', {
          mode: analyticsState.mode,
          success: true,
          timestamp: new Date().toISOString()
        });
      } else {
        console.warn('⚠️ Global analytics system failed to initialize');
      }
    } catch (error) {
      console.error('❌ Global analytics initialization error:', error);
    }
  }, 1000);
}

export default globalAnalytics;