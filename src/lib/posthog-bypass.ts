/**
 * PostHog Ad-Blocker Bypass Implementation
 * 
 * This module provides alternative loading strategies for PostHog analytics
 * to bypass ad blockers and content filters in production environments.
 */

import { isDevelopment } from './env';

export interface PostHogBypassConfig {
  apiKey: string;
  apiHost: string;
  debug?: boolean;
}

export interface PostHogBypassInstance {
  capture: (event: string, properties?: Record<string, any>) => void;
  identify: (userId: string, properties?: Record<string, any>) => void;
  reset: () => void;
  opt_in_capturing: () => void;
  opt_out_capturing: () => void;
  has_opted_out_capturing: () => boolean;
  get_distinct_id: () => string;
  get_session_id: () => string;
  __loaded: boolean;
  config?: {
    api_host: string;
    token: string;
  };
}

/**
 * Custom PostHog implementation that bypasses ad blockers
 * by using direct API calls instead of loading external scripts
 */
export class PostHogBypass implements PostHogBypassInstance {
  private _config: PostHogBypassConfig;
  private distinctId: string;
  private sessionId: string;
  private optedOut: boolean = false;
  private eventQueue: Array<{ event: string; properties: Record<string, any>; timestamp: number }> = [];
  private isInitialized: boolean = false;
  
  public __loaded: boolean = false;

  constructor(config: PostHogBypassConfig) {
    this._config = config;
    this.distinctId = this.generateDistinctId();
    this.sessionId = this.generateSessionId();
    this.loadStoredState();
  }

  /**
   * Initialize the bypass PostHog instance
   */
  public async init(): Promise<boolean> {
    try {
      console.log('🚀 Initializing PostHog bypass mode...');
      
      // Set up configuration
      this._config = {
        ...this._config,
        debug: this._config.debug || isDevelopment()
      };

      // Load any stored state
      this.loadStoredState();

      // Mark as loaded
      this.__loaded = true;
      this.isInitialized = true;

      // Process any queued events
      await this.processEventQueue();

      if (this._config.debug) {
        console.log('✅ PostHog bypass initialized successfully', {
          distinctId: this.distinctId,
          sessionId: this.sessionId,
          apiHost: this._config.apiHost,
          queuedEvents: this.eventQueue.length
        });
      }

      return true;
    } catch (error) {
      console.error('❌ PostHog bypass initialization failed:', error);
      return false;
    }
  }

  /**
   * Capture an event using direct API call
   */
  public capture(event: string, properties: Record<string, any> = {}): void {
    if (this.optedOut) {
      if (this._config.debug) {
        console.log('📊 Event not captured - user opted out:', event);
      }
      return;
    }

    const eventData = {
      event,
      properties: {
        ...properties,
        distinct_id: this.distinctId,
        $session_id: this.sessionId,
        $lib: 'posthog-bypass',
        $lib_version: '1.0.0',
        $current_url: typeof window !== 'undefined' ? window.location.href : '',
        $host: typeof window !== 'undefined' ? window.location.hostname : '',
        $pathname: typeof window !== 'undefined' ? window.location.pathname : '',
        $browser: typeof window !== 'undefined' ? navigator.userAgent : '',
        $timestamp: new Date().toISOString(),
        $time: Date.now() / 1000,
      },
      timestamp: Date.now()
    };

    if (this._config.debug) {
      console.log('📊 Capturing event via bypass:', eventData);
    }

    // Add to queue for processing
    this.eventQueue.push(eventData);

    // Process immediately if initialized, otherwise queue for later
    if (this.isInitialized) {
      this.processEventQueue();
    }
  }

  /**
   * Identify a user
   */
  public identify(userId: string, properties: Record<string, any> = {}): void {
    if (this.optedOut) return;

    this.distinctId = userId;
    this.saveStoredState();

    this.capture('$identify', {
      $anon_distinct_id: this.distinctId,
      ...properties
    });

    if (this._config.debug) {
      console.log('👤 User identified via bypass:', userId, properties);
    }
  }

  /**
   * Reset user session
   */
  public reset(): void {
    this.distinctId = this.generateDistinctId();
    this.sessionId = this.generateSessionId();
    this.eventQueue = [];
    this.clearStoredState();

    if (this._config.debug) {
      console.log('🔄 PostHog bypass session reset');
    }
  }

  /**
   * Opt in to capturing
   */
  public opt_in_capturing(): void {
    this.optedOut = false;
    this.saveStoredState();

    if (this._config.debug) {
      console.log('✅ PostHog bypass opted in');
    }
  }

  /**
   * Opt out of capturing
   */
  public opt_out_capturing(): void {
    this.optedOut = true;
    this.eventQueue = [];
    this.saveStoredState();

    if (this._config.debug) {
      console.log('🚫 PostHog bypass opted out');
    }
  }

  /**
   * Check if user has opted out
   */
  public has_opted_out_capturing(): boolean {
    return this.optedOut;
  }

  /**
   * Get distinct ID
   */
  public get_distinct_id(): string {
    return this.distinctId;
  }

  /**
   * Get session ID
   */
  public get_session_id(): string {
    return this.sessionId;
  }

  /**
   * Get config (for compatibility)
   */
  public get config() {
    return {
      api_host: this._config.apiHost,
      token: this._config.apiKey
    };
  }

  /**
   * Process the event queue by sending events to PostHog API
   */
  private async processEventQueue(): Promise<void> {
    if (this.eventQueue.length === 0) return;

    const eventsToProcess = [...this.eventQueue];
    this.eventQueue = [];

    try {
      // Send events in batches
      const batchSize = 50;
      for (let i = 0; i < eventsToProcess.length; i += batchSize) {
        const batch = eventsToProcess.slice(i, i + batchSize);
        await this.sendEventBatch(batch);
      }

      if (this._config.debug) {
        console.log(`📊 Processed ${eventsToProcess.length} events via bypass`);
      }
    } catch (error) {
      // Re-queue events if sending failed
      this.eventQueue.unshift(...eventsToProcess);
      
      if (this._config.debug) {
        console.warn('⚠️ Failed to process event queue, re-queued events:', error);
      }
    }
  }

  /**
   * Send a batch of events to PostHog API
   */
  private async sendEventBatch(events: Array<{ event: string; properties: Record<string, any>; timestamp: number }>): Promise<void> {
    const payload = {
      api_key: this._config.apiKey,
      batch: events.map(({ event, properties }) => ({
        event,
        properties,
        timestamp: new Date().toISOString()
      }))
    };

    // Use different endpoints to bypass ad blockers
    const endpoints = [
      `${this._config.apiHost}/batch/`,
      `${this._config.apiHost}/capture/`,
      // Fallback to different subdomain
      this._config.apiHost.replace('us.i.posthog.com', 'app.posthog.com') + '/batch/',
    ];

    let lastError: Error | null = null;

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'PostHog-Bypass/1.0.0',
          },
          body: JSON.stringify(payload),
          // Add credentials and mode to help with CORS
          credentials: 'omit',
          mode: 'cors',
        });

        if (response.ok) {
          if (this._config.debug) {
            console.log(`📊 Successfully sent ${events.length} events to ${endpoint}`);
          }
          return; // Success, exit the retry loop
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        lastError = error as Error;
        if (this._config.debug) {
          console.warn(`⚠️ Failed to send to ${endpoint}:`, error);
        }
        continue; // Try next endpoint
      }
    }

    // If all endpoints failed, throw the last error
    if (lastError) {
      throw lastError;
    }
  }

  /**
   * Generate a unique distinct ID
   */
  private generateDistinctId(): string {
    return `bypass_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  }

  /**
   * Load stored state from localStorage
   */
  private loadStoredState(): void {
    if (typeof window === 'undefined') return;

    try {
      const stored = localStorage.getItem('posthog_bypass_state');
      if (stored) {
        const state = JSON.parse(stored);
        this.distinctId = state.distinctId || this.distinctId;
        this.sessionId = state.sessionId || this.sessionId;
        this.optedOut = state.optedOut || false;
      }
    } catch (error) {
      if (this._config.debug) {
        console.warn('⚠️ Failed to load stored state:', error);
      }
    }
  }

  /**
   * Save state to localStorage
   */
  private saveStoredState(): void {
    if (typeof window === 'undefined') return;

    try {
      const state = {
        distinctId: this.distinctId,
        sessionId: this.sessionId,
        optedOut: this.optedOut,
        timestamp: Date.now()
      };
      localStorage.setItem('posthog_bypass_state', JSON.stringify(state));
    } catch (error) {
      if (this._config.debug) {
        console.warn('⚠️ Failed to save state:', error);
      }
    }
  }

  /**
   * Clear stored state
   */
  private clearStoredState(): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.removeItem('posthog_bypass_state');
    } catch (error) {
      if (this._config.debug) {
        console.warn('⚠️ Failed to clear stored state:', error);
      }
    }
  }
}

/**
 * Create and initialize a PostHog bypass instance
 */
export async function createPostHogBypass(config: PostHogBypassConfig): Promise<PostHogBypassInstance> {
  const instance = new PostHogBypass(config);
  await instance.init();
  return instance;
}

/**
 * Check if PostHog script loading is likely to be blocked
 */
export function isPostHogLikelyBlocked(): boolean {
  if (typeof window === 'undefined') return false;

  // Check for common ad blocker indicators
  const adBlockerIndicators = [
    // Check if common ad blocker extensions are present
    () => !!(window as any).adblockDetected,
    () => !!(window as any).blockAdBlock,
    () => document.querySelector('[id*="adblock"]') !== null,
    
    // Check if PostHog domains are blocked
    () => {
      try {
        const img = new Image();
        img.src = 'https://app.posthog.com/static/array.js';
        return false; // If we get here, it's not blocked
      } catch {
        return true; // If error, likely blocked
      }
    }
  ];

  return adBlockerIndicators.some(check => {
    try {
      return check();
    } catch {
      return false;
    }
  });
}

/**
 * Test if PostHog API endpoints are accessible
 */
export async function testPostHogConnectivity(apiHost: string): Promise<boolean> {
  const testEndpoints = [
    `${apiHost}/decide/`,
    `${apiHost}/batch/`,
    apiHost.replace('us.i.posthog.com', 'app.posthog.com') + '/decide/',
  ];

  for (const endpoint of testEndpoints) {
    try {
      await fetch(endpoint, {
        method: 'HEAD',
        mode: 'no-cors', // This will always succeed but we can detect if it's blocked
        cache: 'no-cache',
      });
      
      // If we get here without error, the endpoint is likely accessible
      return true;
    } catch (error) {
      continue;
    }
  }

  return false;
}