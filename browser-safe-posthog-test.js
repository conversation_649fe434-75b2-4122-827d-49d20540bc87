// Browser-Safe PostHog Bypass Test
// This test works within browser security constraints and tests the actual deployed bypass system
// Copy and paste this into the browser console on https://jnabusinesssolutions.com

console.log("🚀 Testing PostHog Bypass System (v0.23) - Browser Safe Version");
console.log("📍 Current URL:", window.location.href);
console.log("🕐 Test started at:", new Date().toISOString());

// Test 1: Check if the page has loaded our analytics system
console.log("\n=== Test 1: Analytics System Detection ===");

// Wait for the page to fully initialize
setTimeout(() => {
  // Check if our analytics functions are available
  const hasAnalytics = typeof window.initializeAnalytics === "function";
  const hasTrackEvent = typeof window.trackEvent === "function";

  console.log("📊 Analytics functions available:", {
    initializeAnalytics: hasAnalytics,
    trackEvent: hasTrackEvent,
    globalPostHog: !!(window.posthog && window.posthog.__loaded),
  });

  // Test 2: Check PostHog initialization status
  console.log("\n=== Test 2: PostHog Status Check ===");

  if (window.posthog && window.posthog.__loaded) {
    console.log("✅ Regular PostHog loaded successfully");
    console.log("📊 PostHog details:", {
      distinctId: window.posthog.get_distinct_id?.(),
      hasOptedOut: window.posthog.has_opted_out_capturing?.(),
      apiHost: window.posthog.config?.api_host,
      apiKey: window.posthog.config?.token?.substring(0, 10) + "...",
    });
  } else {
    console.log("⚠️ Regular PostHog not loaded (expected with ad blockers)");
    console.log("🔧 Bypass system should be handling analytics");
  }

  // Test 3: Test our analytics tracking functions
  console.log("\n=== Test 3: Analytics Function Testing ===");

  if (hasTrackEvent) {
    try {
      // Test different types of events
      const testEvents = [
        {
          name: "cta_click",
          props: {
            cta_type: "bypass_test_v023",
            location: "browser_console",
            text: "Bypass System Test",
            test_timestamp: new Date().toISOString(),
          },
        },
        {
          name: "page_view",
          props: {
            page: "home",
            section: "console_test",
            referrer: document.referrer || "direct",
            test_id: "bypass_v023_" + Math.random().toString(36).substring(7),
          },
        },
      ];

      testEvents.forEach((event, index) => {
        setTimeout(() => {
          try {
            window.trackEvent(event.name, event.props);
            console.log(
              `📤 Test event ${index + 1} sent:`,
              event.name,
              event.props
            );
          } catch (error) {
            console.error(`❌ Failed to send test event ${index + 1}:`, error);
          }
        }, index * 1000); // Stagger events by 1 second
      });

      console.log("✅ Analytics function tests initiated");
    } catch (error) {
      console.error("❌ Error testing analytics functions:", error);
    }
  } else {
    console.log("⚠️ trackEvent function not available");
  }

  // Test 4: Monitor network activity
  console.log("\n=== Test 4: Network Activity Monitoring ===");

  const originalFetch = window.fetch;
  let postHogRequests = [];
  let requestCount = 0;

  // Override fetch to monitor PostHog requests
  window.fetch = function (...args) {
    const url = args[0];
    if (
      typeof url === "string" &&
      (url.includes("posthog") || url.includes("i.posthog.com"))
    ) {
      requestCount++;
      const requestInfo = {
        count: requestCount,
        url: url,
        method: args[1]?.method || "GET",
        timestamp: new Date().toISOString(),
      };
      postHogRequests.push(requestInfo);
      console.log(`🌐 PostHog request #${requestCount}:`, requestInfo);
    }
    return originalFetch.apply(this, args);
  };

  // Restore original fetch after monitoring period
  setTimeout(() => {
    window.fetch = originalFetch;

    console.log("\n=== Test Results Summary ===");
    console.log(`📊 Total PostHog requests detected: ${requestCount}`);

    if (requestCount > 0) {
      console.log("✅ PostHog bypass system is making network requests!");
      console.log("📋 Request details:", postHogRequests);
      console.log("🎯 This indicates the bypass system is working correctly");
    } else {
      console.log("⚠️ No PostHog network requests detected");
      console.log("🔧 Possible reasons:");
      console.log("  - Events are being queued for batch sending");
      console.log("  - Network requests are being blocked at a deeper level");
      console.log("  - Analytics initialization is still in progress");
    }

    console.log("\n🎯 Next Steps:");
    console.log("1. Check PostHog dashboard: https://us.posthog.com");
    console.log('2. Look for events with "bypass_test_v023" in the name');
    console.log("3. Events should appear within 1-2 minutes");
    console.log("4. If events appear, the bypass system is working correctly!");

    console.log("\n📊 Test completed at:", new Date().toISOString());
  }, 15000); // Monitor for 15 seconds

  console.log("⏱️ Monitoring network requests for 15 seconds...");
}, 2000); // Wait 2 seconds for page initialization

// Test 5: Check application health
console.log("\n=== Test 5: Application Health Check ===");

fetch("/api/health")
  .then((response) => response.json())
  .then((health) => {
    console.log("🏥 Application health status:", {
      status: health.status,
      analytics: health.services?.analytics?.status,
      posthog: health.services?.analytics?.posthog,
      version: health.version,
      environment: health.environment,
    });

    if (health.services?.analytics?.status === "up") {
      console.log("✅ Analytics service is reported as UP in health check");
    } else {
      console.log(
        "⚠️ Analytics service status:",
        health.services?.analytics?.status
      );
      console.log("🔧 This might be normal during initialization");
    }
  })
  .catch((error) => {
    console.warn("⚠️ Could not fetch health status:", error.message);
  });

console.log("\n📝 Instructions:");
console.log("1. Wait for all tests to complete (about 20 seconds)");
console.log(
  '2. Look for "PostHog request" messages indicating network activity'
);
console.log("3. Check your PostHog dashboard for test events");
console.log('4. Events with "bypass_test_v023" confirm the system is working');

console.log("\n⏱️ Full test will complete in ~20 seconds...");
