---
title: "Sentry Setup Guide"
description: "Configure Sentry error monitoring for the J&A Business Solutions Next.js application"
---

# Sentry Setup Guide for J&A Business Solutions

This guide will help you configure Sentry error monitoring for the J&A Business Solutions Next.js application.

## Project Information

<CardGroup cols={3}>
  <Card title="Organization" icon="building">
    atemkeng
  </Card>
  <Card title="Project" icon="folder">
    proptech
  </Card>
  <Card title="Platform" icon="react">
    Next.js
  </Card>
</CardGroup>

## Environment Variables Setup

Add the following environment variables to your `.env.local` file:

```bash
# Sentry Configuration
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/your-project-id
NEXT_PUBLIC_SENTRY_ENVIRONMENT=development

# Sentry build configuration (for source maps upload)
SENTRY_ORG=atemkeng
SENTRY_PROJECT=proptech
SENTRY_AUTH_TOKEN=your_sentry_auth_token_here
```

## Getting Your Sentry DSN

<Steps>
  <Step title="Access Dashboard">
    Go to your Sentry dashboard: https://atemkeng.sentry.io/projects/proptech/
  </Step>

  <Step title="Navigate to Settings">
    Navigate to **Settings** → **Client Keys (DSN)**
  </Step>

  <Step title="Copy DSN">
    Copy the **DSN** value
  </Step>

  <Step title="Update Environment">
    Replace `your-key` and `your-project-id` in the DSN above
  </Step>
</Steps>

## Getting Your Auth Token (for source maps)

<Steps>
  <Step title="Access Auth Tokens">
    Go to https://atemkeng.sentry.io/settings/auth-tokens/
  </Step>

  <Step title="Create Token">
    Click **Create New Token**
  </Step>

  <Step title="Configure Token">
    Give it a name like "proptech-sourcemaps" and select the following scopes:
    - `project:read`
    - `project:releases`
    - `org:read`
  </Step>

  <Step title="Save Token">
    Copy the token and add it to your environment variables
  </Step>
</Steps>

## Testing the Integration

<Steps>
  <Step title="Start Development Server">
    ```bash
    npm run dev
    ```
  </Step>

  <Step title="Visit Test Page">
    Visit the test page: http://localhost:3000/sentry-example-page
  </Step>

  <Step title="Trigger Test Errors">
    Click any of the error buttons to trigger test errors
  </Step>

  <Step title="Check Dashboard">
    Check your Sentry dashboard to see the captured errors
  </Step>
</Steps>

## Verification Steps

### 1. Check Environment Variables

<Tip>
Make sure your `.env.local` file contains the correct values:
- `NEXT_PUBLIC_SENTRY_DSN` should start with `https://`
- `NEXT_PUBLIC_SENTRY_ENVIRONMENT` should be set to your current environment
- `SENTRY_ORG` should be `atemkeng`
- `SENTRY_PROJECT` should be `proptech`
</Tip>

### 2. Test Error Capture

<Steps>
  <Step title="Navigate to Test Page">
    Go to `/sentry-example-page`
  </Step>

  <Step title="Trigger Error">
    Click "Trigger Test Error"
  </Step>

  <Step title="Check Sentry">
    Check your Sentry Issues page: https://atemkeng.sentry.io/issues/
  </Step>
</Steps>

### 3. Test Custom Messages

<Steps>
  <Step title="Send Custom Message">
    On the test page, click "Send Custom Message"
  </Step>

  <Step title="Verify in Sentry">
    Check Sentry for the custom message
  </Step>
</Steps>

### 4. Test User Context

<Steps>
  <Step title="Set User Context">
    Click "Set User Context" then trigger an error
  </Step>

  <Step title="Verify Context">
    The error should include user information in Sentry
  </Step>
</Steps>

## Production Configuration

For production deployment, update your environment variables:

```bash
NEXT_PUBLIC_SENTRY_ENVIRONMENT=production
```

<Warning>
Ensure your hosting platform (Vercel, etc.) has the environment variables configured.
</Warning>

## Features Enabled

<CardGroup cols={2}>
  <Card title="Error Monitoring" icon="bug">
    - Automatic error capture for unhandled exceptions
    - Custom error boundaries with Sentry integration
    - Server-side error monitoring
    - Edge runtime error monitoring
  </Card>

  <Card title="Performance Monitoring" icon="gauge">
    - Page load performance tracking
    - API route performance monitoring
    - Core Web Vitals tracking
    - Custom performance metrics
  </Card>

  <Card title="Session Replay" icon="video">
    - User session recordings for debugging
    - Privacy-focused configuration (sensitive data masked)
    - Error replay for better debugging context
  </Card>

  <Card title="Release Tracking" icon="rocket">
    - Source map upload for better stack traces
    - Release-based error tracking
    - Deployment monitoring
  </Card>
</CardGroup>

## Troubleshooting

<AccordionGroup>
  <Accordion title="No Events in Sentry">
    1. Check that `NEXT_PUBLIC_SENTRY_DSN` is correctly set
    2. Verify the DSN format is correct
    3. Check browser console for Sentry initialization errors
    4. Try the test page at `/sentry-example-page`
  </Accordion>

  <Accordion title="Source Maps Not Working">
    1. Verify `SENTRY_AUTH_TOKEN` is set correctly
    2. Check that `SENTRY_ORG` and `SENTRY_PROJECT` match your Sentry project
    3. Ensure the auth token has the correct permissions
  </Accordion>

  <Accordion title="Performance Issues">
    1. Adjust sampling rates in the Sentry configuration files
    2. Review the `beforeSend` filters to reduce noise
    3. Consider disabling session replay in production if needed
  </Accordion>
</AccordionGroup>

## Configuration Files

The following files configure Sentry for different environments:

- `sentry.client.config.ts` - Browser/client-side configuration
- `sentry.server.config.ts` - Server-side configuration  
- `sentry.edge.config.ts` - Edge runtime configuration
- `src/app/global-error.tsx` - Global error boundary
- `next.config.js` - Build-time Sentry integration

## Support

If you encounter issues:

1. Check the [Sentry Next.js documentation](https://docs.sentry.io/platforms/javascript/guides/nextjs/)
2. Review the Sentry dashboard for configuration issues
3. Check the browser console and server logs for error messages
4. Use the test page to verify basic functionality

## Security Notes

<Warning>
- Never commit `.env.local` to version control
- Keep your auth token secure and rotate it regularly
- Review the data being sent to Sentry to ensure no sensitive information is included
- Use the privacy controls in session replay to mask sensitive data
</Warning>