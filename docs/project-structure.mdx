---
title: "Project Structure"
description: "Application architecture and file organization for the J&A Business Solutions Next.js application"
---

# Project Structure

## Root Directory

```
├── src/                    # Source code
├── public/                 # Static assets and PWA files
├── .kiro/                  # Kiro AI assistant configuration
├── node_modules/           # Dependencies
└── dist/                   # Build output (generated)
```

<CardGroup cols={2}>
  <Card title="Source Code" icon="folder">
    `src/` - All application source code and components
  </Card>
  <Card title="Static Assets" icon="image">
    `public/` - Static assets, PWA files, and public resources
  </Card>
  <Card title="AI Configuration" icon="robot">
    `.kiro/` - Kiro AI assistant configuration and specifications
  </Card>
  <Card title="Build Output" icon="package">
    `dist/` - Generated build output (not committed to version control)
  </Card>
</CardGroup>

## Source Code Organization (`src/`)

```
src/
├── components/             # React components
│   ├── ui/                # Reusable UI components
│   └── *.tsx              # Page/section components
├── App.tsx                # Main application component
├── main.tsx               # Application entry point
├── index.css              # Global styles
├── ScrollContext.tsx      # Shared scroll state context
└── vite-env.d.ts         # Vite type definitions
```

<Tabs>
  <Tab title="Components">
    **React Components**
    - `components/ui/` - Reusable shadcn/ui components
    - `components/*.tsx` - Page and section-specific components
    - All components use TypeScript and functional component patterns
  </Tab>

  <Tab title="Application Core">
    **Core Application Files**
    - `App.tsx` - Main application component with routing
    - `main.tsx` - Application entry point and providers
    - `index.css` - Global styles and Tailwind CSS imports
  </Tab>

  <Tab title="Context & State">
    **State Management**
    - `ScrollContext.tsx` - Shared scroll state for navigation
    - Context-based state management for cross-component communication
  </Tab>

  <Tab title="Type Definitions">
    **TypeScript Configuration**
    - `vite-env.d.ts` - Vite-specific type definitions
    - Component-level type definitions within each file
  </Tab>
</Tabs>

## Component Architecture

### Design Patterns

<CardGroup cols={2}>
  <Card title="Single-Page Application" icon="window">
    Single-page application with section-based components for smooth navigation
  </Card>
  <Card title="Context-Based State" icon="share">
    Context-based state management for scroll behavior and shared state
  </Card>
  <Card title="Functional Components" icon="function">
    Functional components with React hooks for all component logic
  </Card>
  <Card title="TypeScript First" icon="code">
    TypeScript for all component files with proper type definitions
  </Card>
</CardGroup>

### Component Hierarchy

```mermaid
graph TD
    A[App.tsx] --> B[Header.tsx]
    A --> C[Hero.tsx]
    A --> D[Services.tsx]
    A --> E[PropertyOwners.tsx]
    A --> F[Contact.tsx]
    A --> G[Footer.tsx]
    
    B --> H[Navigation Components]
    C --> I[CTA Components]
    F --> J[Form Components]
    
    H --> K[ui/Button]
    I --> K
    J --> K
    J --> L[ui/Input]
    J --> M[ui/Textarea]
```

## Key Components

<AccordionGroup>
  <Accordion title="Header.tsx">
    **Navigation Component**
    - Navigation with scroll-responsive styling
    - Mobile-responsive hamburger menu
    - Smooth scrolling to page sections
    - Brand logo and contact information
  </Accordion>

  <Accordion title="Hero.tsx">
    **Landing Section**
    - Primary hero section with value proposition
    - Call-to-action buttons for key user journeys
    - Responsive design for mobile and desktop
    - Background imagery and branding elements
  </Accordion>

  <Accordion title="Services.tsx">
    **Service Offerings Display**
    - Grid layout of service offerings
    - Service descriptions and benefits
    - Visual icons and imagery
    - Links to detailed service information
  </Accordion>

  <Accordion title="PropertyOwners.tsx">
    **Target Audience Section**
    - Information for property owners
    - Partnership opportunities
    - Benefits of working with J&A Business Solutions
    - Contact forms for property owner inquiries
  </Accordion>

  <Accordion title="Contact.tsx">
    **Contact Information and Forms**
    - Contact form with validation
    - Business contact information
    - Location and hours information
    - Integration with analytics tracking
  </Accordion>

  <Accordion title="PWAInstallPrompt.tsx">
    **Progressive Web App Installation**
    - PWA installation prompt component
    - Browser compatibility detection
    - User-friendly installation guidance
    - Installation state management
  </Accordion>
</AccordionGroup>

## Static Assets (`public/`)

```
public/
├── icons/                 # App icons and favicons
├── screenshots/           # App screenshots for PWA
├── manifest.json          # PWA manifest
├── service-worker.js      # Service worker for offline functionality
└── offline.html           # Offline fallback page
```

<Tabs>
  <Tab title="PWA Assets">
    **Progressive Web App Files**
    - `manifest.json` - PWA manifest with app metadata
    - `service-worker.js` - Service worker for offline functionality
    - `offline.html` - Offline fallback page
    - App icons in various sizes for different devices
  </Tab>

  <Tab title="Icons & Images">
    **Visual Assets**
    - `icons/` - App icons, favicons, and platform-specific icons
    - `screenshots/` - App screenshots for PWA store listings
    - Optimized images for fast loading
    - Multiple sizes for responsive design
  </Tab>

  <Tab title="Static Files">
    **Public Resources**
    - Robots.txt for SEO
    - Sitemap.xml for search engines
    - Static assets that don't require processing
  </Tab>
</Tabs>

## Styling Conventions

### CSS Architecture

<CardGroup cols={2}>
  <Card title="Tailwind CSS" icon="paintbrush">
    Utility-first CSS framework for rapid development
  </Card>
  <Card title="Custom Color Palette" icon="palette">
    Navy and gold theme representing professionalism
  </Card>
  <Card title="Responsive Design" icon="mobile">
    Mobile-first approach with responsive breakpoints
  </Card>
  <Card title="Component-Scoped" icon="component">
    Component-scoped styling using Tailwind classes
  </Card>
</CardGroup>

### Design System

<Tabs>
  <Tab title="Colors">
    **Brand Colors:**
    - Navy Blue: `#1e3a8a` (primary)
    - Gold: `#f59e0b` (accent)
    - White: `#ffffff` (background)
    - Gray: `#6b7280` (text)
  </Tab>

  <Tab title="Typography">
    **Font Hierarchy:**
    - Headings: Bold, larger sizes for hierarchy
    - Body text: Regular weight, readable sizes
    - Captions: Smaller, lighter weight
    - Consistent line heights and spacing
  </Tab>

  <Tab title="Spacing">
    **Spacing System:**
    - Consistent spacing scale using Tailwind utilities
    - Responsive spacing adjustments
    - Proper component padding and margins
    - Grid and flexbox layouts
  </Tab>

  <Tab title="Components">
    **UI Components:**
    - shadcn/ui components for consistency
    - Custom styling with Tailwind utilities
    - Accessible design patterns
    - Interactive states and animations
  </Tab>
</Tabs>

## File Naming Conventions

<CardGroup cols={3}>
  <Card title="React Components" icon="react">
    **PascalCase** for React components (`Header.tsx`)
  </Card>
  <Card title="Utility Files" icon="wrench">
    **camelCase** for utility files (`vite.config.ts`)
  </Card>
  <Card title="Static Assets" icon="file">
    **kebab-case** for static assets and configuration files
  </Card>
</CardGroup>

### Examples

<CodeGroup>
```typescript React Components
Header.tsx
Hero.tsx
Services.tsx
PropertyOwners.tsx
Contact.tsx
PWAInstallPrompt.tsx
```

```typescript Utility Files
vite.config.ts
tailwind.config.ts
eslint.config.js
```

```bash Static Assets
manifest.json
service-worker.js
offline.html
favicon.ico
```
</CodeGroup>

## Configuration Files

### Build Configuration

<AccordionGroup>
  <Accordion title="Vite Configuration">
    - `vite.config.ts` - Vite build configuration
    - Plugin configuration for PWA, TypeScript, React
    - Development server settings
    - Build optimization settings
  </Accordion>

  <Accordion title="TypeScript Configuration">
    - `tsconfig.json` - TypeScript compiler configuration
    - Strict type checking enabled
    - Path mapping for clean imports
    - Target ES2020 for modern browser support
  </Accordion>

  <Accordion title="Tailwind Configuration">
    - `tailwind.config.ts` - Tailwind CSS configuration
    - Custom color palette definition
    - Component and utility configurations
    - Responsive breakpoint definitions
  </Accordion>
</AccordionGroup>

### Development Tools

<Tabs>
  <Tab title="Code Quality">
    - `eslint.config.js` - ESLint configuration for code quality
    - `prettier.config.js` - Prettier configuration for code formatting
    - `.gitignore` - Git ignore patterns
  </Tab>

  <Tab title="Package Management">
    - `package.json` - Project dependencies and scripts
    - `package-lock.json` - Locked dependency versions
    - Node.js and npm version requirements
  </Tab>

  <Tab title="Environment">
    - `.env.example` - Environment variable template
    - `.env.local` - Local development environment variables
    - Environment-specific configurations
  </Tab>
</Tabs>

This project structure provides a solid foundation for scalable development while maintaining clear separation of concerns and following Next.js and React best practices.