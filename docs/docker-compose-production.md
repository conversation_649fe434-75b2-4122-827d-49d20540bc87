# Production Docker Compose Configuration

This document explains the production Docker Compose configuration for the J&A Business Solutions application.

## Overview

The production Docker Compose setup includes:
- Main web application container
- Optional monitoring container for system metrics
- Production-optimized networking and security
- Comprehensive logging and health checks
- Resource limits and restart policies

## Services

### Web Application Service

The main application service runs the Next.js application in a production-optimized container.

#### Configuration Details

- **Image**: `atemndobs/jna-amd64:v0.9`
- **Container Name**: `jna-business-solutions-prod`
- **Port Mapping**: `8642:3000` (external:internal)
- **Restart Policy**: `unless-stopped`

#### Environment Variables

The service loads environment variables from:
1. Inline environment variables (NODE_ENV, PORT, HOSTNAME)
2. `.env.production` file for sensitive and configuration data

#### Health Checks

```yaml
healthcheck:
  test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 40s
```

- **Test Command**: Uses wget to check the `/api/health` endpoint
- **Interval**: Checks every 30 seconds
- **Timeout**: 10 seconds per check
- **Retries**: 3 attempts before marking as unhealthy
- **Start Period**: 40 seconds grace period for application startup

#### Volume Mounts

1. **Public Assets**: `./public:/app/public:ro` (read-only)
   - Static assets and PWA files
   - Read-only to prevent container modifications

2. **Logs**: `./logs:/app/logs`
   - Application log files
   - Persistent storage for debugging and monitoring

3. **Environment File**: `./.env.production:/app/.env.production:ro` (read-only)
   - Production environment variables
   - Read-only for security

#### Resource Limits

```yaml
deploy:
  resources:
    limits:
      cpus: '1.0'
      memory: 1G
    reservations:
      cpus: '0.5'
      memory: 512M
```

- **CPU Limit**: 1.0 CPU core maximum
- **Memory Limit**: 1GB maximum
- **CPU Reservation**: 0.5 CPU core guaranteed
- **Memory Reservation**: 512MB guaranteed

#### Security Configuration

- **Security Options**: `no-new-privileges:true` prevents privilege escalation
- **User**: Runs as user `1000:1000` (non-root)
- **Read-only Mounts**: Critical files mounted as read-only

#### Logging Configuration

```yaml
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
    labels: "service=jna-business-solutions"
```

- **Driver**: JSON file logging
- **Max Size**: 10MB per log file
- **Max Files**: Keep 3 log files (30MB total)
- **Labels**: Service identification for log aggregation

### Monitoring Service (Optional)

The monitoring service provides system metrics using Prometheus Node Exporter.

#### Configuration Details

- **Image**: `prom/node-exporter:latest`
- **Container Name**: `jna-monitoring`
- **Port Mapping**: `9100:9100`
- **Purpose**: System metrics collection

#### Metrics Collection

The monitoring service collects:
- CPU usage and load averages
- Memory usage and statistics
- Disk I/O and filesystem metrics
- Network interface statistics
- System uptime and boot time

#### Resource Limits

```yaml
deploy:
  resources:
    limits:
      cpus: '0.2'
      memory: 128M
    reservations:
      cpus: '0.1'
      memory: 64M
```

- Lightweight resource allocation
- Minimal impact on main application performance

## Network Configuration

### Production Network

```yaml
networks:
  jna-production-network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: jna-prod-br0
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
```

- **Network Name**: `jna-production-network`
- **Driver**: Bridge network for container communication
- **Subnet**: `**********/16` (65,534 available addresses)
- **Gateway**: `**********`
- **Bridge Name**: `jna-prod-br0` for system identification

## Volume Configuration

### Persistent Volumes

1. **Logs Volume**: `jna-logs`
   - Local bind mount to `./logs`
   - Persistent application logs
   - Survives container restarts

2. **Public Volume**: `jna-public`
   - Local bind mount to `./public`
   - Static assets and PWA files
   - Shared between host and container

## Deployment Commands

### Start Services

```bash
# Start all services in detached mode
docker-compose -f docker-compose.prod.yml up -d

# Start only the web service
docker-compose -f docker-compose.prod.yml up -d web

# Start with build (if needed)
docker-compose -f docker-compose.prod.yml up -d --build
```

### Stop Services

```bash
# Stop all services
docker-compose -f docker-compose.prod.yml down

# Stop and remove volumes
docker-compose -f docker-compose.prod.yml down -v

# Stop and remove images
docker-compose -f docker-compose.prod.yml down --rmi all
```

### View Logs

```bash
# View all service logs
docker-compose -f docker-compose.prod.yml logs

# View web service logs
docker-compose -f docker-compose.prod.yml logs web

# Follow logs in real-time
docker-compose -f docker-compose.prod.yml logs -f web

# View last 100 lines
docker-compose -f docker-compose.prod.yml logs --tail=100 web
```

### Health Check Status

```bash
# Check service status
docker-compose -f docker-compose.prod.yml ps

# Check health status
docker inspect jna-business-solutions-prod --format='{{.State.Health.Status}}'

# View health check logs
docker inspect jna-business-solutions-prod --format='{{range .State.Health.Log}}{{.Output}}{{end}}'
```

## Monitoring and Maintenance

### Health Monitoring

1. **Application Health**: Monitor the `/api/health` endpoint
2. **Container Health**: Use Docker health check status
3. **System Metrics**: Access Node Exporter metrics at `http://localhost:9100/metrics`
4. **Log Monitoring**: Monitor application logs in `./logs` directory

### Maintenance Tasks

1. **Log Rotation**: Automatic log rotation configured (10MB max, 3 files)
2. **Image Updates**: Regularly update to newer image versions
3. **Security Updates**: Monitor base image security updates
4. **Resource Monitoring**: Monitor CPU and memory usage
5. **Disk Space**: Monitor log directory disk usage

### Backup Procedures

1. **Environment Files**: Backup `.env.production` securely
2. **Application Logs**: Regular backup of `./logs` directory
3. **Configuration Files**: Backup `docker-compose.prod.yml`
4. **Database**: Backup Supabase database separately

## Troubleshooting

### Common Issues

1. **Container Won't Start**
   ```bash
   # Check container logs
   docker-compose -f docker-compose.prod.yml logs web
   
   # Check environment variables
   docker-compose -f docker-compose.prod.yml config
   ```

2. **Health Check Failures**
   ```bash
   # Test health endpoint manually
   curl http://localhost:8642/api/health
   
   # Check health check logs
   docker inspect jna-business-solutions-prod --format='{{range .State.Health.Log}}{{.Output}}{{end}}'
   ```

3. **Port Conflicts**
   ```bash
   # Check port usage
   netstat -tulpn | grep :8642
   
   # Change port in docker-compose.prod.yml if needed
   ```

4. **Resource Issues**
   ```bash
   # Check resource usage
   docker stats jna-business-solutions-prod
   
   # Adjust resource limits if needed
   ```

5. **Network Issues**
   ```bash
   # Check network configuration
   docker network ls
   docker network inspect jna-production-network
   ```

### Performance Optimization

1. **Resource Tuning**: Adjust CPU and memory limits based on usage
2. **Log Management**: Implement log aggregation for large deployments
3. **Caching**: Configure reverse proxy caching if needed
4. **Load Balancing**: Add load balancer for high availability

## Security Considerations

1. **Non-root User**: Container runs as non-root user (1000:1000)
2. **Read-only Mounts**: Critical files mounted as read-only
3. **No New Privileges**: Prevents privilege escalation attacks
4. **Network Isolation**: Containers run in isolated network
5. **Resource Limits**: Prevents resource exhaustion attacks
6. **Log Security**: Logs don't contain sensitive information

## Production Checklist

Before deploying to production:

- [ ] Environment variables configured in `.env.production`
- [ ] Docker image built and pushed to registry
- [ ] Health check endpoint responding correctly
- [ ] Resource limits appropriate for server capacity
- [ ] Logging configuration tested
- [ ] Network configuration verified
- [ ] Security settings applied
- [ ] Monitoring service configured (if using)
- [ ] Backup procedures in place
- [ ] Rollback plan documented