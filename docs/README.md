# J&A Business Solutions Documentation

This directory contains all project documentation in both Markdown and MDX formats for use with Mintlify documentation platform.

## Documentation Structure

### Setup and Configuration
- [Setup Guide](./setup.mdx) - Complete development environment setup
- [Sentry Setup](./sentry-setup.mdx) - Sentry error monitoring configuration
- [Integration Status](./integration-status.mdx) - Current integration status report

### Development Guides
- [Universal Error Wrapper Guide](./universal-error-wrapper-guide.mdx) - Comprehensive error handling system
- [Development Workflow](./development-workflow.mdx) - Development practices and standards
- [Tech Stack](./tech-stack.mdx) - Technology stack overview and configuration

### Architecture and Design
- [Project Structure](./project-structure.mdx) - Application architecture and organization
- [Product Overview](./product-overview.mdx) - Business context and features
- [Sentry AI Rules](./sentry-ai-rules.mdx) - AI-assisted error monitoring patterns

### Feature Specifications
- [Comprehensive Sentry Integration](./specs/comprehensive-sentry-integration/) - Complete Sentry integration spec
- [Tech Stack Integration](./specs/tech-stack-integration/) - Next.js migration and tech stack integration

## Using with Mintlify

All `.mdx` files in this directory are formatted for use with Mintlify documentation platform. The MDX format allows for enhanced documentation with interactive components and better formatting.

## Contributing

When adding new documentation:
1. Create both `.md` and `.mdx` versions
2. Follow the existing structure and formatting
3. Update this README with new documentation links
4. Ensure all code examples use proper syntax highlighting