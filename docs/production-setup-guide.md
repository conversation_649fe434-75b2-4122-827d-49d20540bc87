# Production Setup Guide

This guide walks you through setting up the J&A Business Solutions application for production deployment.

## Prerequisites

- Docker and Docker Compose installed
- Access to Supabase, Sentry, and PostHog production projects
- Production server with sufficient resources (1GB RAM, 1 CPU core minimum)

## Step 1: Supabase Configuration ✅

The Supabase configuration has been automatically configured using the MCP:

- **Project**: proptech
- **Project ID**: bdzvmmnbtwsfvrqepjuu
- **Region**: us-east-2
- **Status**: ACTIVE_HEALTHY
- **URL**: https://bdzvmmnbtwsfvrqepjuu.supabase.co

### Database Schema

The database includes the following tables:
- `properties` - Property listings with Airbnb integration
- `bookings` - Booking management with calendar sync
- `availability_calendar` - Property availability tracking
- `social_posts` - Social media content management
- `blog_articles` - Blog content linked to social posts
- `contact_submissions` - Contact form submissions

### TypeScript Types

Database types have been generated and are available in `src/types/database.ts`.

## Step 2: Sentry Configuration ✅

Sentry is configured for production error monitoring:

- **Organization**: atemkeng
- **Project**: proptech
- **DSN**: Already configured in `.env.production`

### Features Enabled:
- Error tracking and reporting
- Performance monitoring (10% sample rate)
- Source map uploads for debugging
- Custom error boundaries and reporting

## Step 3: PostHog Configuration ⚠️

PostHog configuration needs to be completed:

1. Log into your PostHog dashboard
2. Navigate to your production project
3. Go to Project Settings
4. Copy the Project API Key
5. Update `.env.production`:

```bash
NEXT_PUBLIC_POSTHOG_API_KEY=phc_your_actual_production_key
```

## Step 4: Environment Variables

Review and update the following variables in `.env.production`:

### Required Updates:
- [ ] `NEXT_PUBLIC_POSTHOG_API_KEY` - Replace with actual PostHog production key
- [ ] `SENTRY_AUTH_TOKEN` - Add your Sentry authentication token for source maps
- [ ] `CORS_ORIGIN` - Update with your production domain
- [ ] `TRUSTED_HOSTS` - Update with your production domain

### Optional Updates:
- [ ] `CSP_REPORT_URI` - Configure Content Security Policy reporting
- [ ] `ALERT_WEBHOOK_URL` - Configure alerting webhook
- [ ] Performance and logging settings

## Step 5: Docker Configuration ✅

The Docker configuration is ready for production:

- **Image**: `atemndobs/jna-amd64:v0.9`
- **Port**: 8642 (external) → 3000 (internal)
- **Health Checks**: Configured with `/api/health` endpoint
- **Resource Limits**: 1 CPU core, 1GB RAM
- **Security**: Non-root user, no new privileges
- **Logging**: JSON format with rotation

## Step 6: Validation

Run the validation script to check your configuration:

```bash
./scripts/validate-production-config.sh
```

### Expected Results:
- ✅ All required files exist
- ✅ Supabase configuration is valid
- ✅ Sentry configuration is valid
- ⚠️ PostHog configuration needs actual API key
- ✅ Docker configuration is valid
- ✅ Port 8642 is available

## Step 7: Deployment

### Option 1: Docker Compose (Recommended)

```bash
# Start the production services
docker-compose -f docker-compose.prod.yml up -d

# Check service status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f web
```

### Option 2: Manual Docker Run

```bash
# Pull the latest image
docker pull atemndobs/jna-amd64:v0.9

# Run the container
docker run -d \
  --name jna-business-solutions-prod \
  --restart unless-stopped \
  -p 8642:3000 \
  --env-file .env.production \
  -v ./public:/app/public:ro \
  -v ./logs:/app/logs \
  atemndobs/jna-amd64:v0.9
```

## Step 8: Health Checks

After deployment, verify the application is running:

```bash
# Check application health
curl http://localhost:8642/api/health

# Expected response:
# {"status":"ok","timestamp":"2025-01-XX...","environment":"production"}

# Check container health
docker inspect jna-business-solutions-prod --format='{{.State.Health.Status}}'

# Expected response: healthy
```

## Step 9: Monitoring Setup

### Application Monitoring
- **Sentry**: Error tracking and performance monitoring
- **PostHog**: User analytics and feature flags
- **Docker Health Checks**: Container health monitoring
- **Application Logs**: JSON formatted logs in `./logs/`

### System Monitoring (Optional)
The Docker Compose configuration includes a Node Exporter container for system metrics:

```bash
# Access system metrics
curl http://localhost:9100/metrics
```

## Step 10: SSL/TLS and Reverse Proxy

For production, you'll need to set up SSL/TLS and a reverse proxy:

### Nginx Configuration Example:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    location / {
        proxy_pass http://localhost:8642;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Troubleshooting

### Common Issues:

1. **Container won't start**
   - Check environment variables: `docker-compose -f docker-compose.prod.yml config`
   - Check logs: `docker-compose -f docker-compose.prod.yml logs web`

2. **Health check failures**
   - Test endpoint manually: `curl http://localhost:8642/api/health`
   - Check application logs in `./logs/`

3. **Database connection issues**
   - Verify Supabase URL and API key
   - Check network connectivity to Supabase

4. **Sentry not receiving errors**
   - Verify Sentry DSN and auth token
   - Check Sentry project settings

5. **PostHog not tracking events**
   - Verify PostHog API key
   - Check browser console for errors

### Log Locations:
- **Application Logs**: `./logs/`
- **Docker Logs**: `docker-compose -f docker-compose.prod.yml logs`
- **System Logs**: `/var/log/` (on host system)

## Security Checklist

- [ ] Environment variables secured and not committed to version control
- [ ] SSL/TLS certificate configured
- [ ] Firewall rules configured (only necessary ports open)
- [ ] Regular security updates scheduled
- [ ] Database access restricted to application only
- [ ] API keys rotated regularly
- [ ] Content Security Policy configured
- [ ] CORS properly configured for production domain

## Backup Strategy

1. **Database**: Supabase handles automatic backups
2. **Environment Files**: Secure backup of `.env.production`
3. **Application Logs**: Regular backup of `./logs/` directory
4. **Configuration Files**: Backup of Docker Compose and configuration files

## Maintenance

### Regular Tasks:
- Monitor application health and performance
- Review error logs and Sentry reports
- Update Docker images when new versions are available
- Rotate API keys and certificates
- Monitor disk space and log file sizes
- Review and update security configurations

### Update Procedure:
1. Pull new Docker image
2. Test in staging environment
3. Create backup of current configuration
4. Update production deployment
5. Verify health checks pass
6. Monitor for any issues

## Support

For deployment issues:
1. Check the troubleshooting section above
2. Review application and Docker logs
3. Verify all environment variables are correctly set
4. Test individual service connections (database, Sentry, PostHog)
5. Contact the development team with specific error details and logs