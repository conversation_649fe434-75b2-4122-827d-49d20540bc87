---
title: "Development Setup Guide"
description: "Complete guide for setting up the J&A Business Solutions development environment"
---

# J&A Business Solutions - Development Setup Guide

This guide will help you set up the development environment for the J&A Business Solutions website with all required integrations.

## Prerequisites

- Node.js 18+ installed
- npm or yarn package manager
- Git for version control

## Quick Start

<Steps>
  <Step title="Clone and Install Dependencies">
    ```bash
    git clone <repository-url>
    cd jna_bs-v2
    npm install
    ```
  </Step>

  <Step title="Environment Configuration">
    ```bash
    cp .env.example .env.local
    # Edit .env.local with your actual values (see sections below)
    ```
  </Step>

  <Step title="Validate Environment">
    ```bash
    node scripts/validate-env.js
    ```
  </Step>

  <Step title="Install Playwright Browsers">
    ```bash
    npx playwright install
    ```
  </Step>

  <Step title="Start Development Server">
    ```bash
    npm run dev
    ```
  </Step>
</Steps>

## Required Services Setup

### 1. Supabase (Database)

<Steps>
  <Step title="Create Project">
    Go to [supabase.com](https://supabase.com) and create a new project
  </Step>

  <Step title="Get Credentials">
    Get your project URL and anon key from Settings > API
  </Step>

  <Step title="Configure Environment">
    Add to `.env.local`:
    ```bash
    VITE_SUPABASE_URL=https://your-project-id.supabase.co
    VITE_SUPABASE_ANON_KEY=your-anon-key
    ```
  </Step>
</Steps>

### 2. Sentry (Error Monitoring)

<Steps>
  <Step title="Create Project">
    Go to [sentry.io](https://sentry.io) and create a new project
  </Step>

  <Step title="Choose Platform">
    Choose "React" as the platform
  </Step>

  <Step title="Get DSN">
    Get your DSN from the project settings
  </Step>

  <Step title="Configure Environment">
    Add to `.env.local`:
    ```bash
    VITE_SENTRY_DSN=https://<EMAIL>/your-project-id
    VITE_SENTRY_ENVIRONMENT=development
    ```
  </Step>
</Steps>

### 3. PostHog (Analytics)

<Steps>
  <Step title="Create Project">
    Go to [posthog.com](https://posthog.com) and create a new project
  </Step>

  <Step title="Get API Key">
    Get your API key from Project Settings
  </Step>

  <Step title="Configure Environment">
    Add to `.env.local`:
    ```bash
    VITE_POSTHOG_API_KEY=phc_your-api-key
    VITE_POSTHOG_API_HOST=https://app.posthog.com
    ```
  </Step>
</Steps>

## Development Commands

### Basic Commands
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Testing Commands
```bash
npm run test         # Run unit tests
npm run test:e2e     # Run Playwright E2E tests
npm run test:e2e:ui  # Run E2E tests with UI
npm run test:e2e:headed # Run E2E tests in headed mode
```

### Utility Commands
```bash
node scripts/validate-env.js  # Validate environment configuration
npm run env:validate          # Alternative environment validation
npm run db:check             # Check database connection
```

## MCP (Model Context Protocol) Integration

This project uses MCP servers for enhanced development experience:

<CardGroup cols={2}>
  <Card title="Supabase MCP" icon="database">
    Database operations and schema management
  </Card>
  <Card title="Sentry MCP" icon="bug">
    Error monitoring and issue tracking
  </Card>
  <Card title="PostHog MCP" icon="chart-line">
    Analytics and feature flag management
  </Card>
  <Card title="Playwright MCP" icon="browser">
    Automated testing and browser automation
  </Card>
</CardGroup>

MCP servers are configured in `.kiro/settings/mcp.json` and provide enhanced AI assistance.

## Project Structure

```
src/
├── lib/                    # Core utilities and configurations
│   ├── env.ts             # Environment configuration and validation
│   ├── supabase.ts        # Supabase client and database utilities
│   ├── sentry.ts          # Sentry error monitoring setup
│   └── posthog.ts         # PostHog analytics configuration
├── types/                 # TypeScript type definitions
│   └── env.ts            # Environment variable types
└── components/           # React components
```

## Environment Variables Reference

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `VITE_SUPABASE_URL` | Yes | Supabase project URL | `https://abc123.supabase.co` |
| `VITE_SUPABASE_ANON_KEY` | Yes | Supabase anonymous key | `eyJ0eXAiOiJKV1Q...` |
| `VITE_SENTRY_DSN` | Yes | Sentry project DSN | `https://<EMAIL>/id` |
| `VITE_SENTRY_ENVIRONMENT` | Yes | Environment name | `development` / `production` |
| `VITE_POSTHOG_API_KEY` | Yes | PostHog API key | `phc_abc123...` |
| `VITE_POSTHOG_API_HOST` | No | PostHog API host | `https://app.posthog.com` |

## Troubleshooting

<AccordionGroup>
  <Accordion title="Environment validation fails">
    - Check that `.env.local` exists and has all required variables
    - Ensure no variables contain placeholder values like "your_key_here"
    - Run `node scripts/validate-env.js` for detailed validation
  </Accordion>

  <Accordion title="Database connection fails">
    - Verify Supabase URL and key are correct
    - Check that Supabase project is active
    - Run `npm run db:check` to test connection
  </Accordion>

  <Accordion title="Playwright tests fail">
    - Ensure development server is running (`npm run dev`)
    - Install browsers with `npx playwright install`
    - Check that port 3000 is available
  </Accordion>

  <Accordion title="Build errors">
    - Clear Next.js cache: `rm -rf .next`
    - Reinstall dependencies: `rm -rf node_modules && npm install`
    - Check TypeScript errors: `npx tsc --noEmit`
  </Accordion>
</AccordionGroup>

### Getting Help

- Check the console for detailed error messages
- Validate environment configuration first
- Ensure all required services are properly configured
- Review the setup steps for each service

## Next Steps

After completing the setup:

1. Verify all integrations work by running the development server
2. Run the test suite to ensure everything is working
3. Review the project documentation in the `.kiro/specs/` directory
4. Start implementing features according to the task list

## Production Deployment

For production deployment:

1. Set up production environments for all services
2. Update environment variables for production
3. Configure CI/CD pipeline (GitHub Actions)
4. Set up monitoring and alerting
5. Test deployment in staging environment first