---
title: "Development Workflow Rules"
description: "Development practices, standards, and workflow guidelines for the J&A Business Solutions project"
---

# Development Workflow Rules

## Language Standards

### TypeScript First

<Note>
**TypeScript is the default language** for all new code unless explicitly specified otherwise
</Note>

<CardGroup cols={2}>
  <Card title="File Extensions" icon="file">
    All new files MUST use `.ts` or `.tsx` extensions
  </Card>
  <Card title="Legacy Code" icon="code">
    JavaScript files (`.js`, `.jsx`) are only allowed for legacy code or specific configuration files
  </Card>
  <Card title="Components & Logic" icon="react">
    All components, utilities, and business logic MUST be written in TypeScript
  </Card>
  <Card title="Type Definitions" icon="brackets">
    Proper TypeScript types MUST be defined for all interfaces, props, and function parameters
  </Card>
</CardGroup>

## Branching Strategy

### Feature Branch Workflow

<Steps>
  <Step title="Create Feature Branch">
    **Every new feature MUST be developed in a separate branch**
    - Branch naming convention: `feature/descriptive-name` or `feat/descriptive-name`
    - No direct commits to `main` branch
  </Step>

  <Step title="Pull Request Review">
    All changes must go through Pull Request review process
  </Step>
</Steps>

### Branch Creation Rules

<CodeGroup>
```bash Create Branch
# Create new branch from latest main
git checkout -b feature/feature-name
```

```bash Naming Examples
feature/contact-form
feature/supabase-integration
feature/analytics-setup
```
</CodeGroup>

<Tip>
Branch names should be descriptive and use kebab-case formatting
</Tip>

## Commit and Push Requirements

### After Each Task

<CardGroup cols={2}>
  <Card title="Commit Requirement" icon="git-commit">
    **Code MUST be committed after completing each task**
  </Card>
  <Card title="Push Requirement" icon="upload">
    **Commits MUST be pushed to remote repository immediately**
  </Card>
  <Card title="Commit Messages" icon="message">
    Commit messages should follow conventional commit format
  </Card>
  <Card title="Task Completion" icon="check">
    Each task completion should result in at least one commit
  </Card>
</CardGroup>

### Commit Message Format

```
type(scope): description

Examples:
feat(auth): add user authentication with Supabase
fix(ui): resolve mobile navigation menu issue
docs(readme): update setup instructions
test(e2e): add contact form submission tests
```

<AccordionGroup>
  <Accordion title="Commit Types">
    - `feat`: New feature
    - `fix`: Bug fix
    - `docs`: Documentation changes
    - `style`: Code style changes (formatting, etc.)
    - `refactor`: Code refactoring
    - `test`: Adding or updating tests
    - `chore`: Maintenance tasks
  </Accordion>

  <Accordion title="Scope Examples">
    - `auth`: Authentication related
    - `ui`: User interface changes
    - `api`: API related changes
    - `db`: Database related
    - `config`: Configuration changes
  </Accordion>
</AccordionGroup>

## MCP (Model Context Protocol) Priority

### MCP First Development

<Warning>
**MCPs are first-class citizens and take priority during development**
</Warning>

<Steps>
  <Step title="Check MCP Availability">
    Before setting up any tool or service, MUST first check if an MCP exists
  </Step>

  <Step title="Use MCP Tools">
    Use MCP tools when available instead of manual setup or alternative tools
  </Step>

  <Step title="Configure and Test">
    MCPs should be configured and tested before proceeding with manual alternatives
  </Step>
</Steps>

### Context7 MCP Documentation Rule

<Note>
**ALWAYS use Context7 MCP to search for library documentation before implementation**
</Note>

<CardGroup cols={2}>
  <Card title="Before Implementation" icon="search">
    Before implementing any feature with external libraries, MUST search for latest documentation using Context7 MCP
  </Card>
  <Card title="When Stuck" icon="question">
    When encountering implementation challenges or getting stuck, MUST use Context7 MCP to find relevant documentation
  </Card>
  <Card title="Verify Best Practices" icon="check">
    Use Context7 MCP to verify best practices and current API patterns for libraries
  </Card>
  <Card title="Document Findings" icon="book">
    Document findings from Context7 searches in implementation notes
  </Card>
</CardGroup>

### MCP Integration Workflow

<Tabs>
  <Tab title="Discovery">
    1. Check available MCPs for the required functionality
    2. Use Context7 MCP to search for latest library documentation
    3. Review MCP capabilities and limitations
  </Tab>

  <Tab title="Configuration">
    4. Configure and test MCP integration
    5. Verify MCP functionality works as expected
  </Tab>

  <Tab title="Implementation">
    6. Use MCP tools for development tasks
    7. Document MCP usage and Context7 findings in development notes
  </Tab>

  <Tab title="Fallback">
    8. Only use alternative tools if no suitable MCP exists
  </Tab>
</Tabs>

### MCP Configuration Priority

<CardGroup cols={2}>
  <Card title="Workspace Level" icon="folder">
    Workspace-level MCP configuration takes precedence
  </Card>
  <Card title="User Level" icon="user">
    User-level MCP configuration as fallback
  </Card>
  <Card title="Auto-Approve" icon="check">
    Auto-approve commonly used MCP tools in configuration
  </Card>
  <Card title="Regular Updates" icon="refresh">
    Regularly update MCP server configurations
  </Card>
</CardGroup>

## Code Quality Standards

### TypeScript Configuration

<CardGroup cols={2}>
  <Card title="Strict Mode" icon="shield">
    Strict TypeScript configuration MUST be maintained
  </Card>
  <Card title="No Any Types" icon="ban">
    No `any` types allowed without explicit justification
  </Card>
  <Card title="Proper Types" icon="brackets">
    All props and function parameters MUST have proper types
  </Card>
  <Card title="Utility Types" icon="wrench">
    Use TypeScript utility types when appropriate
  </Card>
</CardGroup>

### Component Standards

<Tabs>
  <Tab title="Component Type">
    All React components MUST be functional components with TypeScript
  </Tab>

  <Tab title="Props Interface">
    Use proper TypeScript interfaces for component props
  </Tab>

  <Tab title="Error Handling">
    Implement proper error boundaries and error handling
  </Tab>

  <Tab title="Best Practices">
    Follow React best practices and hooks rules
  </Tab>
</Tabs>

## Testing Requirements

### Test Coverage

<CardGroup cols={2}>
  <Card title="Feature Tests" icon="test-tube">
    All new features MUST include appropriate tests
  </Card>
  <Card title="Unit Tests" icon="code">
    Unit tests for utility functions and business logic
  </Card>
  <Card title="Component Tests" icon="react">
    Component tests for React components
  </Card>
  <Card title="E2E Tests" icon="browser">
    E2E tests for critical user journeys
  </Card>
</CardGroup>

<Note>
Tests MUST be written in TypeScript
</Note>

### Testing Workflow

<Steps>
  <Step title="Write Tests">
    Write tests alongside feature development
  </Step>

  <Step title="Run Tests">
    Run tests before committing code
  </Step>

  <Step title="CI/CD Validation">
    Ensure all tests pass in CI/CD pipeline
  </Step>

  <Step title="Update Tests">
    Update tests when modifying existing functionality
  </Step>
</Steps>

## Development Best Practices

### Code Organization

<AccordionGroup>
  <Accordion title="File Structure">
    - Follow established project structure
    - Group related files together
    - Use consistent naming conventions
    - Separate concerns appropriately
  </Accordion>

  <Accordion title="Import Organization">
    - Group imports by type (external, internal, relative)
    - Use absolute imports where configured
    - Avoid circular dependencies
    - Keep imports clean and organized
  </Accordion>

  <Accordion title="Component Organization">
    - One component per file
    - Co-locate related files (styles, tests)
    - Use index files for clean imports
    - Follow component naming conventions
  </Accordion>
</AccordionGroup>

### Performance Considerations

<CardGroup cols={2}>
  <Card title="Bundle Size" icon="package">
    Be mindful of bundle size impact
  </Card>
  <Card title="Code Splitting" icon="scissors">
    Use code splitting for large components
  </Card>
  <Card title="Lazy Loading" icon="clock">
    Implement lazy loading where appropriate
  </Card>
  <Card title="Optimization" icon="rocket">
    Follow Next.js optimization best practices
  </Card>
</CardGroup>

### Security Practices

<Warning>
- Never commit sensitive data or credentials
- Use environment variables for configuration
- Validate all user inputs
- Follow security best practices for authentication
- Keep dependencies updated
</Warning>

## Documentation Requirements

### Code Documentation

<Tabs>
  <Tab title="Comments">
    - Add comments for complex logic
    - Document function purposes and parameters
    - Explain business logic and decisions
  </Tab>

  <Tab title="README Updates">
    - Update README files when adding features
    - Document setup and configuration changes
    - Include usage examples
  </Tab>

  <Tab title="API Documentation">
    - Document API endpoints and parameters
    - Include request/response examples
    - Document error handling
  </Tab>
</Tabs>

### Change Documentation

<Steps>
  <Step title="Feature Documentation">
    Document new features and their usage
  </Step>

  <Step title="Breaking Changes">
    Clearly document any breaking changes
  </Step>

  <Step title="Migration Guides">
    Provide migration guides when necessary
  </Step>

  <Step title="Update Logs">
    Maintain clear change logs
  </Step>
</Steps>

This development workflow ensures consistent, high-quality code development while maintaining the TypeScript-first and MCP-priority approach that defines the J&A Business Solutions project standards.