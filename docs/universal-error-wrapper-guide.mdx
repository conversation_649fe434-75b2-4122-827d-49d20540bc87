---
title: "Universal Error Wrapper Guide"
description: "Comprehensive error handling system with automatic context detection and performance monitoring"
---

# Universal Error Wrapper Guide

The Universal Error Wrapper provides comprehensive error handling capabilities with automatic context detection, performance monitoring integration, and proper Sentry error reporting for the J&A Business Solutions application.

## Overview

The `UniversalErrorWrapper` class is designed to:

<CardGroup cols={2}>
  <Card title="Context Detection" icon="magnifying-glass">
    Automatically detect and enrich error context with browser, page, performance, and user journey information
  </Card>
  <Card title="Performance Integration" icon="gauge">
    Integrate performance monitoring with error tracking for correlation analysis
  </Card>
  <Card title="Retry Logic" icon="rotate">
    Provide retry logic with exponential backoff for transient failures
  </Card>
  <Card title="Error Classification" icon="tags">
    Handle different error types with appropriate classification and reporting
  </Card>
</CardGroup>

## Key Features

### 1. Automatic Context Detection

The wrapper automatically captures:

- **Browser Information**: Name, version, user agent
- **Page Context**: URL, section, component
- **Performance Metrics**: Memory usage, operation duration, render times
- **User Journey**: Current step, previous steps, session duration, interaction count
- **System State**: Database connection status, external service status, feature flags

### 2. Performance Monitoring Integration

- **Operation Timing**: Automatic duration tracking for all wrapped operations
- **Memory Monitoring**: Heap usage correlation with errors
- **API Performance**: Response time correlation with failures
- **Core Web Vitals**: Integration with browser performance metrics

### 3. Retry Logic with Exponential Backoff

- **Configurable Retry Policies**: Max retries, base delay, exponential backoff
- **Conditional Retries**: Custom retry conditions based on error types
- **Retry Tracking**: Breadcrumbs for retry attempts and outcomes

### 4. Error Type Classification

Handles different error types with specific processing:

<Tabs>
  <Tab title="ValidationError">
    Input validation failures
  </Tab>
  <Tab title="NetworkError">
    HTTP requests and connectivity issues
  </Tab>
  <Tab title="DatabaseError">
    Supabase operations and query failures
  </Tab>
  <Tab title="BusinessLogicError">
    Domain-specific rule violations
  </Tab>
  <Tab title="Generic Errors">
    Fallback handling with enhanced context
  </Tab>
</Tabs>

## Basic Usage

### Singleton Instance

```typescript
import { universalErrorWrapper } from '@/lib/UniversalErrorWrapper';

// The wrapper is a singleton, so you get the same instance everywhere
const wrapper = universalErrorWrapper;
```

### Convenience Functions

For simple use cases, use the convenience functions:

```typescript
import { wrapAsync, wrapSync, wrapAsyncWithRetry } from '@/lib/UniversalErrorWrapper';

// Wrap async operations
const result = await wrapAsync('user_data_fetch', async () => {
  return await fetchUserData(userId);
}, {
  entityType: 'user',
  entityId: userId,
});

// Wrap sync operations
const processedData = wrapSync('data_processing', () => {
  return processUserData(rawData);
}, {
  entityType: 'data_processor',
});

// Wrap async operations with retry
const result = await wrapAsyncWithRetry(
  'api_call_with_retry',
  async () => await callExternalAPI(),
  { maxRetries: 3, baseDelay: 1000 },
  { entityType: 'external_api' }
);
```

## Advanced Usage

### Full Configuration

```typescript
import { UniversalErrorWrapper, type OperationConfig, type RetryConfig } from '@/lib/UniversalErrorWrapper';

const wrapper = UniversalErrorWrapper.getInstance();

// Configure operation
const config: OperationConfig = {
  operation: 'complex_business_operation',
  entityType: 'booking',
  entityId: 'booking_123',
  timeout: 30000, // 30 seconds
  customContext: {
    propertyId: 'prop_456',
    guestCount: 4,
    totalPrice: 299.99,
  },
};

// Configure retry policy
const retryConfig: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  exponentialBackoff: true,
  retryCondition: (error) => {
    // Only retry network errors
    return error instanceof NetworkError;
  },
};

// Execute with full configuration
const result = await wrapper.wrapAsyncWithRetry(
  config,
  async () => {
    // Your business logic here
    return await performComplexOperation();
  },
  retryConfig,
  {
    // Additional context
    business: {
      operation: 'booking_creation',
      workflow: 'payment_processing',
    },
    user: {
      id: 'user_123',
      email: '<EMAIL>',
    },
  }
);
```

### User Journey Tracking

```typescript
// Record user journey steps for better debugging context
wrapper.recordUserJourneyStep('landing_page_visit');
wrapper.recordUserJourneyStep('contact_form_opened');
wrapper.recordUserJourneyStep('form_field_filled');
wrapper.recordUserJourneyStep('form_submitted');

// These steps are automatically included in error context
```

## Service Layer Integration

### Enhanced Service Example

```typescript
import { wrapAsync, wrapAsyncWithRetry } from '@/lib/UniversalErrorWrapper';
import { createDatabaseError, BusinessOperation } from '@/lib/errorHandling';

export class BookingService extends DatabaseService {
  async createBooking(bookingData: BookingData): Promise<Booking> {
    return wrapAsync(
      'booking_creation',
      async () => {
        // Validate booking data
        this.validateBookingData(bookingData);

        // Check availability
        await this.checkAvailability(bookingData.propertyId, bookingData.dates);

        // Create booking record
        const result = await this.client
          .from('bookings')
          .insert(bookingData)
          .select()
          .single();

        if (result.error) {
          throw createDatabaseError(
            `Failed to create booking: ${result.error.message}`,
            'insert',
            'bookings'
          );
        }

        return result.data;
      },
      {
        entityType: 'booking',
        entityId: bookingData.propertyId,
        timeout: 15000,
        customContext: {
          propertyId: bookingData.propertyId,
          guestCount: bookingData.guestCount,
          totalPrice: bookingData.totalPrice,
        },
        context: {
          business: {
            operation: BusinessOperation.BOOKING_CREATE,
            entityType: 'booking',
            workflow: 'booking_creation',
          },
          user: {
            id: bookingData.userId,
            email: bookingData.userEmail,
          },
        },
      }
    );
  }

  async syncCalendarWithRetry(propertyId: string): Promise<CalendarData> {
    return wrapAsyncWithRetry(
      'calendar_sync',
      async () => {
        const response = await fetch(`/api/calendar/sync/${propertyId}`);
        if (!response.ok) {
          throw new NetworkError(`Calendar sync failed: ${response.status}`, response.status);
        }
        return response.json();
      },
      {
        maxRetries: 3,
        baseDelay: 2000,
        exponentialBackoff: true,
        retryCondition: (error) => {
          // Retry on network errors and 5xx status codes
          return error instanceof NetworkError && 
                 (!error.statusCode || error.statusCode >= 500);
        },
      },
      {
        entityType: 'calendar',
        entityId: propertyId,
        customContext: { propertyId, syncType: 'full' },
      }
    );
  }
}
```

## React Component Integration

### Component Error Handling

```typescript
import React, { useCallback } from 'react';
import { wrapAsync, universalErrorWrapper } from '@/lib/UniversalErrorWrapper';

export const BookingForm: React.FC = () => {
  const handleSubmit = useCallback(async (formData: BookingFormData) => {
    try {
      await wrapAsync(
        'booking_form_submission',
        async () => {
          // Record user journey
          universalErrorWrapper.recordUserJourneyStep('booking_form_submit');

          // Submit booking
          const booking = await bookingService.createBooking(formData);
          
          // Success handling
          setBookingSuccess(true);
          return booking;
        },
        {
          entityType: 'booking_form',
          entityId: 'submission',
          timeout: 20000,
          customContext: {
            propertyId: formData.propertyId,
            checkInDate: formData.checkIn,
            checkOutDate: formData.checkOut,
          },
          context: {
            business: {
              operation: 'booking_form_submit',
              entityType: 'booking',
              workflow: 'user_booking',
            },
          },
        }
      );
    } catch (error) {
      // Handle different error types for user-friendly messages
      handleFormError(error as Error);
    }
  }, []);

  return (
    // Your form JSX
  );
};
```

## Error Type Handling

### Custom Error Types

The wrapper automatically handles different error types:

```typescript
import { 
  ValidationError, 
  NetworkError, 
  DatabaseError, 
  BusinessLogicError 
} from '@/lib/errorHandling';

// Validation errors
throw new ValidationError('Email is required', 'email', formData.email);

// Network errors
throw new NetworkError('API call failed', 500, '/api/bookings');

// Database errors
throw new DatabaseError('Query failed', 'SELECT', 'bookings');

// Business logic errors
throw new BusinessLogicError(
  'Booking conflict detected',
  'BOOKING_CONFLICT',
  BusinessOperation.BOOKING_CREATE,
  'booking_123',
  'booking'
);
```

### Error Context Enrichment

Each error is automatically enriched with:

```typescript
interface EnhancedErrorContext {
  // Automatic context
  page: { url: string; section: string; component?: string };
  browser: { name: string; version: string; userAgent: string };
  performance?: {
    operationDuration?: number;
    memoryUsage?: number;
    apiResponseTime?: number;
  };
  userJourney?: {
    currentStep: string;
    previousSteps: string[];
    sessionDuration: number;
    interactionCount: number;
  };
  systemState?: {
    databaseConnectionStatus: boolean;
    externalServicesStatus: Record<string, boolean>;
  };
  
  // Manual context
  user?: { id?: string; email?: string; role?: string };
  business?: {
    operation?: string;
    entityType?: string;
    entityId?: string;
    workflow?: string;
  };
  technical?: {
    apiEndpoint?: string;
    statusCode?: number;
    responseTime?: number;
  };
}
```

## Performance Monitoring

### Automatic Performance Tracking

The wrapper automatically tracks:

<CardGroup cols={2}>
  <Card title="Operation Duration" icon="clock">
    How long each operation takes
  </Card>
  <Card title="Memory Usage" icon="memory">
    Memory consumption before and after operations
  </Card>
  <Card title="API Response Times" icon="network-wired">
    Network request performance
  </Card>
  <Card title="Render Times" icon="browser">
    Component rendering performance
  </Card>
</CardGroup>

### Performance Metrics in Sentry

All performance metrics are automatically sent to Sentry:

```typescript
// Automatic span creation
Sentry.startSpan({
  op: 'async.booking',
  name: 'booking_creation',
  attributes: {
    entityType: 'booking',
    entityId: 'booking_123',
    operationDuration: 1250, // ms
    memoryDelta: 1024000, // bytes
  }
});
```

## Configuration Options

### Operation Configuration

```typescript
interface OperationConfig {
  operation: string;              // Operation name for tracking
  entityType?: string;            // Type of entity being operated on
  entityId?: string;              // Specific entity identifier
  timeout?: number;               // Operation timeout in milliseconds
  retryCount?: number;            // Number of retries (deprecated, use RetryConfig)
  retryDelay?: number;            // Retry delay (deprecated, use RetryConfig)
  skipPerformanceMonitoring?: boolean; // Skip performance tracking
  customContext?: Record<string, any>; // Additional context data
}
```

### Retry Configuration

```typescript
interface RetryConfig {
  maxRetries: number;             // Maximum number of retry attempts
  baseDelay: number;              // Base delay between retries (ms)
  maxDelay: number;               // Maximum delay between retries (ms)
  exponentialBackoff: boolean;    // Use exponential backoff
  retryCondition?: (error: Error) => boolean; // Custom retry condition
}
```

## Best Practices

### 1. Use Descriptive Operation Names

<CodeGroup>
```typescript Good
await wrapAsync('user_profile_update', updateUserProfile);
await wrapAsync('booking_availability_check', checkAvailability);
```

```typescript Bad
await wrapAsync('operation', doSomething);
await wrapAsync('api_call', makeRequest);
```
</CodeGroup>

### 2. Provide Meaningful Context

<CodeGroup>
```typescript Good
await wrapAsync('booking_creation', createBooking, {
  entityType: 'booking',
  entityId: bookingData.propertyId,
  customContext: {
    propertyId: bookingData.propertyId,
    guestCount: bookingData.guestCount,
    totalPrice: bookingData.totalPrice,
  },
});
```

```typescript Bad
await wrapAsync('booking_creation', createBooking);
```
</CodeGroup>

### 3. Use Appropriate Retry Policies

```typescript
// For network operations
const networkRetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  exponentialBackoff: true,
  retryCondition: (error) => error instanceof NetworkError,
};

// For database operations
const dbRetryConfig = {
  maxRetries: 2,
  baseDelay: 500,
  exponentialBackoff: false,
  retryCondition: (error) => 
    error.message.includes('connection') || 
    error.message.includes('timeout'),
};
```

### 4. Record User Journey Steps

```typescript
// Record meaningful user journey steps
universalErrorWrapper.recordUserJourneyStep('landing_page_visit');
universalErrorWrapper.recordUserJourneyStep('property_search_initiated');
universalErrorWrapper.recordUserJourneyStep('property_selected');
universalErrorWrapper.recordUserJourneyStep('booking_form_opened');
universalErrorWrapper.recordUserJourneyStep('booking_form_submitted');
```

### 5. Handle Errors Appropriately

```typescript
try {
  await wrapAsync('sensitive_operation', performOperation);
} catch (error) {
  if (error instanceof ValidationError) {
    // Show user-friendly validation message
    setFieldError(error.field, error.message);
  } else if (error instanceof NetworkError) {
    // Show network error message
    showErrorMessage('Network error. Please try again.');
  } else {
    // Show generic error message
    showErrorMessage('An unexpected error occurred.');
  }
}
```

## Testing

### Unit Testing with Mocks

```typescript
import { wrapAsync } from '@/lib/UniversalErrorWrapper';

// Mock the wrapper for testing
jest.mock('@/lib/UniversalErrorWrapper', () => ({
  wrapAsync: jest.fn((operation, fn) => fn()),
  universalErrorWrapper: {
    recordUserJourneyStep: jest.fn(),
  },
}));

describe('BookingService', () => {
  it('should handle booking creation', async () => {
    const mockBookingData = { /* test data */ };
    
    const result = await bookingService.createBooking(mockBookingData);
    
    expect(wrapAsync).toHaveBeenCalledWith(
      'booking_creation',
      expect.any(Function),
      expect.objectContaining({
        entityType: 'booking',
      })
    );
  });
});
```

## Monitoring and Alerting

### Sentry Integration

The wrapper automatically integrates with Sentry for:

<CardGroup cols={2}>
  <Card title="Error Tracking" icon="bug">
    All errors are captured with enhanced context
  </Card>
  <Card title="Performance Monitoring" icon="gauge">
    Operation timing and performance metrics
  </Card>
  <Card title="Breadcrumbs" icon="route">
    User journey and operation breadcrumbs
  </Card>
  <Card title="User Context" icon="user">
    Automatic user and session context
  </Card>
</CardGroup>

### Dashboard Metrics

Monitor these key metrics in Sentry:

- **Error Rate by Operation**: Track error rates for different operations
- **Performance by Entity Type**: Monitor performance across different entity types
- **User Journey Analysis**: Analyze user journey patterns leading to errors
- **Retry Success Rate**: Monitor retry effectiveness
- **Memory Usage Trends**: Track memory usage patterns

## Troubleshooting

<AccordionGroup>
  <Accordion title="High Memory Usage">
    Check for memory leaks in long-running operations
  </Accordion>

  <Accordion title="Timeout Errors">
    Adjust timeout values for slow operations
  </Accordion>

  <Accordion title="Retry Loops">
    Ensure retry conditions are not too broad
  </Accordion>

  <Accordion title="Context Overflow">
    Limit custom context size to prevent payload issues
  </Accordion>
</AccordionGroup>

### Debug Mode

Enable debug logging for troubleshooting:

```typescript
// In development, enable verbose logging
if (process.env.NODE_ENV === 'development') {
  console.log('UniversalErrorWrapper: Operation started', config);
}
```

This comprehensive error handling system ensures that all errors in the J&A Business Solutions application are properly tracked, classified, and reported with rich contextual information for effective debugging and monitoring.