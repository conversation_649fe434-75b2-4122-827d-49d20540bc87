# Production Environment Variables Documentation

This document provides comprehensive documentation for all environment variables required for production deployment of the J&A Business Solutions application.

## Required Environment Variables

### Application Configuration

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `NODE_ENV` | ✅ | `production` | Application environment mode |
| `PORT` | ✅ | `3000` | Port number for the application server |
| `HOSTNAME` | ✅ | `0.0.0.0` | Hostname binding for the server |
| `NEXT_TELEMETRY_DISABLED` | ❌ | `1` | Disable Next.js telemetry in production |
| `NEXT_PUBLIC_APP_ENV` | ❌ | `production` | Public environment identifier |

### Supabase Configuration

| Variable | Required | Description |
|----------|----------|-------------|
| `NEXT_PUBLIC_SUPABASE_URL` | ✅ | Production Supabase project URL |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | ✅ | Production Supabase anonymous key |

**Setup Instructions:**
1. Log into your Supabase dashboard
2. Navigate to your production project
3. Go to Settings > API
4. Copy the Project URL and anon/public key
5. Update the environment variables

### Sentry Configuration (Error Monitoring)

| Variable | Required | Description |
|----------|----------|-------------|
| `NEXT_PUBLIC_SENTRY_DSN` | ✅ | Production Sentry DSN for error tracking |
| `NEXT_PUBLIC_SENTRY_ENVIRONMENT` | ✅ | Environment identifier for Sentry (should be "production") |
| `SENTRY_ORG` | ✅ | Sentry organization slug |
| `SENTRY_PROJECT` | ✅ | Sentry project slug |
| `SENTRY_AUTH_TOKEN` | ✅ | Sentry authentication token for source maps |
| `SENTRY_TRACES_SAMPLE_RATE` | ❌ | Performance monitoring sample rate (0.1 = 10%) |
| `SENTRY_PROFILES_SAMPLE_RATE` | ❌ | Profiling sample rate (0.1 = 10%) |

**Setup Instructions:**
1. Log into your Sentry dashboard
2. Navigate to your production project
3. Go to Settings > Client Keys (DSN)
4. Copy the DSN value
5. Create an authentication token in User Settings > Auth Tokens
6. Update the environment variables

### PostHog Configuration (Analytics)

| Variable | Required | Description |
|----------|----------|-------------|
| `NEXT_PUBLIC_POSTHOG_API_KEY` | ✅ | Production PostHog API key |
| `NEXT_PUBLIC_POSTHOG_API_HOST` | ✅ | PostHog API host URL |
| `POSTHOG_CAPTURE_PAGEVIEW` | ❌ | Enable automatic pageview tracking |
| `POSTHOG_CAPTURE_PAGELEAVE` | ❌ | Enable automatic page leave tracking |

**Setup Instructions:**
1. Log into your PostHog dashboard
2. Navigate to your production project
3. Go to Project Settings
4. Copy the Project API Key
5. Use the appropriate host URL for your region
6. Update the environment variables

### Security Configuration

| Variable | Required | Description |
|----------|----------|-------------|
| `CORS_ORIGIN` | ✅ | Allowed CORS origins for API requests |
| `TRUSTED_HOSTS` | ✅ | Comma-separated list of trusted hostnames |
| `CSP_REPORT_URI` | ❌ | Content Security Policy violation report endpoint |

### Performance Configuration

| Variable | Required | Description |
|----------|----------|-------------|
| `NEXT_PUBLIC_IMAGE_DOMAINS` | ❌ | Allowed domains for Next.js image optimization |
| `CACHE_MAX_AGE` | ❌ | Cache max age in seconds for static assets |

### Logging Configuration

| Variable | Required | Description |
|----------|----------|-------------|
| `LOG_LEVEL` | ❌ | Logging level (error, warn, info, debug) |
| `LOG_FORMAT` | ❌ | Log format (json, text) |
| `ENABLE_REQUEST_LOGGING` | ❌ | Enable HTTP request logging |

### Health Check Configuration

| Variable | Required | Description |
|----------|----------|-------------|
| `HEALTH_CHECK_TIMEOUT` | ❌ | Health check timeout in milliseconds |
| `HEALTH_CHECK_RETRIES` | ❌ | Number of health check retries |

### Database Configuration

| Variable | Required | Description |
|----------|----------|-------------|
| `DB_CONNECTION_TIMEOUT` | ❌ | Database connection timeout in milliseconds |
| `DB_MAX_CONNECTIONS` | ❌ | Maximum number of database connections |
| `DB_IDLE_TIMEOUT` | ❌ | Database connection idle timeout |

### Feature Flags

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `FEATURE_CONTACT_FORM` | ❌ | `true` | Enable contact form functionality |
| `FEATURE_PWA_INSTALL` | ❌ | `true` | Enable PWA installation prompt |
| `FEATURE_ANALYTICS_DEBUG` | ❌ | `false` | Enable analytics debugging (should be false in production) |
| `FEATURE_SENTRY_DEBUG` | ❌ | `false` | Enable Sentry debugging (should be false in production) |

### Container Configuration

| Variable | Required | Description |
|----------|----------|-------------|
| `CONTAINER_NAME` | ❌ | Docker container name |
| `RESTART_POLICY` | ❌ | Docker restart policy |
| `MEMORY_LIMIT` | ❌ | Container memory limit |
| `CPU_LIMIT` | ❌ | Container CPU limit |

## Environment Variable Validation

The application includes built-in validation for required environment variables. If any required variables are missing, the application will:

1. Log detailed error messages indicating which variables are missing
2. Fail to start with a clear error message
3. Provide guidance on how to set the missing variables

## Security Best Practices

1. **Never commit production environment files to version control**
2. **Use secure methods to transfer environment files to production servers**
3. **Regularly rotate API keys and tokens**
4. **Use least-privilege access for all external services**
5. **Monitor environment variable access and usage**

## Deployment Checklist

Before deploying to production, ensure:

- [ ] All required environment variables are set
- [ ] Production API keys and tokens are valid
- [ ] Supabase production database is configured
- [ ] Sentry production project is set up
- [ ] PostHog production project is configured
- [ ] Security headers and CORS are properly configured
- [ ] Performance monitoring is enabled
- [ ] Health checks are configured
- [ ] Logging is set to appropriate levels
- [ ] Feature flags are set for production

## Troubleshooting

### Common Issues

1. **Application fails to start**: Check that all required environment variables are set
2. **Database connection errors**: Verify Supabase URL and API key
3. **Error monitoring not working**: Check Sentry DSN and authentication token
4. **Analytics not tracking**: Verify PostHog API key and host URL
5. **Performance issues**: Check database connection limits and caching configuration

### Validation Commands

```bash
# Validate environment variables
npm run validate:env

# Check health endpoint
curl http://localhost:3000/api/health

# Test database connection
npm run test:db

# Verify Sentry integration
npm run test:sentry
```

## Support

For issues with environment configuration:

1. Check the application logs for specific error messages
2. Verify all required variables are set correctly
3. Test individual service connections (database, Sentry, PostHog)
4. Consult the troubleshooting section above
5. Contact the development team with specific error details