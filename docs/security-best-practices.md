# Security Best Practices for Environment Variables

## Overview

This document outlines security best practices for handling environment variables in the J&A Business Solutions application. Proper environment variable management is critical for maintaining application security and preventing sensitive data exposure.

## Environment Variable Classification

### Public Variables (Client-Side)
These variables are prefixed with `NEXT_PUBLIC_` and are exposed to the client-side code:

- `NEXT_PUBLIC_SUPABASE_URL` - Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Supabase anonymous key (safe for client exposure)
- `NEXT_PUBLIC_SENTRY_DSN` - Sentry Data Source Name
- `NEXT_PUBLIC_SENTRY_ENVIRONMENT` - Environment identifier
- `NEXT_PUBLIC_POSTHOG_API_KEY` - PostHog API key
- `NEXT_PUBLIC_POSTHOG_API_HOST` - PostHog API host

### Server-Only Variables (Sensitive)
These variables must NEVER be exposed to the client:

- `SENTRY_AUTH_TOKEN` - Sentry authentication token
- `SUPABASE_SERVICE_ROLE_KEY` - Supabase service role key
- `DATABASE_URL` - Database connection string
- `CORS_ORIGIN` - CORS configuration
- `TRUSTED_HOSTS` - Trusted host list
- `CSP_REPORT_URI` - Content Security Policy report URI

## Security Implementation

### 1. Environment Variable Validation

The application implements comprehensive validation for all environment variables:

```typescript
import { validateAllEnvironmentVariables } from '@/lib/security/envValidator';

// Validate all environment variables
const result = validateAllEnvironmentVariables();
if (!result.isValid) {
  console.error('Environment validation failed:', result.errors);
}
```

### 2. Runtime Security Checks

Security checks are performed at runtime to prevent sensitive data exposure:

```typescript
import { performRuntimeEnvironmentCheck } from '@/lib/security/envValidator';

// Perform runtime security check
performRuntimeEnvironmentCheck();
```

### 3. Server-Only Configuration Access

Use the secure configuration getter for server-only variables:

```typescript
import { getSecureServerConfig } from '@/lib/env';

// Only call on server-side
if (typeof window === 'undefined') {
  const secureConfig = getSecureServerConfig();
  // Access sensitive configuration
}
```

## Production Security Requirements

### Required Variables
All production deployments must have these variables configured:

- `NODE_ENV=production`
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `NEXT_PUBLIC_SENTRY_DSN`
- `NEXT_PUBLIC_POSTHOG_API_KEY`

### Prohibited Values
The following values are not allowed in production:

- Placeholder values (e.g., `your_key_here`, `replace_with_actual`)
- Development URLs (e.g., `localhost`, `127.0.0.1`)
- Test/demo keys
- Default/example values

### Format Validation
Environment variables are validated against specific patterns:

- **Supabase URL**: Must match `https://[project-id].supabase.co`
- **Sentry DSN**: Must match Sentry DSN format
- **PostHog API Key**: Must start with `phc_`
- **URLs**: Must use HTTPS in production

## Security Headers and Middleware

### Content Security Policy (CSP)
The application implements a strict CSP to prevent XSS attacks:

```typescript
const csp = [
  "default-src 'self'",
  "script-src 'self' 'unsafe-inline' https://app.posthog.com",
  "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
  "connect-src 'self' https://bdzvmmnbtwsfvrqepjuu.supabase.co",
  // ... additional directives
].join('; ');
```

### Security Headers
All responses include security headers:

- `X-Frame-Options: DENY` - Prevent clickjacking
- `X-Content-Type-Options: nosniff` - Prevent MIME sniffing
- `Strict-Transport-Security` - Enforce HTTPS
- `Referrer-Policy: origin-when-cross-origin` - Control referrer info

### Origin Validation
Requests are validated against trusted hosts:

```typescript
const trustedHosts = ['yourdomain.com', 'www.yourdomain.com'];
// Validate request origin against trusted hosts
```

## Development vs Production

### Development Environment
- More permissive CSP for development tools
- Detailed error messages and logging
- Non-fatal validation failures
- Source maps enabled

### Production Environment
- Strict CSP and security headers
- Minimal error messages
- Fatal validation failures
- Source maps disabled
- Debug features disabled

## Environment File Management

### File Structure
```
.env.example          # Template with placeholder values
.env.local           # Local development (not committed)
.env.production      # Production template (sensitive values removed)
```

### Best Practices

1. **Never commit sensitive values** to version control
2. **Use placeholder values** in example files
3. **Document all variables** with descriptions
4. **Validate format** before deployment
5. **Rotate keys regularly** in production

### Example Environment File
```bash
# =============================================================================
# Supabase Configuration
# =============================================================================
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# =============================================================================
# Sentry Configuration
# =============================================================================
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id
NEXT_PUBLIC_SENTRY_ENVIRONMENT=production

# =============================================================================
# PostHog Configuration
# =============================================================================
NEXT_PUBLIC_POSTHOG_API_KEY=phc_your_posthog_api_key
NEXT_PUBLIC_POSTHOG_API_HOST=https://us.i.posthog.com
```

## Monitoring and Alerting

### Security Event Logging
The application logs security events for monitoring:

```typescript
logSecurityEvent({
  type: 'validation_failure',
  message: 'Environment validation failed',
  severity: 'high',
  details: { environment: process.env.NODE_ENV }
});
```

### Sensitive Data Detection
Responses are scanned for accidentally exposed sensitive data:

```typescript
const exposedSecrets = detectSensitiveDataExposure(responseContent);
if (exposedSecrets.length > 0) {
  // Log security violation
}
```

## Deployment Checklist

### Pre-Deployment
- [ ] All required variables are configured
- [ ] No placeholder values in production
- [ ] Environment validation passes
- [ ] Security headers are configured
- [ ] CSP is properly configured

### Post-Deployment
- [ ] Application starts successfully
- [ ] No security warnings in logs
- [ ] Environment validation passes
- [ ] Security headers are present in responses
- [ ] No sensitive data in client-side code

## Troubleshooting

### Common Issues

1. **Missing Environment Variables**
   - Check `.env.production` file exists
   - Verify all required variables are set
   - Check for typos in variable names

2. **Invalid Format**
   - Verify URLs use HTTPS
   - Check API key formats
   - Ensure no trailing spaces

3. **Security Violations**
   - Check for server-only variables on client
   - Verify no placeholder values in production
   - Check CSP violations in browser console

### Debug Commands

```bash
# Validate environment configuration
npm run validate:env

# Check for security issues
npm run security:check

# View sanitized environment variables
npm run env:debug
```

## Emergency Procedures

### Security Breach Response
1. **Immediately rotate** all exposed keys
2. **Update environment variables** in deployment
3. **Redeploy application** with new keys
4. **Monitor logs** for suspicious activity
5. **Document incident** for future prevention

### Key Rotation Schedule
- **Development**: As needed
- **Staging**: Monthly
- **Production**: Quarterly or after security incidents

## Contact Information

For security-related issues or questions:
- **Development Team**: [<EMAIL>]
- **Security Team**: [<EMAIL>]
- **Emergency Contact**: [<EMAIL>]

## References

- [Next.js Environment Variables](https://nextjs.org/docs/basic-features/environment-variables)
- [OWASP Environment Variables](https://owasp.org/www-community/vulnerabilities/Insecure_Configuration_Management)
- [Content Security Policy](https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP)
- [Security Headers](https://securityheaders.com/)