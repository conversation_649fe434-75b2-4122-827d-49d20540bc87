# Production Deployment Guide

This guide covers the complete process for building and deploying the J&A Business Solutions application to production.

## Overview

The application is deployed using Docker containers with a multi-stage build process optimized for production. Since we develop on Mac Silicon M3 but deploy to Linux AMD64 servers, we use <PERSON><PERSON>'s buildx for cross-platform building.

## Prerequisites

### Local Development Machine (Mac Silicon M3)
- Docker Desktop with buildx support
- Node.js 18+ 
- Access to Docker Hub registry (`atemndobs`)
- SSH access to production VPC (`zkm3` alias configured)

### Production Environment
- Linux AMD64 server
- Docker and Docker Compose installed
- Access to `/home/<USER>/docker/jna-business-solutions` directory

## Build Process

### 1. Automated Build Script

Use the provided build script for cross-platform building:

```bash
./scripts/build-and-push.sh
```

This script will:
- Prompt for version selection (auto-increment, current, or custom)
- Build the Docker image for Linux/AMD64 platform
- Push to Docker Hub registry as `atemndobs/jna-amd64:vX.X`
- Tag as `latest`
- Update version tracking

### 2. Manual Build (if needed)

For manual building, use the following command:

```bash
# Build and push specific version
docker buildx build --platform linux/amd64 -t atemndobs/jna-amd64:v0.10 --push .

# Also tag as latest
docker buildx build --platform linux/amd64 -t atemndobs/jna-amd64:latest --push .
```

**Important**: Always use `--platform linux/amd64` when building on Mac Silicon M3 for Linux production servers.

## Deployment Process

### 1. Environment Configuration

Create production environment file:

```bash
cp .env.production.example .env.production
```

Update `.env.production` with actual production values:
- Supabase production URL and keys
- Sentry production DSN and configuration
- PostHog production API key
- Any other production-specific settings

### 2. Update Docker Compose

Update the image version in `docker-compose.prod.yml`:

```yaml
services:
  jna-business-solutions:
    image: atemndobs/jna-amd64:v0.10  # Update this version
```

### 3. Deploy to Production

#### Automated Deployment

Use the deployment script:

```bash
./scripts/deploy-production.sh
```

#### Manual Deployment

1. Connect to production VPC:
```bash
zkm3
```

2. Navigate to application directory:
```bash
cd /home/<USER>/docker/jna-business-solutions
```

3. Copy configuration files:
```bash
# From local machine
scp .env.production atem@production-host:/home/<USER>/docker/jna-business-solutions/
scp docker-compose.prod.yml atem@production-host:/home/<USER>/docker/jna-business-solutions/docker-compose.yml
```

4. Deploy the application:
```bash
# Stop existing containers
docker-compose down --remove-orphans

# Pull latest image
docker-compose pull

# Start new containers
docker-compose up -d
```

5. Verify deployment:
```bash
# Check container status
docker-compose ps

# Check application health
curl -f http://localhost:3000/api/health

# View logs
docker-compose logs -f
```

## Docker Configuration

### Multi-Stage Dockerfile

The Dockerfile uses a multi-stage build process:

1. **deps**: Install production dependencies
2. **builder**: Build the Next.js application with standalone output
3. **runner**: Create minimal production image with non-root user

Key features:
- Uses Node.js 18 Alpine for smaller image size
- Runs as non-root user (`nextjs:nodejs`)
- Includes health checks and proper signal handling
- Optimized for Next.js standalone output

### Docker Compose Configuration

Production Docker Compose includes:
- Health checks with retry logic
- Logging configuration with rotation
- Restart policies
- Network isolation
- Environment variable management

## Version Management

### Current Version Tracking

The build script automatically tracks versions:
- Current version: `v0.9` (as of last deployment)
- Next version: Auto-incremented patch version
- Custom versions: Manual input supported

### Version History

- `v0.9`: Last known production version
- `v0.10`: Next planned version (after current build fixes)

## Monitoring and Health Checks

### Health Check Endpoint

The application includes a health check endpoint at `/api/health` that:
- Verifies application startup
- Checks database connectivity (Supabase)
- Returns JSON status with HTTP 200/500

### Container Health Checks

Docker Compose includes health checks:
- Interval: 30 seconds
- Timeout: 10 seconds
- Retries: 3
- Start period: 40 seconds

### Logging

Logs are configured with:
- JSON file driver
- Maximum size: 10MB per file
- Maximum files: 3 (30MB total)
- Automatic rotation

## Troubleshooting

### Build Issues

1. **Platform mismatch errors**:
   ```bash
   # Ensure buildx is used with correct platform
   docker buildx build --platform linux/amd64 ...
   ```

2. **TypeScript compilation errors**:
   ```bash
   # Run local build first to catch errors
   npm run build
   ```

3. **Docker buildx not available**:
   ```bash
   # Update Docker Desktop or install buildx
   docker buildx version
   ```

### Deployment Issues

1. **Connection to VPC fails**:
   - Verify `zkm3` alias is configured
   - Check SSH key authentication
   - Ensure VPN connection if required

2. **Container fails to start**:
   ```bash
   # Check container logs
   docker-compose logs jna-business-solutions
   
   # Check container status
   docker-compose ps
   ```

3. **Health check fails**:
   ```bash
   # Test health endpoint manually
   curl -v http://localhost:3000/api/health
   
   # Check application logs
   docker-compose logs -f jna-business-solutions
   ```

### Environment Issues

1. **Missing environment variables**:
   - Verify `.env.production` file exists
   - Check all required variables are set
   - Validate Supabase/Sentry/PostHog credentials

2. **Database connection issues**:
   - Verify Supabase URL and keys
   - Check network connectivity from production server
   - Validate database permissions

## Security Considerations

### Environment Variables
- Never commit `.env.production` to version control
- Use strong, unique keys for production
- Rotate API keys regularly

### Container Security
- Application runs as non-root user
- Minimal base image (Alpine Linux)
- No unnecessary packages or tools
- Regular security updates

### Network Security
- Application exposed only on necessary ports
- Use reverse proxy (nginx) for SSL termination
- Implement proper firewall rules

## Performance Optimization

### Build Optimization
- Multi-stage build reduces image size
- Next.js standalone output minimizes dependencies
- Production-only dependencies installed

### Runtime Optimization
- Next.js production optimizations enabled
- Console logs removed in production
- Source maps disabled for security
- Asset compression and caching

## Backup and Recovery

### Container Images
- Previous versions kept in Docker Hub
- Quick rollback by changing image tag
- Automated version tracking

### Data Backup
- Database backups handled by Supabase
- Application state is stateless
- Configuration files backed up in repository

### Rollback Procedure
1. Update `docker-compose.prod.yml` with previous version
2. Deploy using standard process
3. Verify application functionality
4. Monitor for issues

## Next Steps

After completing this deployment:

1. Set up monitoring dashboards (Sentry, PostHog)
2. Configure automated backups
3. Implement CI/CD pipeline
4. Set up SSL certificates and domain configuration
5. Configure log aggregation and alerting