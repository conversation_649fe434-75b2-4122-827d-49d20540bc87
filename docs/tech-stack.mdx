---
title: "Technology Stack"
description: "Complete overview of the J&A Business Solutions technology stack and configuration"
---

# Technology Stack

## Build System & Framework

<CardGroup cols={3}>
  <Card title="Next.js 14+" icon="react">
    React framework with App Router, SSR, and SSG capabilities
  </Card>
  <Card title="React 18" icon="react">
    Frontend framework with TypeScript
  </Card>
  <Card title="TypeScript" icon="code">
    Primary development language (default for all new code)
  </Card>
</CardGroup>

## Database & Backend Services

<CardGroup cols={2}>
  <Card title="Supabase" icon="database">
    PostgreSQL database with real-time capabilities, authentication, and storage
  </Card>
  <Card title="Supabase Client" icon="code">
    JavaScript client for database operations and auth
  </Card>
</CardGroup>

## Monitoring & Analytics

<CardGroup cols={2}>
  <Card title="Sentry" icon="bug">
    Error monitoring, performance tracking, and application monitoring
  </Card>
  <Card title="PostHog" icon="chart-line">
    Product analytics, feature flags, and user behavior tracking
  </Card>
</CardGroup>

## Styling & UI

<CardGroup cols={2}>
  <Card title="shadcn/ui" icon="palette">
    Modern component library built on Radix UI primitives
  </Card>
  <Card title="Tailwind CSS" icon="paintbrush">
    Utility-first CSS framework
  </Card>
  <Card title="Radix UI" icon="component">
    Unstyled, accessible UI primitives (via shadcn/ui)
  </Card>
  <Card title="Lucide React" icon="icons">
    Icon library for consistent iconography
  </Card>
</CardGroup>

### Custom Design System
- **Navy blue and gold color palette**
- Professional and premium service positioning

## PWA & Performance

<CardGroup cols={2}>
  <Card title="Next.js PWA" icon="mobile">
    Progressive Web App capabilities with Next.js
  </Card>
  <Card title="Service Worker" icon="cloud">
    Offline functionality and caching
  </Card>
  <Card title="Image Optimization" icon="image">
    Automatic image optimization and lazy loading
  </Card>
  <Card title="App Router" icon="route">
    Modern routing with layouts and streaming
  </Card>
</CardGroup>

## Testing & Quality Assurance

<CardGroup cols={2}>
  <Card title="Playwright" icon="browser">
    End-to-end testing across multiple browsers
  </Card>
  <Card title="Jest" icon="test-tube">
    Unit testing framework
  </Card>
  <Card title="React Testing Library" icon="react">
    Component testing utilities
  </Card>
  <Card title="ESLint & Prettier" icon="code">
    Code linting and formatting with TypeScript support
  </Card>
</CardGroup>

## CI/CD & Deployment

<CardGroup cols={2}>
  <Card title="GitHub Actions" icon="github">
    Continuous integration and deployment pipeline
  </Card>
  <Card title="Automated Testing" icon="test-tube">
    Unit tests, E2E tests, and linting in CI
  </Card>
  <Card title="Multi-Environment" icon="server">
    Development, staging, and production environments
  </Card>
  <Card title="Vercel" icon="vercel">
    Recommended deployment platform for Next.js applications
  </Card>
</CardGroup>

## Documentation

<CardGroup cols={3}>
  <Card title="Mintlify" icon="book">
    Modern documentation platform with MDX support
  </Card>
  <Card title="MDX" icon="markdown">
    Markdown with JSX components for interactive documentation
  </Card>
  <Card title="Interactive Components" icon="component">
    Enhanced documentation with cards, tabs, and code blocks
  </Card>
</CardGroup>

## MCP (Model Context Protocol) Integration

<Note>
**MCP First Priority** - Always check for MCP tools before using alternatives
</Note>

<CardGroup cols={2}>
  <Card title="Context7 MCP" icon="search">
    Library documentation search and latest API patterns (PRIORITY)
  </Card>
  <Card title="Supabase MCP" icon="database">
    Database operations and schema management
  </Card>
  <Card title="GitHub MCP" icon="github">
    Repository management and CI/CD operations
  </Card>
  <Card title="File System MCP" icon="folder">
    File operations and project structure management
  </Card>
  <Card title="Testing MCP" icon="test-tube">
    Automated testing and quality assurance tools
  </Card>
</CardGroup>

## Common Commands

### Development

<CodeGroup>
```bash Development
npm run dev          # Start Next.js development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run format       # Format code with Prettier
```

```bash Testing
npm run test         # Run unit tests
npm run test:e2e     # Run Playwright end-to-end tests
npm run test:e2e:ui  # Run E2E tests with UI mode
npm run test:headed  # Run E2E tests in headed mode
```

```bash Database & Services
npx supabase start   # Start local Supabase instance
npx supabase stop    # Stop local Supabase instance
npx supabase status  # Check Supabase service status
npx supabase db reset # Reset local database
```
</CodeGroup>

## Key Configuration Files

<AccordionGroup>
  <Accordion title="Core Configuration">
    - `next.config.js` - Next.js configuration with PWA setup
    - `tailwind.config.ts` - Custom colors and design tokens (TypeScript)
    - `tsconfig.json` - TypeScript configuration
    - `components.json` - shadcn/ui component configuration
  </Accordion>

  <Accordion title="Code Quality">
    - `eslint.config.js` - ESLint rules and plugins
    - `prettier.config.js` - Prettier formatting configuration
  </Accordion>

  <Accordion title="Testing">
    - `playwright.config.ts` - Playwright testing configuration
    - `jest.config.js` - Jest testing configuration
  </Accordion>

  <Accordion title="Database & Services">
    - `supabase/config.toml` - Supabase local development configuration
    - `.github/workflows/` - GitHub Actions CI/CD pipeline configuration
  </Accordion>
</AccordionGroup>

## Environment Configuration

### Environment Files
- `.env.local` - Local development environment variables
- `.env.example` - Template for required environment variables
- Environment variables for Supabase, Sentry, and PostHog configuration

### Required Environment Variables

```bash
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Sentry Configuration
VITE_SENTRY_DSN=your_sentry_dsn
VITE_SENTRY_ENVIRONMENT=development

# PostHog Configuration
VITE_POSTHOG_API_KEY=your_posthog_api_key
VITE_POSTHOG_API_HOST=https://app.posthog.com
```

## Performance Optimizations

<CardGroup cols={2}>
  <Card title="Code Splitting" icon="scissors">
    Manual chunk splitting for lucide-react
  </Card>
  <Card title="Dependency Optimization" icon="gear">
    Optimized dependencies configuration
  </Card>
  <Card title="Runtime Caching" icon="clock">
    Runtime caching for Google Fonts and external images
  </Card>
  <Card title="Production Optimization" icon="rocket">
    Source maps disabled in production
  </Card>
</CardGroup>

### Monitoring Integration
- **Sentry performance monitoring** for Core Web Vitals
- **PostHog analytics** with privacy-first configuration

## Development Setup Requirements

### Initial Setup Steps

<Steps>
  <Step title="Check MCP Availability">
    Check for all required tools before manual setup
  </Step>

  <Step title="Install Dependencies">
    ```bash
    git clone <repository-url>
    cd jna_bs-v2
    npm install
    ```
  </Step>

  <Step title="Initialize shadcn/ui">
    ```bash
    npx shadcn-ui@latest init
    ```
  </Step>

  <Step title="Configure Environment">
    ```bash
    cp .env.example .env.local
    # Edit .env.local with your actual values
    ```
  </Step>

  <Step title="Set up Services">
    - Set up Supabase project (use Supabase MCP if available)
    - Configure Sentry project for error monitoring
    - Set up PostHog project for analytics
  </Step>

  <Step title="Install Playwright">
    ```bash
    npx playwright install
    ```
  </Step>

  <Step title="Start Development">
    ```bash
    npm run dev
    ```
  </Step>
</Steps>

### MCP Configuration Priority

<Warning>
- Configure workspace-level MCP servers in `.kiro/settings/mcp.json`
- Use MCP tools for database operations, file management, and testing
- Auto-approve commonly used MCP tools in configuration
- Check MCP availability before installing alternative tools
</Warning>

### Context7 MCP Documentation Rule

<Note>
**ALWAYS use Context7 MCP to search for library documentation before implementation**
</Note>

<CardGroup cols={2}>
  <Card title="Before Implementation" icon="search">
    Before implementing any feature with external libraries, MUST search for latest documentation using Context7 MCP
  </Card>
  <Card title="When Stuck" icon="question">
    When encountering implementation challenges or getting stuck, MUST use Context7 MCP to find relevant documentation
  </Card>
  <Card title="Verify Best Practices" icon="check">
    Use Context7 MCP to verify best practices and current API patterns for libraries
  </Card>
  <Card title="Document Findings" icon="book">
    Document findings from Context7 searches in implementation notes
  </Card>
</CardGroup>

### Testing Setup

<CardGroup cols={2}>
  <Card title="Local Testing" icon="computer">
    Playwright tests run against local development server
  </Card>
  <Card title="E2E Coverage" icon="test-tube">
    E2E tests cover critical user journeys and responsive design
  </Card>
  <Card title="CI/CD Integration" icon="github">
    CI/CD pipeline runs tests automatically on pull requests
  </Card>
  <Card title="Production Monitoring" icon="chart-line">
    Production monitoring through Sentry and PostHog dashboards
  </Card>
</CardGroup>