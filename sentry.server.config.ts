// This file configures the initialization of Sentry on the server side
import * as Sen<PERSON> from "@sentry/nextjs";

// Only initialize if <PERSON><PERSON> is configured
if (process.env.NEXT_PUBLIC_SENTRY_DSN && process.env.NEXT_PUBLIC_SENTRY_DSN !== 'your_sentry_dsn_here') {
  Sentry.init({
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
    environment: process.env.NEXT_PUBLIC_SENTRY_ENVIRONMENT || "development",
    
    // Performance monitoring - reduced for development to avoid rate limits
    tracesSampleRate: process.env.NODE_ENV === "production" ? 0.1 : 0.2,
    
    // Server-side specific configuration
    integrations: [
      // Add server-side integrations here if needed
    ],
    
    // Custom error filtering for server-side
    beforeSend(event) {
      // Filter out development-only errors
      if (process.env.NODE_ENV === "development") {
        // Allow all errors in development
        return event;
      }
      
      // Filter out common non-critical server errors
      if (event.exception?.values?.[0]?.value?.includes("ECONNRESET")) {
        return null;
      }
      
      return event;
    },
    
    // Add rate limiting configuration
    maxBreadcrumbs: process.env.NODE_ENV === "production" ? 50 : 25,
    
    // Throttle transactions in development
    beforeSendTransaction(event) {
      // In development, randomly drop some transactions to avoid rate limits
      if (process.env.NODE_ENV === "development" && Math.random() > 0.3) {
        return null;
      }
      
      return event;
    },
    
    // Set initial scope
    initialScope: {
      tags: {
        component: "nextjs-server",
      },
    },
  });
} else {
  console.warn("Sentry DSN not configured. Server-side error monitoring disabled.");
}