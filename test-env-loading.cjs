#!/usr/bin/env node

/**
 * Test environment loading from .env.production file
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Testing Environment Variable Loading');
console.log('=======================================');

// Load .env.production file manually
const envFile = path.join(__dirname, '.env.production');

if (!fs.existsSync(envFile)) {
  console.error('❌ .env.production file not found!');
  process.exit(1);
}

console.log('✅ .env.production file found');

// Read and parse the file
const envContent = fs.readFileSync(envFile, 'utf8');
const envVars = {};

envContent.split('\n').forEach((line, index) => {
  line = line.trim();
  
  // Skip empty lines and comments
  if (!line || line.startsWith('#')) {
    return;
  }
  
  // Parse KEY=VALUE format
  const equalIndex = line.indexOf('=');
  if (equalIndex === -1) {
    return;
  }
  
  const key = line.substring(0, equalIndex).trim();
  const value = line.substring(equalIndex + 1).trim();
  
  if (key) {
    envVars[key] = value;
    console.log(`📝 Parsed: ${key} = ${value.substring(0, 20)}${value.length > 20 ? '...' : ''}`);
  }
});

console.log(`\n📊 Total variables parsed: ${Object.keys(envVars).length}`);

// Check required variables
const REQUIRED_VARS = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY', 
  'NEXT_PUBLIC_SENTRY_DSN',
  'NEXT_PUBLIC_POSTHOG_API_KEY',
];

console.log('\n🔍 Checking Required Variables:');
console.log('===============================');

const placeholderPatterns = [
  /your_.*_here/i,
  /replace.*with.*actual/i,
  /todo.*replace/i,
  /example/i,
  /test.*key/i,
  /dummy/i,
  /placeholder/i,
];

function isPlaceholderValue(value) {
  return placeholderPatterns.some(pattern => pattern.test(value));
}

let allValid = true;

REQUIRED_VARS.forEach(varName => {
  const value = envVars[varName];
  const exists = !!value;
  const isEmpty = !value || value.trim() === '';
  const isPlaceholder = value ? isPlaceholderValue(value) : false;
  
  console.log(`\n📋 ${varName}:`);
  console.log(`   Exists: ${exists}`);
  console.log(`   Empty: ${isEmpty}`);
  console.log(`   Is Placeholder: ${isPlaceholder}`);
  
  if (value) {
    const masked = value.length > 8 
      ? `${value.substring(0, 4)}...${value.substring(value.length - 4)}`
      : '[REDACTED]';
    console.log(`   Value (masked): ${masked}`);
    console.log(`   Length: ${value.length}`);
    
    // Specific validations
    if (varName.includes('POSTHOG') && value) {
      const startsWithPhc = value.startsWith('phc_');
      console.log(`   Starts with 'phc_': ${startsWithPhc}`);
      if (!startsWithPhc) {
        console.log(`   ❌ PostHog API key should start with 'phc_'`);
        allValid = false;
      }
    }
    
    if (varName.includes('SUPABASE_URL') && value) {
      const isValidUrl = value.includes('.supabase.co');
      console.log(`   Contains '.supabase.co': ${isValidUrl}`);
      if (!isValidUrl) {
        console.log(`   ❌ Supabase URL should contain '.supabase.co'`);
        allValid = false;
      }
    }
    
    if (varName.includes('SENTRY_DSN') && value) {
      const isValidDsn = value.includes('sentry.io');
      console.log(`   Contains 'sentry.io': ${isValidDsn}`);
      if (!isValidDsn) {
        console.log(`   ❌ Sentry DSN should contain 'sentry.io'`);
        allValid = false;
      }
    }
  }
  
  if (!exists || isEmpty) {
    console.log(`   ❌ Missing or empty`);
    allValid = false;
  } else if (isPlaceholder) {
    console.log(`   ❌ Contains placeholder value`);
    allValid = false;
  } else {
    console.log(`   ✅ Valid`);
  }
});

console.log('\n📈 Final Result:');
console.log('================');

if (allValid) {
  console.log('🎉 All environment variables are properly configured!');
  
  // Test if they would work in the Docker build
  console.log('\n🐳 Docker Build Test:');
  console.log('=====================');
  console.log('These variables would be available during Docker build:');
  REQUIRED_VARS.forEach(varName => {
    const value = envVars[varName];
    if (value) {
      console.log(`✅ ${varName}: ${value.substring(0, 10)}...`);
    }
  });
  
} else {
  console.log('❌ Some environment variables have issues that need to be fixed.');
  process.exit(1);
}
