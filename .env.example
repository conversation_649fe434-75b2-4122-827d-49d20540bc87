# =============================================================================
# J&A Business Solutions - Environment Configuration
# =============================================================================
# Copy this file to .env.local and fill in your actual values
# Never commit .env.local to version control

# =============================================================================
# Supabase Configuration
# =============================================================================
# Get these values from your Supabase project dashboard
# Project URL format: https://your-project-id.supabase.co
# For development, you can use the same proptech project or create a separate dev project
VITE_SUPABASE_URL=https://bdzvmmnbtwsfvrqepjuu.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJkenZtbW5idHdzZnZycWVwanV1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NDM1MDAsImV4cCI6MjA2ODQxOTUwMH0.j5Fanbr-kiqPdsCmJf736qP2ZT5LZQwo0p3-fmCyUbc

# =============================================================================
# Sentry Configuration (Error Monitoring)
# =============================================================================
# Get DSN from your Sentry project settings
# Format: https://<EMAIL>/your-project-id
VITE_SENTRY_DSN=your_sentry_dsn_here
VITE_SENTRY_ENVIRONMENT=development

# =============================================================================
# PostHog Configuration (Analytics)
# =============================================================================
# Get API key from your PostHog project settings
VITE_POSTHOG_API_KEY=your_posthog_api_key
VITE_POSTHOG_API_HOST=https://app.posthog.com

# =============================================================================
# Development Configuration
# =============================================================================
# These are automatically set by Next.js but can be overridden
# NODE_ENV=development
# NEXT_PUBLIC_VERCEL_URL=localhost:3000