#!/usr/bin/env node

/**
 * Test PostHog configuration in production
 */

import https from 'https';

// Production PostHog configuration from .env.production
const POSTHOG_API_KEY = 'phc_yErhnRXWMQkP4hO7nU37vDRE72w3KcgyUA5CCXvRdP1';
const POSTHOG_API_HOST = 'https://us.i.posthog.com';

console.log('🧪 Testing PostHog Production Configuration');
console.log('===========================================');
console.log(`API Key: ${POSTHOG_API_KEY.substring(0, 10)}...`);
console.log(`API Host: ${POSTHOG_API_HOST}`);
console.log('');

// Test 1: Check if PostHog API is reachable
async function testPostHogAPI() {
  console.log('1. Testing PostHog API reachability...');
  
  return new Promise((resolve, reject) => {
    const url = new URL('/decide/', POSTHOG_API_HOST);
    
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'JNA-Business-Solutions-Test/1.0'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`   Status: ${res.statusCode}`);
        console.log(`   Response length: ${data.length} bytes`);
        
        if (res.statusCode === 200 || res.statusCode === 400) {
          console.log('   ✅ PostHog API is reachable');
          resolve(true);
        } else {
          console.log('   ❌ PostHog API returned unexpected status');
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.log(`   ❌ PostHog API error: ${error.message}`);
      resolve(false);
    });

    // Send a minimal test payload
    const testPayload = JSON.stringify({
      api_key: POSTHOG_API_KEY,
      distinct_id: 'test-user-' + Date.now()
    });

    req.write(testPayload);
    req.end();
  });
}

// Test 2: Validate API key format
function testAPIKeyFormat() {
  console.log('2. Testing API key format...');
  
  const isValidFormat = /^phc_[a-zA-Z0-9]+$/.test(POSTHOG_API_KEY);
  const isValidLength = POSTHOG_API_KEY.length >= 20;
  
  console.log(`   Format (phc_*): ${isValidFormat ? '✅' : '❌'}`);
  console.log(`   Length (>=20): ${isValidLength ? '✅' : '❌'}`);
  console.log(`   Actual length: ${POSTHOG_API_KEY.length}`);
  
  return isValidFormat && isValidLength;
}

// Test 3: Check API host format
function testAPIHostFormat() {
  console.log('3. Testing API host format...');
  
  try {
    const url = new URL(POSTHOG_API_HOST);
    const isHTTPS = url.protocol === 'https:';
    const isValidHost = url.hostname.includes('posthog.com');
    
    console.log(`   Protocol (HTTPS): ${isHTTPS ? '✅' : '❌'}`);
    console.log(`   Host contains 'posthog.com': ${isValidHost ? '✅' : '❌'}`);
    console.log(`   Full URL: ${url.toString()}`);
    
    return isHTTPS && isValidHost;
  } catch (error) {
    console.log(`   ❌ Invalid URL format: ${error.message}`);
    return false;
  }
}

// Test 4: Test event capture endpoint
async function testEventCapture() {
  console.log('4. Testing event capture endpoint...');
  
  return new Promise((resolve, reject) => {
    const url = new URL('/capture/', POSTHOG_API_HOST);
    
    const testEvent = {
      api_key: POSTHOG_API_KEY,
      event: 'test_event_from_production_test',
      properties: {
        test_timestamp: new Date().toISOString(),
        test_source: 'production_configuration_test',
        environment: 'production'
      },
      distinct_id: 'test-user-' + Date.now(),
      timestamp: new Date().toISOString()
    };

    const postData = JSON.stringify(testEvent);
    
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'User-Agent': 'JNA-Business-Solutions-Test/1.0'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`   Status: ${res.statusCode}`);
        console.log(`   Response: ${data.substring(0, 100)}${data.length > 100 ? '...' : ''}`);
        
        if (res.statusCode === 200) {
          console.log('   ✅ Event capture successful');
          resolve(true);
        } else {
          console.log('   ❌ Event capture failed');
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.log(`   ❌ Event capture error: ${error.message}`);
      resolve(false);
    });

    req.write(postData);
    req.end();
  });
}

// Run all tests
async function runTests() {
  console.log('Starting PostHog production tests...\n');
  
  const results = {
    apiKeyFormat: testAPIKeyFormat(),
    apiHostFormat: testAPIHostFormat(),
    apiReachability: await testPostHogAPI(),
    eventCapture: await testEventCapture()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${test}: ${passed ? '✅ PASS' : '❌ FAIL'}`);
  });
  
  const allPassed = Object.values(results).every(result => result === true);
  
  console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (allPassed) {
    console.log('\n✅ PostHog configuration appears to be working correctly!');
    console.log('   The issue might be in the client-side initialization or consent handling.');
  } else {
    console.log('\n❌ PostHog configuration has issues that need to be resolved.');
  }
  
  return allPassed;
}

// Run the tests
runTests().catch(error => {
  console.error('Test execution failed:', error);
  process.exit(1);
});