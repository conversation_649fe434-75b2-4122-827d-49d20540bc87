import * as Sentry from '@sentry/nextjs';

export async function register() {
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    try {
      // Validate environment configuration first
      const { performRuntimeEnvironmentCheck } = await import('./src/lib/security/envValidator');
      performRuntimeEnvironmentCheck();
      
      // Initialize Sentry server configuration
      await import('./sentry.server.config');
      
      console.log('✅ Server-side instrumentation initialized with security validation');
    } catch (error) {
      console.error('❌ Server instrumentation failed:', error);
      
      // Exit on critical security issues in production
      if (process.env.NODE_ENV === 'production') {
        console.error('🚨 Exiting due to instrumentation failure in production');
        process.exit(1);
      }
    }
  }

  if (process.env.NEXT_RUNTIME === 'edge') {
    try {
      // Initialize Sentry edge configuration
      await import('./sentry.edge.config');
      
      console.log('✅ Edge runtime instrumentation initialized');
    } catch (error) {
      console.error('❌ Edge instrumentation failed:', error);
    }
  }
}

export async function onRequestError(
  err: unknown,
  request: {
    path: string;
    method: string;
    headers: { [key: string]: string | string[] | undefined };
  },
  context: {
    routerKind: 'Pages Router' | 'App Router';
    routePath: string;
    routeType: 'render' | 'route' | 'action' | 'middleware';
  }
) {
  Sentry.captureRequestError(err, request, context);
}